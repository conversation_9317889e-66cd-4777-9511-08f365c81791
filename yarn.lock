# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@algolia/cache-browser-local-storage@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/cache-browser-local-storage@npm:4.24.0"
  dependencies:
    "@algolia/cache-common": 4.24.0
  checksum: f7f9bdb1fa37e788a5cb8c835e526caff2fa097f68736accd4c82ade5e5cb7f5bbd361cf8fc8c2a4628d979d81bd90597bdaed77ca72de8423593067b3d15040
  languageName: node
  linkType: hard

"@algolia/cache-common@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/cache-common@npm:4.24.0"
  checksum: bc1d0f8731713f7e6f10cd397b7d8f7464f14a2f4e1decc73a48e99ecbc0fe41bd4df1cc3eb0a4ecf286095e3eb3935b2ea40179de98e11676f8e7d78c622df8
  languageName: node
  linkType: hard

"@algolia/cache-in-memory@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/cache-in-memory@npm:4.24.0"
  dependencies:
    "@algolia/cache-common": 4.24.0
  checksum: 0476f65f4b622b1b38f050a03b9bf02cf6cc77fc69ec785d16e244770eb2c5eea581b089a346d24bdbc3561be78d383f2a8b81179b801b2af72d9795bc48fee2
  languageName: node
  linkType: hard

"@algolia/client-account@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/client-account@npm:4.24.0"
  dependencies:
    "@algolia/client-common": 4.24.0
    "@algolia/client-search": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 059cf39f3e48b2e77a26435267284d2d15a7a3c4e904feb2b2ad2dd207a3ca2e2b3597847ec9f3b1141749b25fb2e6091e9933f53cb86ab278b5b93836c85aad
  languageName: node
  linkType: hard

"@algolia/client-analytics@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/client-analytics@npm:4.24.0"
  dependencies:
    "@algolia/client-common": 4.24.0
    "@algolia/client-search": 4.24.0
    "@algolia/requester-common": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 17540315bc7ed2ed962fe343129ffe6dcd535cd37d4893765b5b3306a5a2b0a32260d116e77c13541bbc932480b14e24cc640eeecae338bebe7b57bc2cf9cde5
  languageName: node
  linkType: hard

"@algolia/client-common@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/client-common@npm:4.24.0"
  dependencies:
    "@algolia/requester-common": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 19c6615f9e1b0bbda7dd8ecd285c5bdf48d7067223b06e385a6c69a20a6d6500086619fa0f9e63403cf33220d5d7a288360df55452fdf00f5feca8ca9852758a
  languageName: node
  linkType: hard

"@algolia/client-personalization@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/client-personalization@npm:4.24.0"
  dependencies:
    "@algolia/client-common": 4.24.0
    "@algolia/requester-common": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 9c569c6d846f7c9cf3056b83f2c67d9e796b5afa7e7aa55b1e125a2cf5a7342c96d94e7e2005931145698a1d1fc9a56d692f56a5b09fc4a4291bcc83b73addba
  languageName: node
  linkType: hard

"@algolia/client-search@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/client-search@npm:4.24.0"
  dependencies:
    "@algolia/client-common": 4.24.0
    "@algolia/requester-common": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 2d19823994e92490885115188d75994fbcc7a407fbe14f52034b191607a51081ed476e367a65c889666f6b337b00d700203204d55666f182809f01fbd29fd1fb
  languageName: node
  linkType: hard

"@algolia/events@npm:^4.0.1":
  version: 4.0.1
  resolution: "@algolia/events@npm:4.0.1"
  checksum: 4f63943f4554cfcfed91d8b8c009a49dca192b81056d8c75e532796f64828cd69899852013e81ff3fff07030df8782b9b95c19a3da0845786bdfe22af42442c2
  languageName: node
  linkType: hard

"@algolia/logger-common@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/logger-common@npm:4.24.0"
  checksum: 668fb5a2cbb6aaea7648ae522b5d088241589a9da9f8abb53e2daa89ca2d0bc04307291f57c65de7a332e092cc054cc98cc21b12af81620099632ca85c4ef074
  languageName: node
  linkType: hard

"@algolia/logger-console@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/logger-console@npm:4.24.0"
  dependencies:
    "@algolia/logger-common": 4.24.0
  checksum: 846d94ecac2e914a2aa7d1ace301cca7371b2bc757c737405eca8d29fc1a26e788387862851c90f611c90f43755367ce676802a21fa37a3bf8531b1a16f5183b
  languageName: node
  linkType: hard

"@algolia/recommend-core@npm:1.15.0":
  version: 1.15.0
  resolution: "@algolia/recommend-core@npm:1.15.0"
  peerDependencies:
    "@algolia/recommend": ^4.22.1
  checksum: 99cb96c0a4aa70f4caee68fa055da1284ad9708f66549fa62c426f323ef2f57933232b0b1aad1e2e892b5d4290c8c7362ce74b68b48c72911c0404fdc21b8ba2
  languageName: node
  linkType: hard

"@algolia/recommend-react@npm:^1.10.0":
  version: 1.15.0
  resolution: "@algolia/recommend-react@npm:1.15.0"
  dependencies:
    "@algolia/recommend-core": 1.15.0
    "@algolia/recommend-vdom": 1.15.0
    dequal: 2.0.3
  peerDependencies:
    "@algolia/recommend": ^4.22.1
    react: ">= 16.8.0 < 19"
    react-dom: ">= 16.8.0 < 19"
  checksum: d96f650a8f031f57d3d11990488304de9de14b6f99543778447ae39f0922c00f98d1c5b6e92922169c0a780dd6bb5b2a8d6472ffdfaa325aecf05a4d3b0f0ef2
  languageName: node
  linkType: hard

"@algolia/recommend-vdom@npm:1.15.0":
  version: 1.15.0
  resolution: "@algolia/recommend-vdom@npm:1.15.0"
  checksum: f44687fb14f8f8cc87e57174198c2edb178f5af183b44a04b655478f6353e24c567df6ace9cb6c7353229135b42861010f40b8c23c6ab3ce136fc6410d260cf5
  languageName: node
  linkType: hard

"@algolia/recommend@npm:4.24.0, @algolia/recommend@npm:^4.20.0":
  version: 4.24.0
  resolution: "@algolia/recommend@npm:4.24.0"
  dependencies:
    "@algolia/cache-browser-local-storage": 4.24.0
    "@algolia/cache-common": 4.24.0
    "@algolia/cache-in-memory": 4.24.0
    "@algolia/client-common": 4.24.0
    "@algolia/client-search": 4.24.0
    "@algolia/logger-common": 4.24.0
    "@algolia/logger-console": 4.24.0
    "@algolia/requester-browser-xhr": 4.24.0
    "@algolia/requester-common": 4.24.0
    "@algolia/requester-node-http": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 426468452186cbcf0653c3a8c8a4f911def6232dc262f0a310c4583939c6efc5a1c567dbff99b6c99a93f2ba05f9336a60d3fc6c9a74ad2d8d13f4c4fa55d3d8
  languageName: node
  linkType: hard

"@algolia/requester-browser-xhr@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/requester-browser-xhr@npm:4.24.0"
  dependencies:
    "@algolia/requester-common": 4.24.0
  checksum: 7c32d38d6c7a83357f52134f50271f1ee3df63888b28bc53040a3c74ef73458d80efaf44a5943a3769e84737c2ffd0743e1044a3b5e99ce69289f63e22b50f2a
  languageName: node
  linkType: hard

"@algolia/requester-common@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/requester-common@npm:4.24.0"
  checksum: 8f4a49ef0fb4aca42fa3703ddf97ff7f6e9c8492928aa66704ca2f54d3785d2338b64917860a01a42dedb1621279558ca7d549c5b1eb5b7f2742f952fb9865e5
  languageName: node
  linkType: hard

"@algolia/requester-node-http@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/requester-node-http@npm:4.24.0"
  dependencies:
    "@algolia/requester-common": 4.24.0
  checksum: 387ee892bf35f46be269996de88f9ea12841796aa33cb5088ba6460a48733614a33300ee44bca0af22b6fded05c16ec92631fb998e9a7e1e6a30504d8b407c23
  languageName: node
  linkType: hard

"@algolia/transporter@npm:4.24.0":
  version: 4.24.0
  resolution: "@algolia/transporter@npm:4.24.0"
  dependencies:
    "@algolia/cache-common": 4.24.0
    "@algolia/logger-common": 4.24.0
    "@algolia/requester-common": 4.24.0
  checksum: 2c026a777de5dcb6f3cc94a0cf5f4650fbc7067f56eb98a1ae9b5750815179a73eb2b1d8ae75853a99823afd13584b62430d7649c65a456b2623123f355955b1
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/code-frame@npm:7.24.7"
  dependencies:
    "@babel/highlight": ^7.24.7
    picocolors: ^1.0.0
  checksum: 830e62cd38775fdf84d612544251ce773d544a8e63df667728cc9e0126eeef14c6ebda79be0f0bc307e8318316b7f58c27ce86702e0a1f5c321d842eb38ffda4
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/generator@npm:7.25.0"
  dependencies:
    "@babel/types": ^7.25.0
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^2.5.1
  checksum: bf25649dde4068bff8e387319bf820f2cb3b1af7b8c0cfba0bd90880656427c8bad96cd5cb6db7058d20cffe93149ee59da16567018ceaa21ecaefbf780a785c
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/helper-module-imports@npm:7.24.7"
  dependencies:
    "@babel/traverse": ^7.24.7
    "@babel/types": ^7.24.7
  checksum: 8ac15d96d262b8940bc469052a048e06430bba1296369be695fabdf6799f201dd0b00151762b56012a218464e706bc033f27c07f6cec20c6f8f5fd6543c67054
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/helper-string-parser@npm:7.24.8"
  checksum: 39b03c5119216883878655b149148dc4d2e284791e969b19467a9411fccaa33f7a713add98f4db5ed519535f70ad273cdadfd2eb54d47ebbdeac5083351328ce
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-validator-identifier@npm:7.24.7"
  checksum: 6799ab117cefc0ecd35cd0b40ead320c621a298ecac88686a14cffceaac89d80cdb3c178f969861bf5fa5e4f766648f9161ea0752ecfe080d8e89e3147270257
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/highlight@npm:7.24.7"
  dependencies:
    "@babel/helper-validator-identifier": ^7.24.7
    chalk: ^2.4.2
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: 5cd3a89f143671c4ac129960024ba678b669e6fc673ce078030f5175002d1d3d52bc10b22c5b916a6faf644b5028e9a4bd2bb264d053d9b05b6a98690f1d46f1
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.25.0, @babel/parser@npm:^7.25.3":
  version: 7.25.3
  resolution: "@babel/parser@npm:7.25.3"
  dependencies:
    "@babel/types": ^7.25.2
  bin:
    parser: ./bin/babel-parser.js
  checksum: b55aba64214fa1d66ccd0d29f476d2e55a48586920d280f88c546f81cbbececc0e01c9d05a78d6bf206e8438b9c426caa344942c1a581eecc4d365beaab8a20e
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.1.2, @babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.14.0, @babel/runtime@npm:^7.9.2":
  version: 7.25.0
  resolution: "@babel/runtime@npm:7.25.0"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: 4a2a374a58eb01aaa65c5762606e90b3a1f448e0c637d42278b6cc0b42a9f5399b5f381ba9f237ee087da2860d14dd2d1de7bddcbe18be6a3cafba97e44bed64
  languageName: node
  linkType: hard

"@babel/template@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/template@npm:7.25.0"
  dependencies:
    "@babel/code-frame": ^7.24.7
    "@babel/parser": ^7.25.0
    "@babel/types": ^7.25.0
  checksum: 3f2db568718756d0daf2a16927b78f00c425046b654cd30b450006f2e84bdccaf0cbe6dc04994aa1f5f6a4398da2f11f3640a4d3ee31722e43539c4c919c817b
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.24.7":
  version: 7.25.3
  resolution: "@babel/traverse@npm:7.25.3"
  dependencies:
    "@babel/code-frame": ^7.24.7
    "@babel/generator": ^7.25.0
    "@babel/parser": ^7.25.3
    "@babel/template": ^7.25.0
    "@babel/types": ^7.25.2
    debug: ^4.3.1
    globals: ^11.1.0
  checksum: 5661308b1357816f1d4e2813a5dd82c6053617acc08c5c95db051b8b6577d07c4446bc861c9a5e8bf294953ac8266ae13d7d9d856b6b889fc0d34c1f51abbd8c
  languageName: node
  linkType: hard

"@babel/types@npm:^7.24.7, @babel/types@npm:^7.25.0, @babel/types@npm:^7.25.2":
  version: 7.25.2
  resolution: "@babel/types@npm:7.25.2"
  dependencies:
    "@babel/helper-string-parser": ^7.24.8
    "@babel/helper-validator-identifier": ^7.24.7
    to-fast-properties: ^2.0.0
  checksum: f73f66ba903c6f7e38f519a33d53a67d49c07e208e59ea65250362691dc546c6da7ab90ec66ee79651ef697329872f6f97eb19a6dfcacc026fd05e76a563c5d2
  languageName: node
  linkType: hard

"@csstools/css-parser-algorithms@npm:^2.3.1":
  version: 2.7.1
  resolution: "@csstools/css-parser-algorithms@npm:2.7.1"
  peerDependencies:
    "@csstools/css-tokenizer": ^2.4.1
  checksum: 304e6f92e583042c310e368a82b694af563a395e5c55911caefe52765c5acb000b9daa17356ea8a4dd37d4d50132b76de48ced75159b169b53e134ff78b362ba
  languageName: node
  linkType: hard

"@csstools/css-tokenizer@npm:^2.2.0":
  version: 2.4.1
  resolution: "@csstools/css-tokenizer@npm:2.4.1"
  checksum: 395c51f8724ddc4851d836f484346bb3ea6a67af936dde12cbf9a57ae321372e79dee717cbe4823599eb0e6fd2d5405cf8873450e986c2fca6e6ed82e7b10219
  languageName: node
  linkType: hard

"@csstools/media-query-list-parser@npm:^2.1.4":
  version: 2.1.13
  resolution: "@csstools/media-query-list-parser@npm:2.1.13"
  peerDependencies:
    "@csstools/css-parser-algorithms": ^2.7.1
    "@csstools/css-tokenizer": ^2.4.1
  checksum: 7754b4b9fcc749a51a2bcd34a167ad16e7227ff087f6c4e15b3593d3342413446b72dad37f1adb99c62538730c77e3e47842987ce453fbb3849d329a39ba9ad7
  languageName: node
  linkType: hard

"@csstools/selector-specificity@npm:^3.0.0":
  version: 3.1.1
  resolution: "@csstools/selector-specificity@npm:3.1.1"
  peerDependencies:
    postcss-selector-parser: ^6.0.13
  checksum: 3786a6afea97b08ad739ee8f4004f7e0a9e25049cee13af809dbda6462090744012a54bd9275a44712791e8f103f85d21641f14e81799f9dab946b0459a5e1ef
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:1.2.2":
  version: 1.2.2
  resolution: "@emotion/is-prop-valid@npm:1.2.2"
  dependencies:
    "@emotion/memoize": ^0.8.1
  checksum: 61f6b128ea62b9f76b47955057d5d86fcbe2a6989d2cd1e583daac592901a950475a37d049b9f7a7c6aa8758a33b408735db759fdedfd1f629df0f85ab60ea25
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.8.1":
  version: 0.8.1
  resolution: "@emotion/memoize@npm:0.8.1"
  checksum: a19cc01a29fcc97514948eaab4dc34d8272e934466ed87c07f157887406bc318000c69ae6f813a9001c6a225364df04249842a50e692ef7a9873335fbcc141b0
  languageName: node
  linkType: hard

"@emotion/unitless@npm:0.8.1":
  version: 0.8.1
  resolution: "@emotion/unitless@npm:0.8.1"
  checksum: 385e21d184d27853bb350999471f00e1429fa4e83182f46cd2c164985999d9b46d558dc8b9cc89975cb337831ce50c31ac2f33b15502e85c299892e67e7b4a88
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: ^3.3.0
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: cdfe3ae42b4f572cbfb46d20edafe6f36fc5fb52bf2d90875c58aefe226892b9677fef60820e2832caf864a326fe4fc225714c46e8389ccca04d5f9288aabd22
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.5.1, @eslint-community/regexpp@npm:^4.6.1":
  version: 4.11.0
  resolution: "@eslint-community/regexpp@npm:4.11.0"
  checksum: 97d2fe46690b69417a551bd19a3dc53b6d9590d2295c43cc4c4e44e64131af541e2f4a44d5c12e87de990403654d3dae9d33600081f3a2f0386b368abc9111ec
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/eslintrc@npm:2.1.4"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^9.6.0
    globals: ^13.19.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 10957c7592b20ca0089262d8c2a8accbad14b4f6507e35416c32ee6b4dbf9cad67dfb77096bbd405405e9ada2b107f3797fe94362e1c55e0b09d6e90dd149127
  languageName: node
  linkType: hard

"@eslint/js@npm:8.57.0":
  version: 8.57.0
  resolution: "@eslint/js@npm:8.57.0"
  checksum: 315dc65b0e9893e2bff139bddace7ea601ad77ed47b4550e73da8c9c2d2766c7a575c3cddf17ef85b8fd6a36ff34f91729d0dcca56e73ca887c10df91a41b0bb
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.6.0":
  version: 1.6.5
  resolution: "@floating-ui/core@npm:1.6.5"
  dependencies:
    "@floating-ui/utils": ^0.2.5
  checksum: 8e6c62a6e9223fba9afbcaca8afe408788a2bc8ab1b2f5734a26d5b02d4017a2baffc7176a938a610fd243e6a983ada605f259b35c88813e2230dd29906a78fd
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.6.1":
  version: 1.6.8
  resolution: "@floating-ui/dom@npm:1.6.8"
  dependencies:
    "@floating-ui/core": ^1.6.0
    "@floating-ui/utils": ^0.2.5
  checksum: bab6954bdde69afeaf8dbbf335818fe710c6eae1c62856ae1e09fa6abdc056bf5995e053638b76fa6661b8384c363ca2af874ab0448c3f6943808f4f8f77f3ea
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.5":
  version: 0.2.5
  resolution: "@floating-ui/utils@npm:0.2.5"
  checksum: 32834fe0fec5ee89187f8defd0b10813d725dab7dc6ed1545ded6655630bac5d438f0c991d019d675585e118846f12391236fc2886a5c73a57576e7de3eca3f9
  languageName: node
  linkType: hard

"@gar/promisify@npm:^1.0.1, @gar/promisify@npm:^1.1.3":
  version: 1.1.3
  resolution: "@gar/promisify@npm:1.1.3"
  checksum: 4059f790e2d07bf3c3ff3e0fec0daa8144fe35c1f6e0111c9921bd32106adaa97a4ab096ad7dab1e28ee6a9060083c4d1a4ada42a7f5f3f7a96b8812e2b757c1
  languageName: node
  linkType: hard

"@google-cloud/common@npm:^5.0.0":
  version: 5.0.2
  resolution: "@google-cloud/common@npm:5.0.2"
  dependencies:
    "@google-cloud/projectify": ^4.0.0
    "@google-cloud/promisify": ^4.0.0
    arrify: ^2.0.1
    duplexify: ^4.1.1
    extend: ^3.0.2
    google-auth-library: ^9.0.0
    html-entities: ^2.5.2
    retry-request: ^7.0.0
    teeny-request: ^9.0.0
  checksum: 13c3af95830c1410edb52b9a1bb8cbaf1b47e63be6049eae9c06b728225fd59f6acce1d8cdba575c14a2bb7e929acf9320bf8aec3f67409d920143a90a69dc53
  languageName: node
  linkType: hard

"@google-cloud/projectify@npm:^4.0.0":
  version: 4.0.0
  resolution: "@google-cloud/projectify@npm:4.0.0"
  checksum: 973d28414ae200433333a3c315aebb881ced42ea4afe6f3f8520d2fecded75e76c913f5189fea8fb29ce6ca36117c4f44001b3c503eecdd3ac7f02597a98354a
  languageName: node
  linkType: hard

"@google-cloud/promisify@npm:^4.0.0":
  version: 4.0.0
  resolution: "@google-cloud/promisify@npm:4.0.0"
  checksum: edd189398c5ed5b7b64a373177d77c87d076a248c31b8ae878bb91e2411d89860108bcb948c349f32628973a823bd131beb53ec008fd613a8cb466ef1d89de49
  languageName: node
  linkType: hard

"@google-cloud/translate@npm:^8.0.2":
  version: 8.3.0
  resolution: "@google-cloud/translate@npm:8.3.0"
  dependencies:
    "@google-cloud/common": ^5.0.0
    "@google-cloud/promisify": ^4.0.0
    arrify: ^2.0.0
    extend: ^3.0.2
    google-gax: ^4.0.3
    is-html: ^2.0.0
  checksum: d8174ddb67aec9cb3511f4596888f6d83de85c9ed539e3928725e25e68ab518ba71e0e48cf886b251ac3d57425e296add0461ab9ee279b19fcd7dc9bb7905288
  languageName: node
  linkType: hard

"@googlemaps/js-api-loader@npm:1.16.2":
  version: 1.16.2
  resolution: "@googlemaps/js-api-loader@npm:1.16.2"
  dependencies:
    fast-deep-equal: ^3.1.3
  checksum: 122ee57cc6a936e50358aedc4e79042353590467210e901fb42d4e7d9123c248c4db5cd5407a350cb5bea6de9e9ef1a508c08ba593475285d90bc4b93db42f51
  languageName: node
  linkType: hard

"@googlemaps/markerclusterer@npm:2.5.3":
  version: 2.5.3
  resolution: "@googlemaps/markerclusterer@npm:2.5.3"
  dependencies:
    fast-deep-equal: ^3.1.3
    supercluster: ^8.0.1
  checksum: aa74e9b59d302a0c7444c48818f017532172973dece223c9a3f9b5cdb8aeba7ea3dd87ee785420972d24e7738937e76373e8aae8a0cf10f045bf9869d1b6b9ee
  languageName: node
  linkType: hard

"@grpc/grpc-js@npm:^1.10.9":
  version: 1.11.1
  resolution: "@grpc/grpc-js@npm:1.11.1"
  dependencies:
    "@grpc/proto-loader": ^0.7.13
    "@js-sdsl/ordered-map": ^4.4.2
  checksum: 906894851a13b09f5a95052e1bec572b1b5760cc53635e80b50155c7546591cc8b8768d2723e2ec2ff5d7b086f65a27809db345fd3038fd50e3fc5e827ee88c6
  languageName: node
  linkType: hard

"@grpc/proto-loader@npm:^0.7.13":
  version: 0.7.13
  resolution: "@grpc/proto-loader@npm:0.7.13"
  dependencies:
    lodash.camelcase: ^4.3.0
    long: ^5.0.0
    protobufjs: ^7.2.5
    yargs: ^17.7.2
  bin:
    proto-loader-gen-types: build/bin/proto-loader-gen-types.js
  checksum: 399c1b8a4627f93dc31660d9636ea6bf58be5675cc7581e3df56a249369e5be02c6cd0d642c5332b0d5673bc8621619bc06fb045aa3e8f57383737b5d35930dc
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.11.14":
  version: 0.11.14
  resolution: "@humanwhocodes/config-array@npm:0.11.14"
  dependencies:
    "@humanwhocodes/object-schema": ^2.0.2
    debug: ^4.3.1
    minimatch: ^3.0.5
  checksum: 861ccce9eaea5de19546653bccf75bf09fe878bc39c3aab00aeee2d2a0e654516adad38dd1098aab5e3af0145bbcbf3f309bdf4d964f8dab9dcd5834ae4c02f2
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^2.0.2":
  version: 2.0.3
  resolution: "@humanwhocodes/object-schema@npm:2.0.3"
  checksum: d3b78f6c5831888c6ecc899df0d03bcc25d46f3ad26a11d7ea52944dc36a35ef543fad965322174238d677a43d5c694434f6607532cff7077062513ad7022631
  languageName: node
  linkType: hard

"@inquirer/figures@npm:^1.0.3":
  version: 1.0.5
  resolution: "@inquirer/figures@npm:1.0.5"
  checksum: 01dc7b95fe7b030b0577d59f45c4fa5c002dccb43ac75ff106d7142825e09dee63a6f9c42b044da2bc964bf38c40229a112a26505a68f3912b15dc8304106bbc
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.5
  resolution: "@jridgewell/gen-mapping@npm:0.3.5"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: ff7a1764ebd76a5e129c8890aa3e2f46045109dabde62b0b6c6a250152227647178ff2069ea234753a690d8f3c4ac8b5e7b267bbee272bffb7f3b0a370ab6e52
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@js-sdsl/ordered-map@npm:^4.4.2":
  version: 4.4.2
  resolution: "@js-sdsl/ordered-map@npm:4.4.2"
  checksum: a927ae4ff8565ecb75355cc6886a4f8fadbf2af1268143c96c0cce3ba01261d241c3f4ba77f21f3f017a00f91dfe9e0673e95f830255945c80a0e96c6d30508a
  languageName: node
  linkType: hard

"@next/env@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/env@npm:14.2.3"
  checksum: 47ddb64ec6cdc13dfcf560ba42cce71d7948174bf800162738e20ba0147cc46a5f6fdde1eb7957a3676a9eca6dccf6603836ed7c755eab238d9f5c73614d9880
  languageName: node
  linkType: hard

"@next/env@npm:^13.4.19":
  version: 13.5.6
  resolution: "@next/env@npm:13.5.6"
  checksum: 5e8f3f6f987a15dad3cd7b2bcac64a6382c2ec372d95d0ce6ab295eb59c9731222017eebf71ff3005932de2571f7543bce7e5c6a8c90030207fb819404138dc2
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-darwin-arm64@npm:14.2.3"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-darwin-x64@npm:14.2.3"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-linux-arm64-gnu@npm:14.2.3"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-linux-arm64-musl@npm:14.2.3"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-linux-x64-gnu@npm:14.2.3"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-linux-x64-musl@npm:14.2.3"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-win32-arm64-msvc@npm:14.2.3"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-ia32-msvc@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-win32-ia32-msvc@npm:14.2.3"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:14.2.3":
  version: 14.2.3
  resolution: "@next/swc-win32-x64-msvc@npm:14.2.3"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^2.0.0":
  version: 2.2.2
  resolution: "@npmcli/agent@npm:2.2.2"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: 67de7b88cc627a79743c88bab35e023e23daf13831a8aa4e15f998b92f5507b644d8ffc3788afc8e64423c612e0785a6a92b74782ce368f49a6746084b50d874
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^1.0.0":
  version: 1.1.1
  resolution: "@npmcli/fs@npm:1.1.1"
  dependencies:
    "@gar/promisify": ^1.0.1
    semver: ^7.3.5
  checksum: f5ad92f157ed222e4e31c352333d0901df02c7c04311e42a81d8eb555d4ec4276ea9c635011757de20cc476755af33e91622838de573b17e52e2e7703f0a9965
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^2.1.0":
  version: 2.1.2
  resolution: "@npmcli/fs@npm:2.1.2"
  dependencies:
    "@gar/promisify": ^1.1.3
    semver: ^7.3.5
  checksum: 405074965e72d4c9d728931b64d2d38e6ea12066d4fad651ac253d175e413c06fe4350970c783db0d749181da8fe49c42d3880bd1cbc12cd68e3a7964d820225
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.1
  resolution: "@npmcli/fs@npm:3.1.1"
  dependencies:
    semver: ^7.3.5
  checksum: d960cab4b93adcb31ce223bfb75c5714edbd55747342efb67dcc2f25e023d930a7af6ece3e75f2f459b6f38fc14d031c766f116cd124fdc937fd33112579e820
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^1.0.1":
  version: 1.1.2
  resolution: "@npmcli/move-file@npm:1.1.2"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: c96381d4a37448ea280951e46233f7e541058cf57a57d4094dd4bdcaae43fa5872b5f2eb6bfb004591a68e29c5877abe3cdc210cb3588cbf20ab2877f31a7de7
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^2.0.0":
  version: 2.0.1
  resolution: "@npmcli/move-file@npm:2.0.1"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: 52dc02259d98da517fae4cb3a0a3850227bdae4939dda1980b788a7670636ca2b4a01b58df03dd5f65c1e3cb70c50fa8ce5762b582b3f499ec30ee5ce1fd9380
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@popperjs/core@npm:^2.9.0":
  version: 2.11.8
  resolution: "@popperjs/core@npm:2.11.8"
  checksum: e5c69fdebf52a4012f6a1f14817ca8e9599cb1be73dd1387e1785e2ed5e5f0862ff817f420a87c7fc532add1f88a12e25aeb010ffcbdc98eace3d55ce2139cf0
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 011fe7ef0826b0fd1a95935a033a3c0fd08483903e1aa8f8b4e0704e3233406abb9ee25350ec0c20bbecb2aad8da0dcea58b392bbd77d6690736f02c143865d2
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 67173ac34de1e242c55da52c2f5bdc65505d82453893f9b51dc74af9fe4c065cf4a657a4538e91b0d4a1a1e0a0642215e31894c31650ff6e3831471061e1ee9e
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 59240c850b1d3d0b56d8f8098dd04787dcaec5c5bd8de186fa548de86b86076e1c50e80144b90335e705a044edf5bc8b0998548474c2a10a98c7e004a1547e4b
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 0369163a3d226851682f855f81413cbf166cd98f131edb94a0f67f79e75342d86e89df9d7a1df08ac28be2bc77e0a7f0200526bb6c2a407abbfee1f0262d5fd7
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": ^1.1.1
    "@protobufjs/inquire": ^1.1.0
  checksum: 3fce7e09eb3f1171dd55a192066450f65324fd5f7cc01a431df01bb00d0a895e6bfb5b0c5561ce157ee1d886349c90703d10a4e11a1a256418ff591b969b3477
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 5781e1241270b8bd1591d324ca9e3a3128d2f768077a446187a049e36505e91bc4156ed5ac3159c3ce3d2ba3743dbc757b051b2d723eea9cd367bfd54ab29b2f
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: ca06f02eaf65ca36fb7498fc3492b7fc087bfcc85c702bac5b86fad34b692bdce4990e0ef444c1e2aea8c034227bd1f0484be02810d5d7e931c55445555646f4
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 856eeb532b16a7aac071cacde5c5620df800db4c80cee6dbc56380524736205aae21e5ae47739114bf669ab5e8ba0e767a282ad894f3b5e124197cb9224445ee
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: d6a34fbbd24f729e2a10ee915b74e1d77d52214de626b921b2d77288bd8f2386808da2315080f2905761527cceffe7ec34c7647bd21a5ae41a25e8212ff79451
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: f9bf3163d13aaa3b6f5e6fbf37a116e094ea021c0e1f2a7ccd0e12a29e2ce08dafba4e8b36e13f8ed7397e1591610ce880ed1289af4d66cf4ace8a36a9557278
  languageName: node
  linkType: hard

"@react-google-maps/api@npm:^2.19.2":
  version: 2.19.3
  resolution: "@react-google-maps/api@npm:2.19.3"
  dependencies:
    "@googlemaps/js-api-loader": 1.16.2
    "@googlemaps/markerclusterer": 2.5.3
    "@react-google-maps/infobox": 2.19.2
    "@react-google-maps/marker-clusterer": 2.19.2
    "@types/google.maps": 3.55.2
    invariant: 2.2.4
  peerDependencies:
    react: ^16.8 || ^17 || ^18
    react-dom: ^16.8 || ^17 || ^18
  checksum: fd12a3ee4ee2c987dff07f791396b57b16c60dcffc53cd151afe2c6a0cb3f1fc1c21d341871b8b0b362cfc6a17541d42ff5e6f5960e6795bc05314bd81b10250
  languageName: node
  linkType: hard

"@react-google-maps/infobox@npm:2.19.2":
  version: 2.19.2
  resolution: "@react-google-maps/infobox@npm:2.19.2"
  checksum: e2022d857931492d0fd457b72fa729076da1e3e0c5281a402a71127e35d683cb7ca4074e2888682a612ca4b5d2b0d6abc26cce94aa9a315abeaa61b4f6239f24
  languageName: node
  linkType: hard

"@react-google-maps/marker-clusterer@npm:2.19.2":
  version: 2.19.2
  resolution: "@react-google-maps/marker-clusterer@npm:2.19.2"
  checksum: fde23240ae153ec8aeddd1d702f1e2a8ee51412fe3b5079a88d4e1697f8c580e743afe6bb815214b218cefddb1a2097a9d982440c2207906dd1dd2a88b8068cb
  languageName: node
  linkType: hard

"@reduxjs/toolkit@npm:^1.9.6":
  version: 1.9.7
  resolution: "@reduxjs/toolkit@npm:1.9.7"
  dependencies:
    immer: ^9.0.21
    redux: ^4.2.1
    redux-thunk: ^2.4.2
    reselect: ^4.1.8
  peerDependencies:
    react: ^16.9.0 || ^17.0.0 || ^18
    react-redux: ^7.2.1 || ^8.0.2
  peerDependenciesMeta:
    react:
      optional: true
    react-redux:
      optional: true
  checksum: ac25dec73a5d2df9fc7fbe98c14ccc73919e5ee1d6f251db0d2ec8f90273f92ef39c26716704bf56b5a40189f72d94b4526dc3a8c7ac3986f5daf44442bcc364
  languageName: node
  linkType: hard

"@remirror/core-constants@npm:3.0.0":
  version: 3.0.0
  resolution: "@remirror/core-constants@npm:3.0.0"
  checksum: a944632b0f9152bff134cf7411f4eef176f0d5295401e283ea1966c21833a0c5e6d7e387a076d916fd4696dabb81ae4a7d626a29cb52d8378f52e43d68c6c699
  languageName: node
  linkType: hard

"@rollup/plugin-commonjs@npm:^25.0.4":
  version: 25.0.8
  resolution: "@rollup/plugin-commonjs@npm:25.0.8"
  dependencies:
    "@rollup/pluginutils": ^5.0.1
    commondir: ^1.0.1
    estree-walker: ^2.0.2
    glob: ^8.0.3
    is-reference: 1.2.1
    magic-string: ^0.30.3
  peerDependencies:
    rollup: ^2.68.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: dd105ee5625fbcaf832c0cf80be0aaf6a86bbd8fe99ff911f9ac4b78c79f26e9e99442b5aa0cc1136b5ddf89ec0b6c5728e5341ac04d687aef1b53063670b395
  languageName: node
  linkType: hard

"@rollup/plugin-node-resolve@npm:^15.2.0":
  version: 15.2.3
  resolution: "@rollup/plugin-node-resolve@npm:15.2.3"
  dependencies:
    "@rollup/pluginutils": ^5.0.1
    "@types/resolve": 1.20.2
    deepmerge: ^4.2.2
    is-builtin-module: ^3.2.1
    is-module: ^1.0.0
    resolve: ^1.22.1
  peerDependencies:
    rollup: ^2.78.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 730f32c2f8fdddff07cf0fca86a5dac7c475605fb96930197a868c066e62eb6388c557545e4f7d99b7a283411754c9fbf98944ab086b6074e04fc1292e234aa8
  languageName: node
  linkType: hard

"@rollup/plugin-typescript@npm:^11.1.2":
  version: 11.1.6
  resolution: "@rollup/plugin-typescript@npm:11.1.6"
  dependencies:
    "@rollup/pluginutils": ^5.1.0
    resolve: ^1.22.1
  peerDependencies:
    rollup: ^2.14.0||^3.0.0||^4.0.0
    tslib: "*"
    typescript: ">=3.7.0"
  peerDependenciesMeta:
    rollup:
      optional: true
    tslib:
      optional: true
  checksum: 3f5b981f4d9c9501be1f16396f7b6d4ae584cb1b61e9f0bed66f98245fb77f249caea2b9b5f222f933b46fd9043c1f2664a7445aefa386c1ffbb4f0b80fc6004
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^4.1.2":
  version: 4.2.1
  resolution: "@rollup/pluginutils@npm:4.2.1"
  dependencies:
    estree-walker: ^2.0.1
    picomatch: ^2.2.2
  checksum: 6bc41f22b1a0f1efec3043899e4d3b6b1497b3dea4d94292d8f83b4cf07a1073ecbaedd562a22d11913ff7659f459677b01b09e9598a98936e746780ecc93a12
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.0.1, @rollup/pluginutils@npm:^5.1.0":
  version: 5.1.0
  resolution: "@rollup/pluginutils@npm:5.1.0"
  dependencies:
    "@types/estree": ^1.0.0
    estree-walker: ^2.0.2
    picomatch: ^2.3.1
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 3cc5a6d91452a6eabbfd1ae79b4dd1f1e809d2eecda6e175deb784e75b0911f47e9ecce73f8dd315d6a8b3f362582c91d3c0f66908b6ced69345b3cbe28f8ce8
  languageName: node
  linkType: hard

"@statsig/client-core@npm:1.6.0, @statsig/client-core@npm:^1.4.0":
  version: 1.6.0
  resolution: "@statsig/client-core@npm:1.6.0"
  checksum: 2c0f882bb7649b48d0b2ceb744475da2b7edcabe97df8d055df745bfb29f9876564020eb660360935a6ea0cd04316f936547c537d98376ed6d5e2adbb68b7e5c
  languageName: node
  linkType: hard

"@statsig/js-client@npm:^1.4.0":
  version: 1.6.0
  resolution: "@statsig/js-client@npm:1.6.0"
  dependencies:
    "@statsig/client-core": 1.6.0
  checksum: c9b104bfabd6eb467b3ffd121b1b4eea0fbbface85b699007db3f72cbb9c844fdc70975c1ae5e6ab315fc3de2e71b5ae21da4c70d69c912c680d18dfbac54f53
  languageName: node
  linkType: hard

"@statsig/react-bindings@npm:^1.4.0":
  version: 1.6.0
  resolution: "@statsig/react-bindings@npm:1.6.0"
  dependencies:
    "@statsig/client-core": 1.6.0
  peerDependencies:
    react: ^16.6.3 || ^17.0.0 || ^18.0.0
  checksum: fe983d475395875d3a268739a253e0c07489975e1938123fa0adc3ebf7379fde29e0f2671efc260f8139e24784391870124d7a972ac957c78f81d1d1f8898bee
  languageName: node
  linkType: hard

"@storybook/csf@npm:^0.0.1":
  version: 0.0.1
  resolution: "@storybook/csf@npm:0.0.1"
  dependencies:
    lodash: ^4.17.15
  checksum: fb57fa028b08a51edf44e1a2bf4be40a4607f5c6ccb58aae8924f476a42b9bbd61a0ad521cfc82196f23e6a912caae0a615e70a755e6800b284c91c509fd2de6
  languageName: node
  linkType: hard

"@swc/counter@npm:^0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.5":
  version: 0.5.5
  resolution: "@swc/helpers@npm:0.5.5"
  dependencies:
    "@swc/counter": ^0.1.3
    tslib: ^2.4.0
  checksum: d4f207b191e54b29460804ddf2984ba6ece1d679a0b2f6a9c765dcf27bba92c5769e7965668a4546fb9f1021eaf0ff9be4bf5c235ce12adcd65acdfe77187d11
  languageName: node
  linkType: hard

"@tiptap/core@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/core@npm:2.9.1"
  peerDependencies:
    "@tiptap/pm": ^2.7.0
  checksum: a70674915579fb408879e4ba506b449e81132087a1e8ed6259063908747265945a0d269ba8bbcf52173fdcb300294ff1fd1150a34ccc45fef058aa4344c13cb3
  languageName: node
  linkType: hard

"@tiptap/extension-blockquote@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-blockquote@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 60cbc27f82087953490850e53f0d806f786317418e2011bad607b5d7d1c9f6253fd46dbfc20ca3ad0c5a7b5f903f02f8a9a7b48dd8610bdc4d68efac725093b6
  languageName: node
  linkType: hard

"@tiptap/extension-bold@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-bold@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: e0301921946cf5b9a549460f725eb237c48e53b9952ed0233b98805169dc29669de5b258e56721f528a4c2ecc489798d88586c1439ec16952b3f79d6145b7abb
  languageName: node
  linkType: hard

"@tiptap/extension-bubble-menu@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-bubble-menu@npm:2.9.1"
  dependencies:
    tippy.js: ^6.3.7
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: aae560bb6b7f798da84f15ce54b527c71441c5d46136adf0baa375c68b024ca9a887b11e271428707551102fa12da4c642c52a446eae412dd5e655d89c29b207
  languageName: node
  linkType: hard

"@tiptap/extension-bullet-list@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-bullet-list@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 2248dff18f7f0c61aad4fa0e63fbb7301e64fb1354c668a295cb06ed962f7d03cdb84e7a889c99febf02b65f93f6053968b5db55d01a7f2be99ebb203fc8a950
  languageName: node
  linkType: hard

"@tiptap/extension-code-block@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-code-block@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: 49021aa4669d1022eb62150d900345a988f304843da8b9111ed32f0143abdd4d543d22e5a8260209292d36d9e296dc8175f15c1704661209009bfe7e0f6f7162
  languageName: node
  linkType: hard

"@tiptap/extension-code@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-code@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 8f68a289eb913746fe4b43f251db47d2dfb2ea6446a32fbd0a44b57e544589830dd40f38b9852a751d57064fe68eeef9224ecc5a55b8920f3f9f8fd4bc4af8de
  languageName: node
  linkType: hard

"@tiptap/extension-document@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-document@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 0eeb70d17a910fed6966758f06f017a019060d04aa1420998e2f5f4e0894ff21267923fb5a6e79d5de1fcc14b0457ea68ccacb24b68a76f094dfad763d3b99aa
  languageName: node
  linkType: hard

"@tiptap/extension-dropcursor@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-dropcursor@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: f58aedb61e55daeb16fd10a8a1587c5a803d5c508aa56debb07f6ce96b8ccac9d4a17f7587feac214a874660644ce906f26ed25d67971d22c6bbb0e0372b535c
  languageName: node
  linkType: hard

"@tiptap/extension-floating-menu@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-floating-menu@npm:2.9.1"
  dependencies:
    tippy.js: ^6.3.7
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: e3649d1d7155ea408f045e684d53240d5c68685f61eb85c6fb343992b3e87b6f97409504493f5475b6a2e209f0ff7180fe9dd9f5eb57caecdd6027c36ccec9bd
  languageName: node
  linkType: hard

"@tiptap/extension-gapcursor@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-gapcursor@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: 03206857291f8235ced8038c8b1cd62f4f081df358a44365cd3c65f8170b526788ae8eed9777470b00362ad88c0412fa2a895c57354ec76dc95eb6a7f1a86cde
  languageName: node
  linkType: hard

"@tiptap/extension-hard-break@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-hard-break@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 04a519ae1fc9ee6a1dfd844489595e85a153ef8f2567b6f53d9274617c6d31444cfceaeb698e7c7b20a9198bdf8c1577e573ceb417d737525e4720e405b3be14
  languageName: node
  linkType: hard

"@tiptap/extension-heading@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-heading@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 277065577347c8e51398b0c39cd11d1f2356ab45d5eeb09f900a2dc535cd90546ef925743a296b3f0a8332e3f5c73dc73808f39b27e33cb21ed2b4c81915887e
  languageName: node
  linkType: hard

"@tiptap/extension-history@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-history@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: fe0e878947d1816ced045e33f62304f51c3d1ec969ef35ad25804b214d9882dc78168925911046d96a5704e5b125e04986aaf201355572ff9b0b8571c7e26afe
  languageName: node
  linkType: hard

"@tiptap/extension-horizontal-rule@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-horizontal-rule@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
  checksum: cea7d7f8bdc69f6395d4a5fc6ba250e10a358f75cc3e314eb357ad8e1fe3b0879f41808cb711d045940737991772042195bb80364568b7e2dcea40bf41426b60
  languageName: node
  linkType: hard

"@tiptap/extension-italic@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-italic@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 0c8bff1afdf46e5abb6c44a58ed69ed915ce83e2a70456580189e6a5a74706ff59380c09e0458103cfab4dcc1b426f71e4ab60955141c1c5fd5fbd04358b8b3c
  languageName: node
  linkType: hard

"@tiptap/extension-list-item@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-list-item@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 40d8527851034558092d54187b938bc8f421450c510030cc4c067ca22d9f2d3513fddc5455a94fca41cb31069ffdd26eccfa1feaba3e1a4f734cd51768e260f3
  languageName: node
  linkType: hard

"@tiptap/extension-ordered-list@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-ordered-list@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 89dea38712f72eb6143803cce190aa97a907c0d1080e117a92f7b71e0caba5280e415d4676a1560710e01cc6d930d1abd90a350c710970c2c29b3181f07f4418
  languageName: node
  linkType: hard

"@tiptap/extension-paragraph@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-paragraph@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 3f598bc523695ad92c8f341843ead19e943c7fa7cb706393963b3029c991f4e40d80b209564a5029f2c623c06ed4229b1da90185c2ebdc6aab446170d265e0b7
  languageName: node
  linkType: hard

"@tiptap/extension-strike@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-strike@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 6af38f5632bdc5c0d78758f4a23d4857287f42cf35cdd4f8d51b2d39b3d6f28a2529a9d58f031d57d6781f69387383488058d43f87e2af6829ba1b9ad5af1836
  languageName: node
  linkType: hard

"@tiptap/extension-text-style@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-text-style@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 555a7bbfb880a69a6399b39394ba04f625a224a1b716353d1bc27383ee3b0ec331d431e8f9e9d6730c8cd0f74cb4d3a92730236636e96345762ea6d358c098c2
  languageName: node
  linkType: hard

"@tiptap/extension-text@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/extension-text@npm:2.9.1"
  peerDependencies:
    "@tiptap/core": ^2.7.0
  checksum: 63ed5179027a50e22422fd826d68f0556d0707ae2c71cd453fea61b7e8c16ae631b031bf7cba613ee47adacec3bbf02205c807c7e3a2a684104336c167b8afa8
  languageName: node
  linkType: hard

"@tiptap/pm@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/pm@npm:2.9.1"
  dependencies:
    prosemirror-changeset: ^2.2.1
    prosemirror-collab: ^1.3.1
    prosemirror-commands: ^1.6.0
    prosemirror-dropcursor: ^1.8.1
    prosemirror-gapcursor: ^1.3.2
    prosemirror-history: ^1.4.1
    prosemirror-inputrules: ^1.4.0
    prosemirror-keymap: ^1.2.2
    prosemirror-markdown: ^1.13.0
    prosemirror-menu: ^1.2.4
    prosemirror-model: ^1.22.3
    prosemirror-schema-basic: ^1.2.3
    prosemirror-schema-list: ^1.4.1
    prosemirror-state: ^1.4.3
    prosemirror-tables: ^1.4.0
    prosemirror-trailing-node: ^3.0.0
    prosemirror-transform: ^1.10.0
    prosemirror-view: ^1.34.3
  checksum: 707cebcfc8a3ef1a473f45aa037f40b1147cb63541472fa986692e42675878e82d09a57d6dd7549d82e45809062581423a1add012606f5570b4229b7d2116881
  languageName: node
  linkType: hard

"@tiptap/react@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/react@npm:2.9.1"
  dependencies:
    "@tiptap/extension-bubble-menu": ^2.9.1
    "@tiptap/extension-floating-menu": ^2.9.1
    "@types/use-sync-external-store": ^0.0.6
    fast-deep-equal: ^3
    use-sync-external-store: ^1.2.2
  peerDependencies:
    "@tiptap/core": ^2.7.0
    "@tiptap/pm": ^2.7.0
    react: ^17.0.0 || ^18.0.0
    react-dom: ^17.0.0 || ^18.0.0
  checksum: ff93c37dbb0e48369a0ff6a61d3a5fe422a4f78227174411d0683e6e13c25b9b2db3cb6cf099788a4c666b2f7e77f40e478ae4c1382dc96e13e367a259d0868b
  languageName: node
  linkType: hard

"@tiptap/starter-kit@npm:^2.9.1":
  version: 2.9.1
  resolution: "@tiptap/starter-kit@npm:2.9.1"
  dependencies:
    "@tiptap/core": ^2.9.1
    "@tiptap/extension-blockquote": ^2.9.1
    "@tiptap/extension-bold": ^2.9.1
    "@tiptap/extension-bullet-list": ^2.9.1
    "@tiptap/extension-code": ^2.9.1
    "@tiptap/extension-code-block": ^2.9.1
    "@tiptap/extension-document": ^2.9.1
    "@tiptap/extension-dropcursor": ^2.9.1
    "@tiptap/extension-gapcursor": ^2.9.1
    "@tiptap/extension-hard-break": ^2.9.1
    "@tiptap/extension-heading": ^2.9.1
    "@tiptap/extension-history": ^2.9.1
    "@tiptap/extension-horizontal-rule": ^2.9.1
    "@tiptap/extension-italic": ^2.9.1
    "@tiptap/extension-list-item": ^2.9.1
    "@tiptap/extension-ordered-list": ^2.9.1
    "@tiptap/extension-paragraph": ^2.9.1
    "@tiptap/extension-strike": ^2.9.1
    "@tiptap/extension-text": ^2.9.1
    "@tiptap/extension-text-style": ^2.9.1
    "@tiptap/pm": ^2.9.1
  checksum: e73183a1f243999b086ec93c0dee843f25538d2d34e78c3cdff4a7d516e4df87453145c1f19594c14c59c8ed6c265ae8ab91b05e19d9144d5fa82c860e25c6bc
  languageName: node
  linkType: hard

"@tootallnate/once@npm:1":
  version: 1.1.2
  resolution: "@tootallnate/once@npm:1.1.2"
  checksum: e1fb1bbbc12089a0cb9433dc290f97bddd062deadb6178ce9bcb93bb7c1aecde5e60184bc7065aec42fe1663622a213493c48bbd4972d931aae48315f18e1be9
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 11226c39b52b391719a2a92e10183e4260d9651f86edced166da1d95f39a0a1eaa470e44d14ac685ccd6d3df7e2002433782872c0feeb260d61e80f21250e65c
  languageName: node
  linkType: hard

"@types/caseless@npm:*":
  version: 0.12.5
  resolution: "@types/caseless@npm:0.12.5"
  checksum: f6a3628add76d27005495914c9c3873a93536957edaa5b69c63b46fe10b4649a6fecf16b676c1695f46aab851da47ec6047dcf3570fa8d9b6883492ff6d074e0
  languageName: node
  linkType: hard

"@types/cssnano@npm:^5.0.0":
  version: 5.0.0
  resolution: "@types/cssnano@npm:5.0.0"
  dependencies:
    postcss: ^8
  checksum: 7d05401db3c419a0bdab9a2889e5a8568dca33b9fedb4cb0ba883d13d76f436cc99f0b25317630cd10d4f1700577a70b76e83a37684589e3541a05aa3b31662f
  languageName: node
  linkType: hard

"@types/d3-geo@npm:^3.1.0":
  version: 3.1.0
  resolution: "@types/d3-geo@npm:3.1.0"
  dependencies:
    "@types/geojson": "*"
  checksum: a4b2daa8a64012912ce7186891e8554af123925dca344c111b771e168a37477e02d504c6c94ee698440380e8c4f3f373d6755be97935da30eae0904f6745ce40
  languageName: node
  linkType: hard

"@types/dom-speech-recognition@npm:^0.0.1":
  version: 0.0.1
  resolution: "@types/dom-speech-recognition@npm:0.0.1"
  checksum: 8b34af73c311580fa17842d72025a6d9d3eb0768f03e8eac91d2699566800efb6e99cba8138c0b44f16a00822ba8a6da00f830aff504cbf996bc86bbe4834fc2
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree@npm:1.0.5"
  checksum: dd8b5bed28e6213b7acd0fb665a84e693554d850b0df423ac8076cc3ad5823a6bc26b0251d080bdc545af83179ede51dd3f6fa78cad2c46ed1f29624ddf3e41a
  languageName: node
  linkType: hard

"@types/events@npm:*":
  version: 3.0.3
  resolution: "@types/events@npm:3.0.3"
  checksum: 50af9312fab001fd6bd4bb3ff65830f940877e6778de140a92481a0d9bf5f4853d44ec758a8800ef60e0598ac43ed1b5688116a3c65906ae54e989278d6c7c82
  languageName: node
  linkType: hard

"@types/geojson@npm:*":
  version: 7946.0.14
  resolution: "@types/geojson@npm:7946.0.14"
  checksum: ae511bee6488ae3bd5a3a3347aedb0371e997b14225b8983679284e22fa4ebd88627c6e3ff8b08bf4cc35068cb29310c89427311ffc9322c255615821a922e71
  languageName: node
  linkType: hard

"@types/google.maps@npm:3.55.2":
  version: 3.55.2
  resolution: "@types/google.maps@npm:3.55.2"
  checksum: c0b554fddb0a59ddbe7cfb1ef8bc4aeacd9660b726546ec75fca62619680572338ceb3bcc5c63b035c87e6cc91532fa78f7f0d1aa2b5be4ac7fdebcffb396b39
  languageName: node
  linkType: hard

"@types/google.maps@npm:^3.55.12":
  version: 3.58.1
  resolution: "@types/google.maps@npm:3.58.1"
  checksum: 7ad5bd9566ffa0396485c432368e45c43e3fe1ecc2b89324f257a49d9abbe03dfe046a771d82ae1808fa0fb6e04e6ffca870c7f2295fef73a6015a678b067364
  languageName: node
  linkType: hard

"@types/hogan.js@npm:^3.0.0":
  version: 3.0.5
  resolution: "@types/hogan.js@npm:3.0.5"
  checksum: a2cc95b1a94bd321aa2fe0303005703a7e801cf463ee7b3ab5e2fae101ef426ace87bf9554bb995c8d3c60c2612b657d765d20d96faae3af03bd0e3a55357aba
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/linkify-it@npm:^5":
  version: 5.0.0
  resolution: "@types/linkify-it@npm:5.0.0"
  checksum: ec98e03aa883f70153a17a1e6ed9e28b39a604049b485daeddae3a1482ec65cac0817520be6e301d99fd1a934b3950cf0f855655aae6ec27da2bb676ba4a148e
  languageName: node
  linkType: hard

"@types/long@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/long@npm:4.0.2"
  checksum: d16cde7240d834cf44ba1eaec49e78ae3180e724cd667052b194a372f350d024cba8dd3f37b0864931683dab09ca935d52f0c4c1687178af5ada9fc85b0635f4
  languageName: node
  linkType: hard

"@types/markdown-it@npm:^14.0.0":
  version: 14.1.2
  resolution: "@types/markdown-it@npm:14.1.2"
  dependencies:
    "@types/linkify-it": ^5
    "@types/mdurl": ^2
  checksum: ad66e0b377d6af09a155bb65f675d1e2cb27d20a3d407377fe4508eb29cde1e765430b99d5129f89012e2524abb5525d629f7057a59ff9fd0967e1ff645b9ec6
  languageName: node
  linkType: hard

"@types/mdurl@npm:^2":
  version: 2.0.0
  resolution: "@types/mdurl@npm:2.0.0"
  checksum: 78746e96c655ceed63db06382da466fd52c7e9dc54d60b12973dfdd110cae06b9439c4b90e17bb8d4461109184b3ea9f3e9f96b3e4bf4aa9fe18b6ac35f283c8
  languageName: node
  linkType: hard

"@types/minimist@npm:^1.2.0, @types/minimist@npm:^1.2.2":
  version: 1.2.5
  resolution: "@types/minimist@npm:1.2.5"
  checksum: 477047b606005058ab0263c4f58097136268007f320003c348794f74adedc3166ffc47c80ec3e94687787f2ab7f4e72c468223946e79892cf0fd9e25e9970a90
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=13.7.0":
  version: 22.1.0
  resolution: "@types/node@npm:22.1.0"
  dependencies:
    undici-types: ~6.13.0
  checksum: 3544c35da06009790a2e07742a7dfa0ac0f0d64ec47d9e6d3edf0ff6dcfc1a7cc2efdc5e524e80f8ed80aa37154513b2c1c724f95146ff89fc5aefb8e33575f2
  languageName: node
  linkType: hard

"@types/node@npm:20.3.2":
  version: 20.3.2
  resolution: "@types/node@npm:20.3.2"
  checksum: 5929ce2b9b12b1e2a2304a0921a953c72a81f5753ad39ac43b99ce6312fbb2b4fb5bc6b60d64a2550704e3223cd5de1299467d36085ac69888899db978f2653a
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.4
  resolution: "@types/normalize-package-data@npm:2.4.4"
  checksum: 65dff72b543997b7be8b0265eca7ace0e34b75c3e5fee31de11179d08fa7124a7a5587265d53d0409532ecb7f7fba662c2012807963e1f9b059653ec2c83ee05
  languageName: node
  linkType: hard

"@types/papaparse@npm:^5.3.9":
  version: 5.3.14
  resolution: "@types/papaparse@npm:5.3.14"
  dependencies:
    "@types/node": "*"
  checksum: fbf942ed92179eeb824d4e544cc701468157a4ce3f6f668f8b17692d9886fea92ccff5e56965615ff64f049efa01ff95ddb7d30c67e0186bc802a6cc8ef26e63
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: 5bf62eec37c332ad10059252fc0dab7e7da730764869c980b0714777ad3d065e490627be9f40fc52f238ffa3ac4199b19de4127196910576c2fe34dd47c7a470
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.12
  resolution: "@types/prop-types@npm:15.7.12"
  checksum: ac16cc3d0a84431ffa5cfdf89579ad1e2269549f32ce0c769321fdd078f84db4fbe1b461ed5a1a496caf09e637c0e367d600c541435716a55b1d9713f5035dfe
  languageName: node
  linkType: hard

"@types/qs@npm:^6.5.3":
  version: 6.9.15
  resolution: "@types/qs@npm:6.9.15"
  checksum: 97d8208c2b82013b618e7a9fc14df6bd40a73e1385ac479b6896bafc7949a46201c15f42afd06e86a05e914f146f495f606b6fb65610cc60cf2e0ff743ec38a2
  languageName: node
  linkType: hard

"@types/raf@npm:^3.4.0":
  version: 3.4.3
  resolution: "@types/raf@npm:3.4.3"
  checksum: 70b0d8ce4ed1fdd60abbee8ff2a572bd2947bd764691f98ef948748375f5012db7ee39a037dd063cfbbb52c0b7479bec68111bbb95ce5de023ec581794c9b85f
  languageName: node
  linkType: hard

"@types/react-dom@npm:18.2.6":
  version: 18.2.6
  resolution: "@types/react-dom@npm:18.2.6"
  dependencies:
    "@types/react": "*"
  checksum: b56e42efab121a3a8013d2eb8c1688e6028a25ea6d33c4362d2846f0af3760b164b4d7c34846614024cfb8956cca70dd1743487f152e32ff89a00fe6fbd2be54
  languageName: node
  linkType: hard

"@types/react@npm:*":
  version: 18.3.3
  resolution: "@types/react@npm:18.3.3"
  dependencies:
    "@types/prop-types": "*"
    csstype: ^3.0.2
  checksum: c63d6a78163244e2022b01ef79b0baec4fe4da3475dc4a90bb8accefad35ef0c43560fd0312e5974f92a0f1108aa4d669ac72d73d66396aa060ea03b5d2e3873
  languageName: node
  linkType: hard

"@types/react@npm:18.2.14":
  version: 18.2.14
  resolution: "@types/react@npm:18.2.14"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: a6a5e8cc78f486b9020d1ad009aa6c56943c68c7c6376e0f8399e9cbcd950b7b8f5d73f00200f5379f5e58d31d57d8aed24357f301d8e86108cd438ce6c8b3dd
  languageName: node
  linkType: hard

"@types/request@npm:^2.48.8":
  version: 2.48.12
  resolution: "@types/request@npm:2.48.12"
  dependencies:
    "@types/caseless": "*"
    "@types/node": "*"
    "@types/tough-cookie": "*"
    form-data: ^2.5.0
  checksum: 20dfad0a46b4249bf42f09c51fbd4d02ec6738c5152194b5c7c69bab80b00eae9cc71df4489ffa929d0968d453ef7d0823d1f98871efed563a4fdb57bf0a4c58
  languageName: node
  linkType: hard

"@types/resolve@npm:1.20.2":
  version: 1.20.2
  resolution: "@types/resolve@npm:1.20.2"
  checksum: 61c2cad2499ffc8eab36e3b773945d337d848d3ac6b7b0a87c805ba814bc838ef2f262fc0f109bfd8d2e0898ff8bd80ad1025f9ff64f1f71d3d4294c9f14e5f6
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.23.0
  resolution: "@types/scheduler@npm:0.23.0"
  checksum: 874d753aa65c17760dfc460a91e6df24009bde37bfd427a031577b30262f7770c1b8f71a21366c7dbc76111967384cf4090a31d65315155180ef14bd7acccb32
  languageName: node
  linkType: hard

"@types/semver@npm:^7.3.12, @types/semver@npm:^7.5.0":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: ea6f5276f5b84c55921785a3a27a3cd37afee0111dfe2bcb3e03c31819c197c782598f17f0b150a69d453c9584cd14c4c4d7b9a55d2c5e6cacd4d66fdb3b3663
  languageName: node
  linkType: hard

"@types/stylis@npm:4.2.5":
  version: 4.2.5
  resolution: "@types/stylis@npm:4.2.5"
  checksum: 24f91719db5569979e9e2f197e050ef82e1fd72474e8dc45bca38d48ee56481eae0f0d4a7ac172540d7774b45a2a78d901a4c6d07bba77a33dbccff464ea3edf
  languageName: node
  linkType: hard

"@types/topojson-client@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/topojson-client@npm:3.1.4"
  dependencies:
    "@types/geojson": "*"
    "@types/topojson-specification": "*"
  checksum: ee35870aa81ebc01113d42457f64b98847bcb65ab6fb050d70c6a5d7383a229b68b0de0f4116dd169861915db0e8fdad8e3e529376150ae7ccf0ff0cf666323c
  languageName: node
  linkType: hard

"@types/topojson-specification@npm:*":
  version: 1.0.5
  resolution: "@types/topojson-specification@npm:1.0.5"
  dependencies:
    "@types/geojson": "*"
  checksum: 8c879e48317e805a0eeebfa54fcb5418e477c4e2e5712a23de1d0e038b0ab6afe806a91fc40452cd3bc931de6a141b53920c7f443d767aadf408dde757b1807c
  languageName: node
  linkType: hard

"@types/tough-cookie@npm:*":
  version: 4.0.5
  resolution: "@types/tough-cookie@npm:4.0.5"
  checksum: f19409d0190b179331586365912920d192733112a195e870c7f18d20ac8adb7ad0b0ff69dad430dba8bc2be09593453a719cfea92dc3bda19748fd158fe1498d
  languageName: node
  linkType: hard

"@types/use-sync-external-store@npm:^0.0.3":
  version: 0.0.3
  resolution: "@types/use-sync-external-store@npm:0.0.3"
  checksum: 161ddb8eec5dbe7279ac971531217e9af6b99f7783213566d2b502e2e2378ea19cf5e5ea4595039d730aa79d3d35c6567d48599f69773a02ffcff1776ec2a44e
  languageName: node
  linkType: hard

"@types/use-sync-external-store@npm:^0.0.6":
  version: 0.0.6
  resolution: "@types/use-sync-external-store@npm:0.0.6"
  checksum: a95ce330668501ad9b1c5b7f2b14872ad201e552a0e567787b8f1588b22c7040c7c3d80f142cbb9f92d13c4ea41c46af57a20f2af4edf27f224d352abcfe4049
  languageName: node
  linkType: hard

"@types/yt-player@npm:^3.5.1":
  version: 3.5.4
  resolution: "@types/yt-player@npm:3.5.4"
  dependencies:
    "@types/events": "*"
  checksum: 2b8ea25bdb77c0cbc9698d818b5c0435e7479021a2c86d8b582adb635642c9ed93d321e2fb27ba00fa3e003fa8f8e26133d7f61022b551bc985c690b0fa0f9c8
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.4.1":
  version: 6.21.0
  resolution: "@typescript-eslint/eslint-plugin@npm:6.21.0"
  dependencies:
    "@eslint-community/regexpp": ^4.5.1
    "@typescript-eslint/scope-manager": 6.21.0
    "@typescript-eslint/type-utils": 6.21.0
    "@typescript-eslint/utils": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
    debug: ^4.3.4
    graphemer: ^1.4.0
    ignore: ^5.2.4
    natural-compare: ^1.4.0
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 5ef2c502255e643e98051e87eb682c2a257e87afd8ec3b9f6274277615e1c2caf3131b352244cfb1987b8b2c415645eeacb9113fa841fc4c9b2ac46e8aed6efd
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^6.7.4":
  version: 6.21.0
  resolution: "@typescript-eslint/parser@npm:6.21.0"
  dependencies:
    "@typescript-eslint/scope-manager": 6.21.0
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/typescript-estree": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 162fe3a867eeeffda7328bce32dae45b52283c68c8cb23258fb9f44971f761991af61f71b8c9fe1aa389e93dfe6386f8509c1273d870736c507d76dd40647b68
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/scope-manager@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
  checksum: 6062d6b797fe1ce4d275bb0d17204c827494af59b5eaf09d8a78cdd39dadddb31074dded4297aaf5d0f839016d601032857698b0e4516c86a41207de606e9573
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/scope-manager@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
  checksum: 71028b757da9694528c4c3294a96cc80bc7d396e383a405eab3bc224cda7341b88e0fc292120b35d3f31f47beac69f7083196c70616434072fbcd3d3e62d3376
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/type-utils@npm:6.21.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 6.21.0
    "@typescript-eslint/utils": 6.21.0
    debug: ^4.3.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 77025473f4d80acf1fafcce99c5c283e557686a61861febeba9c9913331f8a41e930bf5cd8b7a54db502a57b6eb8ea6d155cbd4f41349ed00e3d7aeb1f477ddc
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/types@npm:5.62.0"
  checksum: 48c87117383d1864766486f24de34086155532b070f6264e09d0e6139449270f8a9559cfef3c56d16e3bcfb52d83d42105d61b36743626399c7c2b5e0ac3b670
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/types@npm:6.21.0"
  checksum: 9501b47d7403417af95fc1fb72b2038c5ac46feac0e1598a46bcb43e56a606c387e9dcd8a2a0abe174c91b509f2d2a8078b093786219eb9a01ab2fbf9ee7b684
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/typescript-estree@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    semver: ^7.3.7
    tsutils: ^3.21.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3624520abb5807ed8f57b1197e61c7b1ed770c56dfcaca66372d584ff50175225798bccb701f7ef129d62c5989070e1ee3a0aa2d84e56d9524dcf011a2bb1a52
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/typescript-estree@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    minimatch: 9.0.3
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dec02dc107c4a541e14fb0c96148f3764b92117c3b635db3a577b5a56fc48df7a556fa853fb82b07c0663b4bf2c484c9f245c28ba3e17e5cb0918ea4cab2ea21
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/utils@npm:6.21.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@types/json-schema": ^7.0.12
    "@types/semver": ^7.5.0
    "@typescript-eslint/scope-manager": 6.21.0
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/typescript-estree": 6.21.0
    semver: ^7.5.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: b129b3a4aebec8468259f4589985cb59ea808afbfdb9c54f02fad11e17d185e2bf72bb332f7c36ec3c09b31f18fc41368678b076323e6e019d06f74ee93f7bf2
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:^5.45.0":
  version: 5.62.0
  resolution: "@typescript-eslint/utils@npm:5.62.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@types/json-schema": ^7.0.9
    "@types/semver": ^7.3.12
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/typescript-estree": 5.62.0
    eslint-scope: ^5.1.1
    semver: ^7.3.7
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: ee9398c8c5db6d1da09463ca7bf36ed134361e20131ea354b2da16a5fdb6df9ba70c62a388d19f6eebb421af1786dbbd79ba95ddd6ab287324fc171c3e28d931
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/visitor-keys@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    eslint-visitor-keys: ^3.3.0
  checksum: 976b05d103fe8335bef5c93ad3f76d781e3ce50329c0243ee0f00c0fcfb186c81df50e64bfdd34970148113f8ade90887f53e3c4938183afba830b4ba8e30a35
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/visitor-keys@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": 6.21.0
    eslint-visitor-keys: ^3.4.1
  checksum: 67c7e6003d5af042d8703d11538fca9d76899f0119130b373402819ae43f0bc90d18656aa7add25a24427ccf1a0efd0804157ba83b0d4e145f06107d7d1b7433
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.2.0":
  version: 1.2.0
  resolution: "@ungap/structured-clone@npm:1.2.0"
  checksum: 4f656b7b4672f2ce6e272f2427d8b0824ed11546a601d8d5412b9d7704e83db38a8d9f402ecdf2b9063fc164af842ad0ec4a55819f621ed7e7ea4d1efcc74524
  languageName: node
  linkType: hard

"@vercel/speed-insights@npm:^1.0.11":
  version: 1.0.12
  resolution: "@vercel/speed-insights@npm:1.0.12"
  peerDependencies:
    "@sveltejs/kit": ^1 || ^2
    next: ">= 13"
    react: ^18 || ^19
    svelte: ^4
    vue: ^3
    vue-router: ^4
  peerDependenciesMeta:
    "@sveltejs/kit":
      optional: true
    next:
      optional: true
    react:
      optional: true
    svelte:
      optional: true
    vue:
      optional: true
    vue-router:
      optional: true
  checksum: df108b6ddf5b58ecf6b3b0463f46bad98ec11fac1bfbc11d4fa7221218ca030d34726fdc36332edb99a2419940222aa7ea4749498debbe8742a6afb2ebb9f153
  languageName: node
  linkType: hard

"@wokaylabs/tiptap-react-render@npm:^0.0.1":
  version: 0.0.1
  resolution: "@wokaylabs/tiptap-react-render@npm:0.0.1"
  checksum: a1dd52b2a60c6dc98626e5c73c851b2941c7a40781bde3cb00ee82b123c4369b39029ddf6b69d23a3c5492a157f948558d07ede3ab8152124348a994e71a2919
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 170bdba9b47b7e65906a28c8ce4f38a7a369d78e2271706f020849c1bfe0ee2067d4261df8bbb66eb84f79208fd5b710df759d64191db58cfba7ce8ef9c54b75
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.9.0":
  version: 8.12.1
  resolution: "acorn@npm:8.12.1"
  bin:
    acorn: bin/acorn
  checksum: 677880034aee5bdf7434cc2d25b641d7bedb0b5ef47868a78dadabedccf58e1c5457526d9d8249cd253f2df087e081c3fe7d903b448d8e19e5131a3065b83c07
  languageName: node
  linkType: hard

"adler-32@npm:~1.3.0":
  version: 1.3.1
  resolution: "adler-32@npm:1.3.1"
  checksum: c7f6b02df64a4392fcf1591862344f56733716a558e97a8b06a553dadeeaec792054512389000f42f371b13d2be5370e056e18db3b573944b595c4cb7742c5c6
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2, agent-base@npm:^7.1.0, agent-base@npm:^7.1.1":
  version: 7.1.1
  resolution: "agent-base@npm:7.1.1"
  dependencies:
    debug: ^4.3.4
  checksum: 51c158769c5c051482f9ca2e6e1ec085ac72b5a418a9b31b4e82fe6c0a6699adb94c1c42d246699a587b3335215037091c79e0de512c516f73b6ea844202f037
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.1.3, agentkeepalive@npm:^4.2.1":
  version: 4.5.0
  resolution: "agentkeepalive@npm:4.5.0"
  dependencies:
    humanize-ms: ^1.2.1
  checksum: 13278cd5b125e51eddd5079f04d6fe0914ac1b8b91c1f3db2c1822f99ac1a7457869068997784342fe455d59daaff22e14fb7b8c3da4e741896e7e31faf92481
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.1":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: 1797bf242cfffbaf3b870d13565bd1716b73f214bb7ada9a497063aada210200da36e3ed40237285f3255acc4feeae91b1fb183625331bad27da95973f7253d9
  languageName: node
  linkType: hard

"algoliasearch-helper@npm:3.22.5":
  version: 3.22.5
  resolution: "algoliasearch-helper@npm:3.22.5"
  dependencies:
    "@algolia/events": ^4.0.1
  peerDependencies:
    algoliasearch: ">= 3.1 < 6"
  checksum: bf1f0573778a5d4398efbc723558eaa7efd81a0c906c8c6339d6ab243e065c8dabea656c5bb914cda8dfa57e7af3c16e5370b0399f265a2a3dc49fed0dceedac
  languageName: node
  linkType: hard

"algoliasearch@npm:^4.20.0":
  version: 4.24.0
  resolution: "algoliasearch@npm:4.24.0"
  dependencies:
    "@algolia/cache-browser-local-storage": 4.24.0
    "@algolia/cache-common": 4.24.0
    "@algolia/cache-in-memory": 4.24.0
    "@algolia/client-account": 4.24.0
    "@algolia/client-analytics": 4.24.0
    "@algolia/client-common": 4.24.0
    "@algolia/client-personalization": 4.24.0
    "@algolia/client-search": 4.24.0
    "@algolia/logger-common": 4.24.0
    "@algolia/logger-console": 4.24.0
    "@algolia/recommend": 4.24.0
    "@algolia/requester-browser-xhr": 4.24.0
    "@algolia/requester-common": 4.24.0
    "@algolia/requester-node-http": 4.24.0
    "@algolia/transporter": 4.24.0
  checksum: 13cae6ea7ff05e068906dcb101b940bcf1a4ea41008757554c16a7951cdaa3af3094e547e3e51f9e751f68906b5654506e1dd4a1debb1b9d54cbb227ca83e8db
  languageName: node
  linkType: hard

"altusgroup@workspace:.":
  version: 0.0.0-use.local
  resolution: "altusgroup@workspace:."
  dependencies:
    "@algolia/recommend": ^4.20.0
    "@algolia/recommend-react": ^1.10.0
    "@google-cloud/translate": ^8.0.2
    "@next/env": ^13.4.19
    "@react-google-maps/api": ^2.19.2
    "@reduxjs/toolkit": ^1.9.6
    "@rollup/plugin-commonjs": ^25.0.4
    "@rollup/plugin-node-resolve": ^15.2.0
    "@rollup/plugin-typescript": ^11.1.2
    "@statsig/client-core": ^1.4.0
    "@statsig/js-client": ^1.4.0
    "@statsig/react-bindings": ^1.4.0
    "@tiptap/pm": ^2.9.1
    "@tiptap/react": ^2.9.1
    "@tiptap/starter-kit": ^2.9.1
    "@types/d3-geo": ^3.1.0
    "@types/node": 20.3.2
    "@types/papaparse": ^5.3.9
    "@types/react": 18.2.14
    "@types/react-dom": 18.2.6
    "@types/topojson-client": ^3.1.4
    "@types/yt-player": ^3.5.1
    "@typescript-eslint/eslint-plugin": ^6.4.1
    "@typescript-eslint/parser": ^6.7.4
    "@vercel/speed-insights": ^1.0.11
    "@wokaylabs/tiptap-react-render": ^0.0.1
    algoliasearch: ^4.20.0
    csv-parser: ^3.0.0
    csvtojson: ^2.0.10
    d3-geo: ^3.1.1
    dotenv: ^16.3.1
    echarts: ^5.5.0
    echarts-for-react: ^3.0.2
    eslint: ^8.47.0
    eslint-plugin-storybook: ^0.6.14
    eslint-plugin-unused-imports: ^3.0.0
    fs-extra: ^11.1.1
    husky: ^8.0.0
    immer: ^10.1.1
    inquirer: ^9.2.10
    jspdf: ^2.5.1
    moment: ^2.29.4
    next: 14.2.3
    node-sass: ^9.0.0
    papaparse: ^5.4.1
    postcss-import: ^15.1.0
    prettier: ^3.3.3
    react: latest
    react-bootstrap-icons: ^1.10.2
    react-dom: latest
    react-error-boundary: ^4.0.13
    react-icons: ^4.10.1
    react-instantsearch: latest
    react-redux: ^9.1.2
    react-share: ^5.0.3
    react-tooltip: ^5.21.4
    rollup: ^3.29.0
    rollup-plugin-babel: ^4.4.0
    rollup-plugin-postcss: ^4.0.2
    rollup-plugin-styles: ^4.0.0
    sass: ^1.64.2
    statsig-node: ^5.23.1
    statsig-react: ^2.1.0
    styled-components: ^6.0.0-rc.1
    stylelint: ^15.10.3
    stylelint-config-standard: ^34.0.0
    topojson-client: ^3.1.0
    typescript: 5.1.6
    xlsx: ^0.18.5
    yt-player: ^3.6.1
  languageName: unknown
  linkType: soft

"ansi-escapes@npm:^4.3.2":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: 745075dd4a4624ff0225c331dacb99be501a515d39bcb7c84d24660314a6ec28e68131b137e6f7e16318170842ce97538cd298fc4cd6b2cc798e0b957f2747e7
  languageName: node
  linkType: hard

"arrify@npm:^2.0.0, arrify@npm:^2.0.1":
  version: 2.0.1
  resolution: "arrify@npm:2.0.1"
  checksum: 067c4c1afd182806a82e4c1cb8acee16ab8b5284fbca1ce29408e6e91281c36bb5b612f6ddfbd40a0f7a7e0c75bf2696eb94c027f6e328d6e9c52465c98e4209
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 876231688c66400473ba505731df37ea436e574dd524520294cc3bbc54ea40334865e01fa0d074d74d036ee874ee7e62f486ea38bc421ee8e6a871c06f011766
  languageName: node
  linkType: hard

"async-foreach@npm:^0.1.3":
  version: 0.1.3
  resolution: "async-foreach@npm:0.1.3"
  checksum: cc43dee65de4decfa521d9444fb87edb2d475e7125d7f63d0d12004d12953e382135a2ea89a83b145ee1b9ec140550c804e1bfca24085d6faeb52c2902acd1f1
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: dfeeeb70090c5ebea7be4b9f787f866686c645d9f39a0d184c817252d0cf08455ed25267d79c03254d3be1f03ac399992a792edcd5ffb9c91e097ab5ef42833a
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"balanced-match@npm:^2.0.0":
  version: 2.0.0
  resolution: "balanced-match@npm:2.0.0"
  checksum: 9a5caad6a292c5df164cc6d0c38e0eedf9a1413f42e5fece733640949d74d0052cfa9587c1a1681f772147fb79be495121325a649526957fd75b3a216d1fbc68
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "base64-arraybuffer@npm:1.0.2"
  checksum: 15e6400d2d028bf18be4ed97702b11418f8f8779fb8c743251c863b726638d52f69571d4cc1843224da7838abef0949c670bde46936663c45ad078e89fee5c62
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.0, base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.1.2
  resolution: "bignumber.js@npm:9.1.2"
  checksum: 582c03af77ec9cb0ebd682a373ee6c66475db94a4325f92299621d544aa4bd45cb45fd60001610e94aef8ae98a0905fa538241d9638d4422d57abbeeac6fadaf
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 9e8521fa7e83aa9427c6f8ccdcba6e8167ef30cc9a22df26effcc5ab682ef91d2cbc23a239f945d099289e4bbcfae7a192e9c28c84c6202e710a0dfec3722662
  languageName: node
  linkType: hard

"bluebird@npm:^3.5.1":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 869417503c722e7dc54ca46715f70e15f4d9c602a423a02c825570862d12935be59ed9c7ba34a9b31f186c017c23cac6b54e35446f8353059c101da73eac22ef
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.0.0, browserslist@npm:^4.21.4":
  version: 4.23.3
  resolution: "browserslist@npm:4.23.3"
  dependencies:
    caniuse-lite: ^1.0.30001646
    electron-to-chromium: ^1.5.4
    node-releases: ^2.0.18
    update-browserslist-db: ^1.1.0
  bin:
    browserslist: cli.js
  checksum: 7906064f9970aeb941310b2fcb8b4ace4a1b50aa657c986677c6f1553a8cabcc94ee9c5922f715baffbedaa0e6cf0831b6fed7b059dde6873a4bfadcbe069c7e
  languageName: node
  linkType: hard

"btoa@npm:^1.2.1":
  version: 1.2.1
  resolution: "btoa@npm:1.2.1"
  bin:
    btoa: bin/btoa.js
  checksum: afbf004fb1b1d530e053ffa66ef5bd3878b101c59d808ac947fcff96810b4452abba2b54be687adadea2ba9efc7af48b04228742789bf824ef93f103767e690c
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: 80bb945f5d782a56f374b292770901065bad21420e34936ecbe949e57724b4a13874f735850dd1cc61f078773c4fb5493a41391e7bda40d1fa388d6bd80daaab
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"builtin-modules@npm:^3.3.0":
  version: 3.3.0
  resolution: "builtin-modules@npm:3.3.0"
  checksum: db021755d7ed8be048f25668fe2117620861ef6703ea2c65ed2779c9e3636d5c3b82325bd912244293959ff3ae303afa3471f6a15bf5060c103e4cc3a839749d
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"cacache@npm:^15.2.0":
  version: 15.3.0
  resolution: "cacache@npm:15.3.0"
  dependencies:
    "@npmcli/fs": ^1.0.0
    "@npmcli/move-file": ^1.0.1
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    glob: ^7.1.4
    infer-owner: ^1.0.4
    lru-cache: ^6.0.0
    minipass: ^3.1.1
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.2
    mkdirp: ^1.0.3
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^8.0.1
    tar: ^6.0.2
    unique-filename: ^1.1.1
  checksum: a07327c27a4152c04eb0a831c63c00390d90f94d51bb80624a66f4e14a6b6360bbf02a84421267bd4d00ca73ac9773287d8d7169e8d2eafe378d2ce140579db8
  languageName: node
  linkType: hard

"cacache@npm:^16.1.0":
  version: 16.1.3
  resolution: "cacache@npm:16.1.3"
  dependencies:
    "@npmcli/fs": ^2.1.0
    "@npmcli/move-file": ^2.0.0
    chownr: ^2.0.0
    fs-minipass: ^2.1.0
    glob: ^8.0.1
    infer-owner: ^1.0.4
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    mkdirp: ^1.0.4
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^9.0.0
    tar: ^6.1.11
    unique-filename: ^2.0.0
  checksum: d91409e6e57d7d9a3a25e5dcc589c84e75b178ae8ea7de05cbf6b783f77a5fae938f6e8fda6f5257ed70000be27a681e1e44829251bfffe4c10216002f8f14e6
  languageName: node
  linkType: hard

"cacache@npm:^18.0.0":
  version: 18.0.4
  resolution: "cacache@npm:18.0.4"
  dependencies:
    "@npmcli/fs": ^3.1.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^4.0.0
    ssri: ^10.0.0
    tar: ^6.1.11
    unique-filename: ^3.0.0
  checksum: b7422c113b4ec750f33beeca0f426a0024c28e3172f332218f48f963e5b970647fa1ac05679fe5bb448832c51efea9fda4456b9a95c3a1af1105fe6c1833cde2
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-keys@npm:^6.2.2":
  version: 6.2.2
  resolution: "camelcase-keys@npm:6.2.2"
  dependencies:
    camelcase: ^5.3.1
    map-obj: ^4.0.0
    quick-lru: ^4.0.1
  checksum: 43c9af1adf840471e54c68ab3e5fe8a62719a6b7dbf4e2e86886b7b0ff96112c945736342b837bd2529ec9d1c7d1934e5653318478d98e0cf22c475c04658e2a
  languageName: node
  linkType: hard

"camelcase-keys@npm:^7.0.0":
  version: 7.0.2
  resolution: "camelcase-keys@npm:7.0.2"
  dependencies:
    camelcase: ^6.3.0
    map-obj: ^4.1.0
    quick-lru: ^5.1.1
    type-fest: ^1.2.1
  checksum: b5821cc48dd00e8398a30c5d6547f06837ab44de123f1b3a603d0a03399722b2fc67a485a7e47106eb02ef543c3b50c5ebaabc1242cde4b63a267c3258d2365b
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.3.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 91d8611d09af725e422a23993890d22b2b72b4cabf7239651856950c76b4bf53fe0d0da7c5e4db05180e898e4e647220e78c9fbc976113bd96d603d1fcbfcb99
  languageName: node
  linkType: hard

"caniuse-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "caniuse-api@npm:3.0.0"
  dependencies:
    browserslist: ^4.0.0
    caniuse-lite: ^1.0.0
    lodash.memoize: ^4.1.2
    lodash.uniq: ^4.5.0
  checksum: db2a229383b20d0529b6b589dde99d7b6cb56ba371366f58cbbfa2929c9f42c01f873e2b6ef641d4eda9f0b4118de77dbb2805814670bdad4234bf08e720b0b4
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.0, caniuse-lite@npm:^1.0.30001579, caniuse-lite@npm:^1.0.30001646":
  version: 1.0.30001646
  resolution: "caniuse-lite@npm:1.0.30001646"
  checksum: 53d45b990d21036aaab7547e164174a0ac9a117acdd14a6c33822c4983e2671b1df48686d5383002d0ef158b208b0047a7dc404312a6229bf8ee629de3351b44
  languageName: node
  linkType: hard

"canvg@npm:^3.0.6":
  version: 3.0.10
  resolution: "canvg@npm:3.0.10"
  dependencies:
    "@babel/runtime": ^7.12.5
    "@types/raf": ^3.4.0
    core-js: ^3.8.3
    raf: ^3.4.1
    regenerator-runtime: ^0.13.7
    rgbcolor: ^1.0.1
    stackblur-canvas: ^2.0.0
    svg-pathdata: ^6.0.3
  checksum: 2cfd86bcb9b56b43a97745cc672e696169b4c09e8850fb4f27bec5ebf173179d16feb594224d643a32f1ce01e47b55d44e0058419114d48d34f12c2452c65927
  languageName: node
  linkType: hard

"cfb@npm:~1.2.1":
  version: 1.2.2
  resolution: "cfb@npm:1.2.2"
  dependencies:
    adler-32: ~1.3.0
    crc-32: ~1.2.0
  checksum: cfb63a7d630a7fa415c1b25655dca66666584f29c95fb0ee90866ada1a28090857827f2ba70a9a50df28bdce05728ae58d495bce417249f305ef7b3c85840024
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 6fd5da1f5d18ff5712c1e0aed41da200d7c51c28f11b36ee3c7b483f3696dabc08927fc6b227735eb8f0e1215c9a8abd8154637f3eff8cada5959df7f58b024d
  languageName: node
  linkType: hard

"chokidar@npm:>=3.0.0 <4.0.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: d2f29f499705dcd4f6f3bbed79a9ce2388cf530460122eed3b9c48efeab7a4e28739c6551fd15bec9245c6b9eeca7a32baa64694d64d9b6faeb74ddb8c4a413d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"classnames@npm:^2.3.0, classnames@npm:^2.3.2":
  version: 2.5.1
  resolution: "classnames@npm:2.5.1"
  checksum: da424a8a6f3a96a2e87d01a432ba19315503294ac7e025f9fece656db6b6a0f7b5003bb1fbb51cbb0d9624d964f1b9bb35a51c73af9b2434c7b292c42231c1e5
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 1bd588289b28432e4676cb5d40505cfe3e53f2e4e10fbe05c8a710a154d6fe0ce7836844b00d6858f740f2ffe67cdc36e0fce9c7b6a8430e80e6388d5aa4956c
  languageName: node
  linkType: hard

"cli-width@npm:^4.1.0":
  version: 4.1.0
  resolution: "cli-width@npm:4.1.0"
  checksum: 0a79cff2dbf89ef530bcd54c713703ba94461457b11e5634bd024c78796ed21401e32349c004995954e06f442d82609287e7aabf6a5f02c919a1cf3b9b6854ff
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"codepage@npm:~1.15.0":
  version: 1.15.0
  resolution: "codepage@npm:1.15.0"
  checksum: 86bdfd8f8fd4d78ace6ddab02a1621cbb4a833686fe886984b4155d99cd0287581d69495774b816ab2f571c4dc851c1595e1dbb8d69bd6dbb5a631ebf317fab0
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"colord@npm:^2.9.1, colord@npm:^2.9.3":
  version: 2.9.3
  resolution: "colord@npm:2.9.3"
  checksum: 95d909bfbcfd8d5605cbb5af56f2d1ce2b323990258fd7c0d2eb0e6d3bb177254d7fb8213758db56bb4ede708964f78c6b992b326615f81a18a6aaf11d64c650
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"commander@npm:2":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"concat-with-sourcemaps@npm:^1.1.0":
  version: 1.1.0
  resolution: "concat-with-sourcemaps@npm:1.1.0"
  dependencies:
    source-map: ^0.6.1
  checksum: 57faa6f4a6f38a1846a58f96b2745ec8435755e0021f069e89085c651d091b78d9bc20807ea76c38c85021acca80dc2fa4cedda666aade169b602604215d25b9
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"core-js@npm:^3.6.0, core-js@npm:^3.8.3":
  version: 3.37.1
  resolution: "core-js@npm:3.37.1"
  checksum: 2d58a5c599f05c3e04abc8bc5e64b88eb17d914c0f552f670fb800afa74ec54b4fcc7f231ad6bd45badaf62c0fb0ce30e6fe89cedb6bb6d54e6f19115c3c17ff
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.1":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": ^4.0.0
    import-fresh: ^3.2.1
    parse-json: ^5.0.0
    path-type: ^4.0.0
    yaml: ^1.10.0
  checksum: c53bf7befc1591b2651a22414a5e786cd5f2eeaa87f3678a3d49d6069835a9d8d1aef223728e98aa8fec9a95bf831120d245096db12abe019fecb51f5696c96f
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.2.0":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
    path-type: ^4.0.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dc339ebea427898c9e03bf01b56ba7afbac07fc7d2a2d5a15d6e9c14de98275a9565da949375aee1809591c152c0a3877bb86dbeaf74d5bd5aaa79955ad9e7a0
  languageName: node
  linkType: hard

"crc-32@npm:~1.2.0, crc-32@npm:~1.2.1":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: ad2d0ad0cbd465b75dcaeeff0600f8195b686816ab5f3ba4c6e052a07f728c3e70df2e3ca9fd3d4484dc4ba70586e161ca5a2334ec8bf5a41bf022a6103ff243
  languageName: node
  linkType: hard

"crelt@npm:^1.0.0":
  version: 1.0.6
  resolution: "crelt@npm:1.0.6"
  checksum: dad842093371ad702afbc0531bfca2b0a8dd920b23a42f26e66dabbed9aad9acd5b9030496359545ef3937c3aced0fd4ac39f7a2d280a23ddf9eb7fdcb94a69f
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 8f125e3ad477bd03c77b533044bd9e8a6f7c0da52d49bbc0bbe38327b3829d6ba04d368ca49dd9ff3b667d2fc8f1698d891c198bbf8feade1a5501bf5a296408
  languageName: node
  linkType: hard

"css-declaration-sorter@npm:^6.3.1":
  version: 6.4.1
  resolution: "css-declaration-sorter@npm:6.4.1"
  peerDependencies:
    postcss: ^8.0.9
  checksum: cbdc9e0d481011b1a28fd5b60d4eb55fe204391d31a0b1b490b2cecf4baa85810f9b8c48adab4df644f4718104ed3ed72c64a9745e3216173767bf4aeca7f9b8
  languageName: node
  linkType: hard

"css-functions-list@npm:^3.2.1":
  version: 3.2.2
  resolution: "css-functions-list@npm:3.2.2"
  checksum: b8a564118b93b87b63236a57132a3ef581416896a70c1d0df73360a9ec43dc582f7c2a586b578feb8476179518e557c6657570a8b6185b16300c7232a84d43e3
  languageName: node
  linkType: hard

"css-line-break@npm:^2.1.0":
  version: 2.1.0
  resolution: "css-line-break@npm:2.1.0"
  dependencies:
    utrie: ^1.0.2
  checksum: 37b1fe632b03be7a287cd394cef8b5285666343443125c510df9cfb6a4734a2c71e154ec8f7bbff72d7c339e1e5872989b1c52d86162aed27d6cc114725bb4d0
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.0.1
    domhandler: ^4.3.1
    domutils: ^2.8.0
    nth-check: ^2.0.1
  checksum: d6202736839194dd7f910320032e7cfc40372f025e4bf21ca5bf6eb0a33264f322f50ba9c0adc35dadd342d3d6fae5ca244779a4873afbfa76561e343f2058e0
  languageName: node
  linkType: hard

"css-to-react-native@npm:3.2.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: ^1.0.0
    css-color-keywords: ^1.0.0
    postcss-value-parser: ^4.0.2
  checksum: 263be65e805aef02c3f20c064665c998a8c35293e1505dbe6e3054fb186b01a9897ac6cf121f9840e5a9dfe3fb3994f6fcd0af84a865f1df78ba5bf89e77adce
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2, css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: 2.0.14
    source-map: ^0.6.1
  checksum: 79f9b81803991b6977b7fcb1588799270438274d89066ce08f117f5cdb5e20019b446d766c61506dd772c839df84caa16042d6076f20c97187f5abe3b50e7d1f
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: 2.0.30
    source-map-js: ^1.0.1
  checksum: 493cc24b5c22b05ee5314b8a0d72d8a5869491c1458017ae5ed75aeb6c3596637dbe1b11dac2548974624adec9f7a1f3a6cf40593dc1f9185eb0e8279543fbc0
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"cssnano-preset-default@npm:^5.2.14":
  version: 5.2.14
  resolution: "cssnano-preset-default@npm:5.2.14"
  dependencies:
    css-declaration-sorter: ^6.3.1
    cssnano-utils: ^3.1.0
    postcss-calc: ^8.2.3
    postcss-colormin: ^5.3.1
    postcss-convert-values: ^5.1.3
    postcss-discard-comments: ^5.1.2
    postcss-discard-duplicates: ^5.1.0
    postcss-discard-empty: ^5.1.1
    postcss-discard-overridden: ^5.1.0
    postcss-merge-longhand: ^5.1.7
    postcss-merge-rules: ^5.1.4
    postcss-minify-font-values: ^5.1.0
    postcss-minify-gradients: ^5.1.1
    postcss-minify-params: ^5.1.4
    postcss-minify-selectors: ^5.2.1
    postcss-normalize-charset: ^5.1.0
    postcss-normalize-display-values: ^5.1.0
    postcss-normalize-positions: ^5.1.1
    postcss-normalize-repeat-style: ^5.1.1
    postcss-normalize-string: ^5.1.0
    postcss-normalize-timing-functions: ^5.1.0
    postcss-normalize-unicode: ^5.1.1
    postcss-normalize-url: ^5.1.0
    postcss-normalize-whitespace: ^5.1.1
    postcss-ordered-values: ^5.1.3
    postcss-reduce-initial: ^5.1.2
    postcss-reduce-transforms: ^5.1.0
    postcss-svgo: ^5.1.0
    postcss-unique-selectors: ^5.1.1
  peerDependencies:
    postcss: ^8.2.15
  checksum: d3bbbe3d50c6174afb28d0bdb65b511fdab33952ec84810aef58b87189f3891c34aaa8b6a6101acd5314f8acded839b43513e39a75f91a698ddc985a1b1d9e95
  languageName: node
  linkType: hard

"cssnano-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "cssnano-utils@npm:3.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 975c84ce9174cf23bb1da1e9faed8421954607e9ea76440cd3bb0c1bea7e17e490d800fca5ae2812d1d9e9d5524eef23ede0a3f52497d7ccc628e5d7321536f2
  languageName: node
  linkType: hard

"cssnano@npm:^5.0.1, cssnano@npm:^5.0.15":
  version: 5.1.15
  resolution: "cssnano@npm:5.1.15"
  dependencies:
    cssnano-preset-default: ^5.2.14
    lilconfig: ^2.0.3
    yaml: ^1.10.2
  peerDependencies:
    postcss: ^8.2.15
  checksum: ca9e1922178617c66c2f1548824b2c7af2ecf69cc3a187fc96bf8d29251c2e84d9e4966c69cf64a2a6a057a37dff7d6d057bc8a2a0957e6ea382e452ae9d0bbb
  languageName: node
  linkType: hard

"csso@npm:^4.2.0":
  version: 4.2.0
  resolution: "csso@npm:4.2.0"
  dependencies:
    css-tree: ^1.1.2
  checksum: 380ba9663da3bcea58dee358a0d8c4468bb6539be3c439dc266ac41c047217f52fd698fb7e4b6b6ccdfb8cf53ef4ceed8cc8ceccb8dfca2aa628319826b5b998
  languageName: node
  linkType: hard

"csstype@npm:3.1.3, csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"csv-parser@npm:^3.0.0":
  version: 3.0.0
  resolution: "csv-parser@npm:3.0.0"
  dependencies:
    minimist: ^1.2.0
  bin:
    csv-parser: bin/csv-parser
  checksum: adc9d67d9f185249825570778c24d13004625301655330f6b735a052b9fdfbe1a239a014afb1f89939e0626ee573718f71f9f14164db7c17e4bcb2f38d6a162b
  languageName: node
  linkType: hard

"csvtojson@npm:^2.0.10":
  version: 2.0.10
  resolution: "csvtojson@npm:2.0.10"
  dependencies:
    bluebird: ^3.5.1
    lodash: ^4.17.3
    strip-bom: ^2.0.0
  bin:
    csvtojson: ./bin/csvtojson
  checksum: 5312b054cd989c26f8b1ce2941f05c98cbdcb7e66b0585196f0299d57d533c6747d74b73f70c26befa13a53e2f4281421f5e841ef47dea6694a1bdc187f3accf
  languageName: node
  linkType: hard

"d3-array@npm:2.5.0 - 3":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: 1 - 2
  checksum: a5976a6d6205f69208478bb44920dd7ce3e788c9dceb86b304dbe401a4bfb42ecc8b04c20facde486e9adcb488b5d1800d49393a3f81a23902b68158e12cddd0
  languageName: node
  linkType: hard

"d3-geo@npm:^3.1.1":
  version: 3.1.1
  resolution: "d3-geo@npm:3.1.1"
  dependencies:
    d3-array: 2.5.0 - 3
  checksum: 3cc4bb50af5d2d4858d2df1729a1777b7fd361854079d9faab1166186c988d2cba0d11911da0c4598d5e22fae91d79113ed262a9f98cabdbc6dbf7c30e5c0363
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.3.6
  resolution: "debug@npm:4.3.6"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 1630b748dea3c581295e02137a9f5cbe2c1d85fea35c1e6597a65ca2b16a6fce68cec61b299d480787ef310ba927dc8c92d3061faba0ad06c6a724672f66be7f
  languageName: node
  linkType: hard

"debug@npm:^2.1.3":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"decamelize-keys@npm:^1.1.0":
  version: 1.1.1
  resolution: "decamelize-keys@npm:1.1.1"
  dependencies:
    decamelize: ^1.1.0
    map-obj: ^1.0.0
  checksum: fc645fe20b7bda2680bbf9481a3477257a7f9304b1691036092b97ab04c0ab53e3bf9fcc2d2ae382536568e402ec41fb11e1d4c3836a9abe2d813dd9ef4311e0
  languageName: node
  linkType: hard

"decamelize@npm:^1.1.0, decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decamelize@npm:^5.0.0":
  version: 5.0.1
  resolution: "decamelize@npm:5.0.1"
  checksum: 7c3b1ed4b3e60e7fbc00a35fb248298527c1cdfe603e41dfcf05e6c4a8cb9efbee60630deb677ed428908fb4e74e322966c687a094d1478ddc9c3a74e9dc7140
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 95476a7d28f267292ce745eac3524a9079058bbb35767b76e3ee87d42e34cd0275d2eb19d9d08c3e167f97556e8a2872747f5e65cbebcac8b0c98d83e285f139
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"dequal@npm:2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 8679b850e1a3d0ebbc46ee780d5df7b478c23f335887464023a631d1b9af051ad4a6595a44220f9ff8ff95a8ddccf019b5ad778a976fd7bbf77383d36f412f90
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: fbb0b01f87a8a2d18e6e5a388ad0f7ec4a5c05c06d219377da1abc7bb0f674d804f4a8a94e3f71ff15f6cb7dcfc75704a54b261db672b9b3ab03da6b758b0b22
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: ^2.2.0
  checksum: 4c665ceed016e1911bf7d1dadc09dc888090b64dee7851cccd2fcf5442747ec39c647bb1cb8c8919f8bbdd0f0c625a6bafeeed4b2d656bbecdbae893f43ffaaa
  languageName: node
  linkType: hard

"dompurify@npm:^2.2.0":
  version: 2.5.6
  resolution: "dompurify@npm:2.5.6"
  checksum: 1d329fe79928aa86c61539b758bdbc53df58dd90bdc5b74032a2a3a22a436e84178d8f6ad8b022c8f6fac46b26d6e7e553c0cd131a37ed5105bbed6bf87be226
  languageName: node
  linkType: hard

"domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"dotenv@npm:^16.3.1":
  version: 16.4.5
  resolution: "dotenv@npm:16.4.5"
  checksum: 301a12c3d44fd49888b74eb9ccf9f07a1f5df43f489e7fcb89647a2edcd84c42d6bc349dc8df099cd18f07c35c7b04685c1a4f3e6a6a9e6b30f8d48c15b7f49c
  languageName: node
  linkType: hard

"duplexify@npm:^4.0.0, duplexify@npm:^4.1.1":
  version: 4.1.3
  resolution: "duplexify@npm:4.1.3"
  dependencies:
    end-of-stream: ^1.4.1
    inherits: ^2.0.3
    readable-stream: ^3.1.1
    stream-shift: ^1.0.2
  checksum: 9636a027345de3dd3c801594d01a7c73d9ce260019538beb1ee650bba7544e72f40a4d4902b52e1ab283dc32a06f210d42748773af02ff15e3064a9659deab7f
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11, ecdsa-sig-formatter@npm:^1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 207f9ab1c2669b8e65540bce29506134613dd5f122cccf1e6a560f4d63f2732d427d938f8481df175505aad94583bcb32c688737bb39a6df0625f903d6d93c03
  languageName: node
  linkType: hard

"echarts-for-react@npm:^3.0.2":
  version: 3.0.2
  resolution: "echarts-for-react@npm:3.0.2"
  dependencies:
    fast-deep-equal: ^3.1.3
    size-sensor: ^1.0.1
  peerDependencies:
    echarts: ^3.0.0 || ^4.0.0 || ^5.0.0
    react: ^15.0.0 || >=16.0.0
  checksum: d3b16325befb1294d99f6f089462415be739c1654370945eef2172efd5868596f10e4cd021e0ff65b89a6f9de5e9c331ccf3765d9167ccb12d573f9632b5b7a6
  languageName: node
  linkType: hard

"echarts@npm:^5.5.0":
  version: 5.5.1
  resolution: "echarts@npm:5.5.1"
  dependencies:
    tslib: 2.3.0
    zrender: 5.6.0
  checksum: 1b8e00a9492157d6faa58483add07bac7560e273e8716bd62d6e75777809dbe43106cd5bceb7e0deebf36ae100356395e2be07ce3a21442496241e25a219482f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.4":
  version: 1.5.4
  resolution: "electron-to-chromium@npm:1.5.4"
  checksum: 352f13c043cb185b464efe20f9b0a1adea2b1a7dad56e41dac995d0ad060f9981e479d632ebc73a1dce3bd5c36bbceeffe0667161ce296c2488fbb95f89bc793
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encoding@npm:^0.1.12, encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"entities@npm:^4.4.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 853f8ebd5b425d350bffa97dd6958143179a5938352ccae092c62d1267c4e392a039be1bae7d51b6e4ffad25f51f9617531fedf5237f15df302ccfb452cbf2d7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.1.2":
  version: 3.1.2
  resolution: "escalade@npm:3.1.2"
  checksum: 1ec0977aa2772075493002bdbd549d595ff6e9393b1cb0d7d6fcaf78c750da0c158f180938365486f75cb69fba20294351caddfce1b46552a7b6c3cde52eaa02
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-plugin-storybook@npm:^0.6.14":
  version: 0.6.15
  resolution: "eslint-plugin-storybook@npm:0.6.15"
  dependencies:
    "@storybook/csf": ^0.0.1
    "@typescript-eslint/utils": ^5.45.0
    requireindex: ^1.1.0
    ts-dedent: ^2.2.0
  peerDependencies:
    eslint: ">=6"
  checksum: e2c4d7be3e695c88d7194c363fba8ac644b36583bf9d608aa59dcd53cc5e422f7828611ee49c7934639ce827c0206d33fa94b3ea452ffbd2c8e7254ed90bc412
  languageName: node
  linkType: hard

"eslint-plugin-unused-imports@npm:^3.0.0":
  version: 3.2.0
  resolution: "eslint-plugin-unused-imports@npm:3.2.0"
  dependencies:
    eslint-rule-composer: ^0.3.0
  peerDependencies:
    "@typescript-eslint/eslint-plugin": 6 - 7
    eslint: 8
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
  checksum: e85ae4f3af489294ef5e0969ab904fa87f9fa7c959ca0804f30845438db4aeb0428ddad7ab06a70608e93121626799977241b442fdf126a4d0667be57390c3d6
  languageName: node
  linkType: hard

"eslint-rule-composer@npm:^0.3.0":
  version: 0.3.0
  resolution: "eslint-rule-composer@npm:0.3.0"
  checksum: c2f57cded8d1c8f82483e0ce28861214347e24fd79fd4144667974cd334d718f4ba05080aaef2399e3bbe36f7d6632865110227e6b176ed6daa2d676df9281b1
  languageName: node
  linkType: hard

"eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.2":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: ec97dbf5fb04b94e8f4c5a91a7f0a6dd3c55e46bfc7bbcd0e3138c3a76977570e02ed89a1810c778dcd72072ff0e9621ba1379b4babe53921d71e2e4486fda3e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint@npm:^8.47.0":
  version: 8.57.0
  resolution: "eslint@npm:8.57.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.6.1
    "@eslint/eslintrc": ^2.1.4
    "@eslint/js": 8.57.0
    "@humanwhocodes/config-array": ^0.11.14
    "@humanwhocodes/module-importer": ^1.0.1
    "@nodelib/fs.walk": ^1.2.8
    "@ungap/structured-clone": ^1.2.0
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    doctrine: ^3.0.0
    escape-string-regexp: ^4.0.0
    eslint-scope: ^7.2.2
    eslint-visitor-keys: ^3.4.3
    espree: ^9.6.1
    esquery: ^1.4.2
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^6.0.1
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    globals: ^13.19.0
    graphemer: ^1.4.0
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    js-yaml: ^4.1.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: 3a48d7ff85ab420a8447e9810d8087aea5b1df9ef68c9151732b478de698389ee656fd895635b5f2871c89ee5a2652b3f343d11e9db6f8486880374ebc74a2d9
  languageName: node
  linkType: hard

"espree@npm:^9.6.0, espree@npm:^9.6.1":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: ^8.9.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^3.4.1
  checksum: eb8c149c7a2a77b3f33a5af80c10875c3abd65450f60b8af6db1bfcfa8f101e21c1e56a561c6dc13b848e18148d43469e7cd208506238554fb5395a9ea5a1ab9
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"estree-walker@npm:^0.6.1":
  version: 0.6.1
  resolution: "estree-walker@npm:0.6.1"
  checksum: 9d6f82a4921f11eec18f8089fb3cce6e53bcf45a8e545c42a2674d02d055fb30f25f90495f8be60803df6c39680c80dcee7f944526867eb7aa1fc9254883b23d
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.1, estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 6151e6f9828abe2259e57f5fd3761335bb0d2ebd76dc1a01048ccee22fabcfef3c0859300f6d83ff0d1927849368775ec5a6d265dde2f6de5a1be1721cd94efc
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 1ffe3bb22a6d51bdeb6bf6f7cf97d2ff4a74b017ad12284cc9e6a279e727dc30a5de6bb613e5596ff4dc3e517841339ad09a7eec44266eccb1aa201a30448166
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.4":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 1875311c42fcfe9c707b2712c32664a245629b42bb0a5a84439762dd0fd637fc54d078155ea83c2af9e0323c9ac13687e03cfba79b03af9f40c89b4960099374
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"extend@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"external-editor@npm:^3.1.0":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: 1c2a616a73f1b3435ce04030261bed0e22d4737e14b090bb48e58865da92529c9f2b05b893de650738d55e692d071819b45e1669259b2b354bc3154d27a698c7
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3, fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.1":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: 900e4979f4dbc3313840078419245621259f349950411ca2fa445a2f9a1a6d98c3b5e7e0660c5ccd563aa61abe133a21765c6c0dec8e57da1ba71d8000b05ec1
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.1
  resolution: "fast-uri@npm:3.0.1"
  checksum: 106143ff83705995225dcc559411288f3337e732bb2e264e79788f1914b6bd8f8bc3683102de60b15ba00e6ebb443633cabac77d4ebc5cb228c47cf955e199ff
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.16":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: a78d44285c9e2ae2c25f3ef0f8a73f332c1247b7ea7fb4a191e6bb51aa6ee1ef0dfb3ed113616dcdc7023e18e35a8db41f61c8d88988e877cf510df8edafbc71
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: ^1.0.4
  checksum: a8c5b26788d5a1763f88bae56a8ddeee579f935a831c5fe7a8268cea5b0a91fbfe705f612209e02d639b881d7b48e461a50da4a10cfaa40da5ca7cc9da098d88
  languageName: node
  linkType: hard

"fflate@npm:^0.4.8":
  version: 0.4.8
  resolution: "fflate@npm:0.4.8"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: ^3.0.4
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-entry-cache@npm:^7.0.0":
  version: 7.0.2
  resolution: "file-entry-cache@npm:7.0.2"
  dependencies:
    flat-cache: ^3.2.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: cf2104a7c45ff48e7f505b78a3991c8f7f30f28bd8106ef582721f321f1c6277f7751aacd5d83026cb079d9d5091082f588d14a72e7c5d720ece79118fa61e10
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4, flat-cache@npm:^3.2.0":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.3
    rimraf: ^3.0.2
  checksum: e7e0f59801e288b54bee5cb9681e9ee21ee28ef309f886b312c9d08415b79fc0f24ac842f84356ce80f47d6a53de62197ce0e6e148dc42d5db005992e2a756ec
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.1
  resolution: "flatted@npm:3.3.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.2.1
  resolution: "foreground-child@npm:3.2.1"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 3e2e844d6003c96d70affe8ae98d7eaaba269a868c14d997620c088340a8775cd5d2d9043e6ceebae1928d8d9a874911c4d664b9a267e8995945df20337aebc0
  languageName: node
  linkType: hard

"form-data@npm:^2.5.0":
  version: 2.5.1
  resolution: "form-data@npm:2.5.1"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.6
    mime-types: ^2.1.12
  checksum: 5134ada56cc246b293a1ac7678dba6830000603a3979cf83ff7b2f21f2e3725202237cfb89e32bcb38a1d35727efbd3c3a22e65b42321e8ade8eec01ce755d08
  languageName: node
  linkType: hard

"frac@npm:~1.1.2":
  version: 1.1.2
  resolution: "frac@npm:1.1.2"
  checksum: fbfbb28003bb84506dd35e7aad8543c5a358bdc95451d0065b6127d40d2c45106f14221575c3e9ce3ea4bf0bbf1225b73c5d655965c9f4ce44332cbe1b34667d
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-extra@npm:^11.1.1":
  version: 11.2.0
  resolution: "fs-extra@npm:11.2.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: b12e42fa40ba47104202f57b8480dd098aa931c2724565e5e70779ab87605665594e76ee5fb00545f772ab9ace167fe06d2ab009c416dc8c842c5ae6df7aa7e8
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0, fs-minipass@npm:^2.1.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"gaxios@npm:^6.0.0, gaxios@npm:^6.1.1":
  version: 6.7.0
  resolution: "gaxios@npm:6.7.0"
  dependencies:
    extend: ^3.0.2
    https-proxy-agent: ^7.0.1
    is-stream: ^2.0.0
    node-fetch: ^2.6.9
    uuid: ^10.0.0
  checksum: 7316ea45cb1fc84d2725d675a6f23fc68c5dfa53b437b89c2596e3219a1bf32ee48f57242b670ebad515c9644d45cc7b2b7ef9063fa50a86de54e1a5a6433999
  languageName: node
  linkType: hard

"gaze@npm:^1.0.0":
  version: 1.1.3
  resolution: "gaze@npm:1.1.3"
  dependencies:
    globule: ^1.0.0
  checksum: d5fd375a029c07346154806a076bde21290598179d01ffbe7bc3e54092fa65814180bd27fc2b577582737733eec77cdbb7a572a4e73dff934dde60317223cde6
  languageName: node
  linkType: hard

"gcp-metadata@npm:^6.1.0":
  version: 6.1.0
  resolution: "gcp-metadata@npm:6.1.0"
  dependencies:
    gaxios: ^6.0.0
    json-bigint: ^1.0.0
  checksum: 55de8ae4a6b7664379a093abf7e758ae06e82f244d41bd58d881a470bf34db94c4067ce9e1b425d9455b7705636d5f8baad844e49bb73879c338753ba7785b2b
  languageName: node
  linkType: hard

"generic-names@npm:^4.0.0":
  version: 4.0.0
  resolution: "generic-names@npm:4.0.0"
  dependencies:
    loader-utils: ^3.2.0
  checksum: 8dabd2505164191501b75f2861b5e1194458a344ae2a7c9776bdd72d1f50b248dff737bcdf118fff677275edb3632f2d10662e6ac122dd7b245c5baa8d303270
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-stdin@npm:^4.0.1":
  version: 4.0.1
  resolution: "get-stdin@npm:4.0.1"
  checksum: 4f73d3fe0516bc1f3dc7764466a68ad7c2ba809397a02f56c2a598120e028430fcff137a648a01876b2adfb486b4bc164119f98f1f7d7c0abd63385bdaa0113f
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^7.0.0, glob@npm:^7.0.3, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"glob@npm:^8.0.1, glob@npm:^8.0.3":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^5.0.1
    once: ^1.3.0
  checksum: 92fbea3221a7d12075f26f0227abac435de868dd0736a17170663783296d0dd8d3d532a5672b4488a439bf5d7fb85cdd07c11185d6cd39184f0385cbdfb86a47
  languageName: node
  linkType: hard

"glob@npm:~7.1.1":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: b61f48973bbdcf5159997b0874a2165db572b368b931135832599875919c237fc05c12984e38fe828e69aa8a921eb0e8a4997266211c517c9cfaae8a93988bb8
  languageName: node
  linkType: hard

"global-modules@npm:^2.0.0":
  version: 2.0.0
  resolution: "global-modules@npm:2.0.0"
  dependencies:
    global-prefix: ^3.0.0
  checksum: d6197f25856c878c2fb5f038899f2dca7cbb2f7b7cf8999660c0104972d5cfa5c68b5a0a77fa8206bb536c3903a4615665acb9709b4d80846e1bb47eaef65430
  languageName: node
  linkType: hard

"global-prefix@npm:^3.0.0":
  version: 3.0.0
  resolution: "global-prefix@npm:3.0.0"
  dependencies:
    ini: ^1.3.5
    kind-of: ^6.0.2
    which: ^1.3.1
  checksum: 8a82fc1d6f22c45484a4e34656cc91bf021a03e03213b0035098d605bfc612d7141f1e14a21097e8a0413b4884afd5b260df0b6a25605ce9d722e11f1df2881d
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: ^0.20.2
  checksum: 56066ef058f6867c04ff203b8a44c15b038346a62efbc3060052a1016be9f56f4cf0b2cd45b74b22b81e521a889fc7786c73691b0549c2f3a6e825b3d394f43c
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"globjoin@npm:^0.1.4":
  version: 0.1.4
  resolution: "globjoin@npm:0.1.4"
  checksum: 0a47d88d566122d9e42da946453ee38b398e0021515ac6a95d13f980ba8c1e42954e05ee26cfcbffce1ac1ee094d0524b16ce1dd874ca52408d6db5c6d39985b
  languageName: node
  linkType: hard

"globule@npm:^1.0.0":
  version: 1.3.4
  resolution: "globule@npm:1.3.4"
  dependencies:
    glob: ~7.1.1
    lodash: ^4.17.21
    minimatch: ~3.0.2
  checksum: 258b6865c77d54fbd4c91dd6931d99baf81b1485fdf4bd2c053b1a10eab015163cb646e6c96812d5c8b027fb07adfc0b7c7fb13bbbb571f3c12ea60bd7fda2f5
  languageName: node
  linkType: hard

"google-auth-library@npm:^9.0.0, google-auth-library@npm:^9.3.0":
  version: 9.13.0
  resolution: "google-auth-library@npm:9.13.0"
  dependencies:
    base64-js: ^1.3.0
    ecdsa-sig-formatter: ^1.0.11
    gaxios: ^6.1.1
    gcp-metadata: ^6.1.0
    gtoken: ^7.0.0
    jws: ^4.0.0
  checksum: 1ccedc9b6ef6a274e78256aa602b3fb6dd44b3df41d5514058518775f3990a95e7ab0ebad74fe8a3e4f10c23dc8e6a8074bf132f3cc338f335d0e9a6095f8fd4
  languageName: node
  linkType: hard

"google-gax@npm:^4.0.3":
  version: 4.3.8
  resolution: "google-gax@npm:4.3.8"
  dependencies:
    "@grpc/grpc-js": ^1.10.9
    "@grpc/proto-loader": ^0.7.13
    "@types/long": ^4.0.0
    abort-controller: ^3.0.0
    duplexify: ^4.0.0
    google-auth-library: ^9.3.0
    node-fetch: ^2.6.1
    object-hash: ^3.0.0
    proto3-json-serializer: ^2.0.2
    protobufjs: ^7.3.2
    retry-request: ^7.0.0
    uuid: ^9.0.1
  checksum: e6a6946645d3290bf04c2815d091037ff24ef41bd3f8d9eaab802c82adc86b05fe665dc36181a79972292350a01a5e203e0f42dfa3498bf084caee99a16a8207
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"gtoken@npm:^7.0.0":
  version: 7.1.0
  resolution: "gtoken@npm:7.1.0"
  dependencies:
    gaxios: ^6.0.0
    jws: ^4.0.0
  checksum: 1f338dced78f9d895ea03cd507454eb5a7b77e841ecd1d45e44483b08c1e64d16a9b0342358d37586d87462ffc2d5f5bff5dfe77ed8d4f0aafc3b5b0347d5d16
  languageName: node
  linkType: hard

"hard-rejection@npm:^2.1.0":
  version: 2.1.0
  resolution: "hard-rejection@npm:2.1.0"
  checksum: 7baaf80a0c7fff4ca79687b4060113f1529589852152fa935e6787a2bc96211e784ad4588fb3048136ff8ffc9dfcf3ae385314a5b24db32de20bea0d1597f9dc
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"hogan.js@npm:^3.0.2":
  version: 3.0.2
  resolution: "hogan.js@npm:3.0.2"
  dependencies:
    mkdirp: 0.3.0
    nopt: 1.0.10
  bin:
    hulk: ./bin/hulk
  checksum: c7bbff84faa9ca265c39f4a2100546ba0388fcc9c5bac8526f488592ce3fcaa042eba6ac25db277f4478ec3855b9bc28ce59acffbf6e8a28d45a17df7590c6aa
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"hosted-git-info@npm:^4.0.1":
  version: 4.1.0
  resolution: "hosted-git-info@npm:4.1.0"
  dependencies:
    lru-cache: ^6.0.0
  checksum: c3f87b3c2f7eb8c2748c8f49c0c2517c9a95f35d26f4bf54b2a8cba05d2e668f3753548b6ea366b18ec8dadb4e12066e19fa382a01496b0ffa0497eb23cbe461
  languageName: node
  linkType: hard

"htm@npm:^3.0.0":
  version: 3.1.1
  resolution: "htm@npm:3.1.1"
  checksum: 1827a0cafffcff69690b048a4df59944086d7503fe5eb7c10b40834439205bdf992941e7aa25e92b3c2c086170565b4ed7c365bc072d31067c6e7a4e478776bd
  languageName: node
  linkType: hard

"html-entities@npm:^2.5.2":
  version: 2.5.2
  resolution: "html-entities@npm:2.5.2"
  checksum: b23f4a07d33d49ade1994069af4e13d31650e3fb62621e92ae10ecdf01d1a98065c78fd20fdc92b4c7881612210b37c275f2c9fba9777650ab0d6f2ceb3b99b6
  languageName: node
  linkType: hard

"html-tags@npm:^3.0.0, html-tags@npm:^3.3.1":
  version: 3.3.1
  resolution: "html-tags@npm:3.3.1"
  checksum: b4ef1d5a76b678e43cce46e3783d563607b1d550cab30b4f511211564574770aa8c658a400b100e588bc60b8234e59b35ff72c7851cc28f3b5403b13a2c6cbce
  languageName: node
  linkType: hard

"html2canvas@npm:^1.0.0-rc.5":
  version: 1.4.1
  resolution: "html2canvas@npm:1.4.1"
  dependencies:
    css-line-break: ^2.1.0
    text-segmentation: ^1.0.3
  checksum: c134324af57f3262eecf982e436a4843fded3c6cf61954440ffd682527e4dd350e0c2fafd217c0b6f9a455fe345d0c67b4505689796ab160d4ca7c91c3766739
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.0, http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^4.0.1":
  version: 4.0.1
  resolution: "http-proxy-agent@npm:4.0.1"
  dependencies:
    "@tootallnate/once": 1
    agent-base: 6
    debug: 4
  checksum: c6a5da5a1929416b6bbdf77b1aca13888013fe7eb9d59fc292e25d18e041bb154a8dfada58e223fc7b76b9b2d155a87e92e608235201f77d34aa258707963a82
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.5
  resolution: "https-proxy-agent@npm:7.0.5"
  dependencies:
    agent-base: ^7.0.2
    debug: 4
  checksum: 2e1a28960f13b041a50702ee74f240add8e75146a5c37fc98f1960f0496710f6918b3a9fe1e5aba41e50f58e6df48d107edd9c405c5f0d73ac260dabf2210857
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"husky@npm:^8.0.0":
  version: 8.0.3
  resolution: "husky@npm:8.0.3"
  bin:
    husky: lib/bin.js
  checksum: 837bc7e4413e58c1f2946d38fb050f5d7324c6f16b0fd66411ffce5703b294bd21429e8ba58711cd331951ee86ed529c5be4f76805959ff668a337dbfa82a1b0
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-replace-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "icss-replace-symbols@npm:1.1.0"
  checksum: 24575b2c2f7e762bfc6f4beee31be9ba98a01cad521b5aa9954090a5de2b5e1bf67814c17e22f9e51b7d798238db8215a173d6c2b4726ce634ce06b68ece8045
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.2.4":
  version: 5.3.1
  resolution: "ignore@npm:5.3.1"
  checksum: 71d7bb4c1dbe020f915fd881108cbe85a0db3d636a0ea3ba911393c53946711d13a9b1143c7e70db06d571a5822c0a324a6bcde5c9904e7ca5047f01f1bf8cd3
  languageName: node
  linkType: hard

"immer@npm:^10.1.1":
  version: 10.1.1
  resolution: "immer@npm:10.1.1"
  checksum: 07c67970b7d22aded73607193d84861bf786f07d47f7d7c98bb10016c7a88f6654ad78ae1e220b3c623695b133aabbf24f5eb8d9e8060cff11e89ccd81c9c10b
  languageName: node
  linkType: hard

"immer@npm:^9.0.21":
  version: 9.0.21
  resolution: "immer@npm:9.0.21"
  checksum: 70e3c274165995352f6936695f0ef4723c52c92c92dd0e9afdfe008175af39fa28e76aafb3a2ca9d57d1fb8f796efc4dd1e1cc36f18d33fa5b74f3dfb0375432
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.3.7
  resolution: "immutable@npm:4.3.7"
  checksum: 1c50eb053bb300796551604afff554066f041aa8e15926cf98f6d11d9736b62ad12531c06515dd96375258653878b4736f8051cd20b640f5f976d09fa640e3ec
  languageName: node
  linkType: hard

"import-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "import-cwd@npm:3.0.0"
  dependencies:
    import-from: ^3.0.0
  checksum: f2c4230e8389605154a390124381f9136811306ae4ba1c8017398c3c6926bc5cf75cf89350372b4938f79792ea373776b4efabd27506440ec301ce34c4e867eb
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "import-from@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 5040a7400e77e41e2c3bb6b1b123b52a15a284de1ffc03d605879942c00e3a87428499d8d031d554646108a0f77652549411167f6a7788e4fc7027eefccf3356
  languageName: node
  linkType: hard

"import-lazy@npm:^4.0.0":
  version: 4.0.0
  resolution: "import-lazy@npm:4.0.0"
  checksum: 22f5e51702134aef78890156738454f620e5fe7044b204ebc057c614888a1dd6fdf2ede0fdcca44d5c173fd64f65c985f19a51775b06967ef58cc3d26898df07
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"indent-string@npm:^5.0.0":
  version: 5.0.0
  resolution: "indent-string@npm:5.0.0"
  checksum: e466c27b6373440e6d84fbc19e750219ce25865cb82d578e41a6053d727e5520dc5725217d6eb1cc76005a1bb1696a0f106d84ce7ebda3033b963a38583fb3b3
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:^1.3.5":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"inquirer@npm:^9.2.10":
  version: 9.3.6
  resolution: "inquirer@npm:9.3.6"
  dependencies:
    "@inquirer/figures": ^1.0.3
    ansi-escapes: ^4.3.2
    cli-width: ^4.1.0
    external-editor: ^3.1.0
    mute-stream: 1.0.0
    ora: ^5.4.1
    run-async: ^3.0.0
    rxjs: ^7.8.1
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wrap-ansi: ^6.2.0
    yoctocolors-cjs: ^2.1.2
  checksum: f1fd086585e301ec17ce016355e9eb6eb87329c6de578cde35b10d5e4b57443b9f8f1f304d3ab570e5dad2cbc55851c476480296e15793f76836c0c33cf2e713
  languageName: node
  linkType: hard

"instantsearch-ui-components@npm:0.9.0":
  version: 0.9.0
  resolution: "instantsearch-ui-components@npm:0.9.0"
  dependencies:
    "@babel/runtime": ^7.1.2
  checksum: a45987abaae672beec88a7cfca8e1e25aabba3a8faae21e18e73c7a9080a9a4fe1ad20eaac5a7ae692f28f5be390fa5cd654572d92ce110d9b71290c745753c5
  languageName: node
  linkType: hard

"instantsearch.js@npm:4.75.0":
  version: 4.75.0
  resolution: "instantsearch.js@npm:4.75.0"
  dependencies:
    "@algolia/events": ^4.0.1
    "@types/dom-speech-recognition": ^0.0.1
    "@types/google.maps": ^3.55.12
    "@types/hogan.js": ^3.0.0
    "@types/qs": ^6.5.3
    algoliasearch-helper: 3.22.5
    hogan.js: ^3.0.2
    htm: ^3.0.0
    instantsearch-ui-components: 0.9.0
    preact: ^10.10.0
    qs: ^6.5.1 < 6.10
    search-insights: ^2.15.0
  peerDependencies:
    algoliasearch: ">= 3.1 < 6"
  checksum: 48e58033420ce926fa12cc30c006af01177ac7dfc6e0edf21bf503ce62350319e3468e3e3d0262fe1d8bc5fde298e4aa03802503204616ae1669c17414cb1019
  languageName: node
  linkType: hard

"internmap@npm:1 - 2":
  version: 2.0.3
  resolution: "internmap@npm:2.0.3"
  checksum: 7ca41ec6aba8f0072fc32fa8a023450a9f44503e2d8e403583c55714b25efd6390c38a87161ec456bf42d7bc83aab62eb28f5aef34876b1ac4e60693d5e1d241
  languageName: node
  linkType: hard

"invariant@npm:2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: ^1.0.0
  checksum: cc3182d793aad82a8d1f0af697b462939cb46066ec48bbf1707c150ad5fad6406137e91a262022c269702e01621f35ef60269f6c0d7fd178487959809acdfb14
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"ip3country@npm:^5.0.0":
  version: 5.0.1
  resolution: "ip3country@npm:5.0.1"
  checksum: adbffcb6785943a2bfa6d883c2eb3d4955b4325b2e04675736616d039f72fd81c5553328a664250226d9de3d77f168c27b5197c21e9ded4c735fbc3f4bbf45ee
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-builtin-module@npm:^3.2.1":
  version: 3.2.1
  resolution: "is-builtin-module@npm:3.2.1"
  dependencies:
    builtin-modules: ^3.3.0
  checksum: e8f0ffc19a98240bda9c7ada84d846486365af88d14616e737d280d378695c8c448a621dcafc8332dbf0fcd0a17b0763b845400709963fa9151ddffece90ae88
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.5.0":
  version: 2.15.0
  resolution: "is-core-module@npm:2.15.0"
  dependencies:
    hasown: ^2.0.2
  checksum: a9f7a52707c9b59d7164094d183bda892514fc3ba3139f245219c7abe7f6e8d3e2cdcf861f52a891a467f785f1dfa5d549f73b0ee715f4ba56e8882d335ea585
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-html@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-html@npm:2.0.0"
  dependencies:
    html-tags: ^3.0.0
  checksum: 0cc233e3851453913023560bcd2411a9294bfa18f994d4428f2692d2cfb7f90e5521a42cd4c6a62bf1bc7d42301cb214de442766cbd12a9aa52398d34ab5a2a7
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-module@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-module@npm:1.0.0"
  checksum: 8cd5390730c7976fb4e8546dd0b38865ee6f7bacfa08dfbb2cc07219606755f0b01709d9361e01f13009bbbd8099fa2927a8ed665118a6105d66e40f1b838c3f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: 0ee04807797aad50859652a7467481816cbb57e5cc97d813a7dcd8915da8195dc68c436010bf39d195226cde6a2d352f4b815f16f26b7bf486a5754290629931
  languageName: node
  linkType: hard

"is-plain-object@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-plain-object@npm:5.0.0"
  checksum: e32d27061eef62c0847d303125440a38660517e586f2f3db7c9d179ae5b6674ab0f469d519b2e25c147a1a3bc87156d0d5f4d8821e0ce4a9ee7fe1fcf11ce45c
  languageName: node
  linkType: hard

"is-reference@npm:1.2.1":
  version: 1.2.1
  resolution: "is-reference@npm:1.2.1"
  dependencies:
    "@types/estree": "*"
  checksum: e7b48149f8abda2c10849ea51965904d6a714193d68942ad74e30522231045acf06cbfae5a4be2702fede5d232e61bf50b3183acdc056e6e3afe07fcf4f4b2bc
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-utf8@npm:^0.2.0":
  version: 0.2.1
  resolution: "is-utf8@npm:0.2.1"
  checksum: 167ccd2be869fc228cc62c1a28df4b78c6b5485d15a29027d3b5dceb09b383e86a3522008b56dcac14b592b22f0a224388718c2505027a994fd8471465de54b3
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"js-base64@npm:^2.4.9":
  version: 2.6.4
  resolution: "js-base64@npm:2.6.4"
  checksum: 5f4084078d6c46f8529741d110df84b14fac3276b903760c21fa8cc8521370d607325dfe1c1a9fbbeaae1ff8e602665aaeef1362427d8fef704f9e3659472ce8
  languageName: node
  linkType: hard

"js-sha256@npm:^0.11.0":
  version: 0.11.0
  resolution: "js-sha256@npm:0.11.0"
  checksum: 742d34a0c6eb15247309f1c74889b5a51df01a96e4307375b420fbe973f2f25585012b4d3c8fa52a1b18546153d12587e6463bb725dc1bc58686da03e892c334
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"json-bigint@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-bigint@npm:1.0.0"
  dependencies:
    bignumber.js: ^9.0.0
  checksum: c67bb93ccb3c291e60eb4b62931403e378906aab113ec1c2a8dd0f9a7f065ad6fd9713d627b732abefae2e244ac9ce1721c7a3142b2979532f12b258634ce6f6
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonp@npm:^0.2.1":
  version: 0.2.1
  resolution: "jsonp@npm:0.2.1"
  dependencies:
    debug: ^2.1.3
  checksum: 90aabd9deb3a9ba83aedf8d40e1aaff1fc29f3f3fe42985a661782498dde526a7cd9b7bec4b8721f7d0beafab9e5ccfdafd46640f7dd6c58d529a6bb5238e2b8
  languageName: node
  linkType: hard

"jspdf@npm:^2.5.1":
  version: 2.5.1
  resolution: "jspdf@npm:2.5.1"
  dependencies:
    "@babel/runtime": ^7.14.0
    atob: ^2.1.2
    btoa: ^1.2.1
    canvg: ^3.0.6
    core-js: ^3.6.0
    dompurify: ^2.2.0
    fflate: ^0.4.8
    html2canvas: ^1.0.0-rc.5
  dependenciesMeta:
    canvg:
      optional: true
    core-js:
      optional: true
    dompurify:
      optional: true
    html2canvas:
      optional: true
  checksum: 9ecdccc50678cd780f0995157618630ca0da65576835983232d48001aab0b29e51af765e078808526d5e5e2e1ebf3cee460e03eaf590f875d160f2e0cb614a1e
  languageName: node
  linkType: hard

"jwa@npm:^2.0.0":
  version: 2.0.0
  resolution: "jwa@npm:2.0.0"
  dependencies:
    buffer-equal-constant-time: 1.0.1
    ecdsa-sig-formatter: 1.0.11
    safe-buffer: ^5.0.1
  checksum: 8f00b71ad5fe94cb55006d0d19202f8f56889109caada2f7eeb63ca81755769ce87f4f48101967f398462e3b8ae4faebfbd5a0269cb755dead5d63c77ba4d2f1
  languageName: node
  linkType: hard

"jws@npm:^4.0.0":
  version: 4.0.0
  resolution: "jws@npm:4.0.0"
  dependencies:
    jwa: ^2.0.0
    safe-buffer: ^5.0.1
  checksum: d68d07aa6d1b8cb35c363a9bd2b48f15064d342a5d9dc18a250dbbce8dc06bd7e4792516c50baa16b8d14f61167c19e851fd7f66b59ecc68b7f6a013759765f7
  languageName: node
  linkType: hard

"kdbush@npm:^4.0.2":
  version: 4.0.2
  resolution: "kdbush@npm:4.0.2"
  checksum: 6782ef2cdaec9322376b9955a16b0163beda0cefa2f87da76e8970ade2572d8b63bec915347aaeac609484b0c6e84d7b591f229ef353b68b460238095bacde2d
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2, kind-of@npm:^6.0.3":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"known-css-properties@npm:^0.29.0":
  version: 0.29.0
  resolution: "known-css-properties@npm:0.29.0"
  checksum: daa6562e907f856cbfd58a00c42f532c9bba283388984da6a3bffb494e56612e5f23c52f30b0d9885f0ea07ad5d88bfa0470ee65017a6ce6c565289a1afd78af
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lilconfig@npm:^2.0.3, lilconfig@npm:^2.0.5":
  version: 2.1.0
  resolution: "lilconfig@npm:2.1.0"
  checksum: 8549bb352b8192375fed4a74694cd61ad293904eee33f9d4866c2192865c44c4eb35d10782966242634e0cbc1e91fe62b1247f148dc5514918e3a966da7ea117
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"linkify-it@npm:^5.0.0":
  version: 5.0.0
  resolution: "linkify-it@npm:5.0.0"
  dependencies:
    uc.micro: ^2.0.0
  checksum: b0b86cadaf816b64c947a83994ceaad1c15f9fe7e079776ab88699fb71afd7b8fc3fd3d0ae5ebec8c92c1d347be9ba257b8aef338c0ebf81b0d27dcf429a765a
  languageName: node
  linkType: hard

"load-script2@npm:^2.0.1":
  version: 2.0.6
  resolution: "load-script2@npm:2.0.6"
  checksum: a9c54f07eacb1af147c742a922fedc1dc2e7923999e5857a4be363a76b60ae625d4a94cbab371c67dad5f1a98b5c9387e57db4e83fbcf1d25fa9c8404de5df41
  languageName: node
  linkType: hard

"loader-utils@npm:^3.2.0":
  version: 3.3.1
  resolution: "loader-utils@npm:3.3.1"
  checksum: d35808e081635e5bc50228a52ed79f83e2c82bd8f7578818c12b1b4cf0b7f409d72d9b93a683ec36b9eaa93346693d3f3c8380183ba2ff81599b0829d685de39
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: cb9227612f71b83e42de93eccf1232feeb25e705bdb19ba26c04f91e885bfd3dd5c517c4a97137658190581d3493ea3973072ca010aab7e301046d90740393d1
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 9ff3942feeccffa4f1fafa88d32f0d24fdc62fd15ded5a74a5f950ff5f0c6f61916157246744c620173dddf38d37095a92327d5fd3861e2063e736a5c207d089
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.truncate@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.truncate@npm:4.4.2"
  checksum: b463d8a382cfb5f0e71c504dcb6f807a7bd379ff1ea216669aa42c52fc28c54e404bfbd96791aa09e6df0de2c1d7b8f1b7f4b1a61f324d38fe98bc535aeee4f5
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: a4779b57a8d0f3c441af13d9afe7ecff22dd1b8ce1129849f71d9bbc8e8ee4e46dfb4b7c28f7ad3d67481edd6e51126e4e2a6ee276e25906d10f7140187c392d
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.15, lodash@npm:^4.17.21, lodash@npm:^4.17.3":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"long@npm:^5.0.0":
  version: 5.2.3
  resolution: "long@npm:5.2.3"
  checksum: 885ede7c3de4facccbd2cacc6168bae3a02c3e836159ea4252c87b6e34d40af819824b2d4edce330bfb5c4d6e8ce3ec5864bdcf9473fa1f53a4f8225860e5897
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.3":
  version: 0.30.11
  resolution: "magic-string@npm:0.30.11"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
  checksum: e041649453c9a3f31d2e731fc10e38604d50e20d3585cd48bc7713a6e2e1a3ad3012105929ca15750d59d0a3f1904405e4b95a23b7e69dc256db3c277a73a3ca
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^10.0.4":
  version: 10.2.1
  resolution: "make-fetch-happen@npm:10.2.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^16.1.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-fetch: ^2.0.3
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^9.0.0
  checksum: 2332eb9a8ec96f1ffeeea56ccefabcb4193693597b132cd110734d50f2928842e22b84cfa1508e921b8385cdfd06dda9ad68645fed62b50fff629a580f5fb72c
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^13.0.0":
  version: 13.0.1
  resolution: "make-fetch-happen@npm:13.0.1"
  dependencies:
    "@npmcli/agent": ^2.0.0
    cacache: ^18.0.0
    http-cache-semantics: ^4.1.1
    is-lambda: ^1.0.1
    minipass: ^7.0.2
    minipass-fetch: ^3.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    proc-log: ^4.2.0
    promise-retry: ^2.0.1
    ssri: ^10.0.0
  checksum: 5c9fad695579b79488fa100da05777213dd9365222f85e4757630f8dd2a21a79ddd3206c78cfd6f9b37346819681782b67900ac847a57cf04190f52dda5343fd
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^9.1.0":
  version: 9.1.0
  resolution: "make-fetch-happen@npm:9.1.0"
  dependencies:
    agentkeepalive: ^4.1.3
    cacache: ^15.2.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^4.0.1
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^6.0.0
    minipass: ^3.1.3
    minipass-collect: ^1.0.2
    minipass-fetch: ^1.3.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.2
    promise-retry: ^2.0.1
    socks-proxy-agent: ^6.0.0
    ssri: ^8.0.0
  checksum: 0eb371c85fdd0b1584fcfdf3dc3c62395761b3c14658be02620c310305a9a7ecf1617a5e6fb30c1d081c5c8aaf177fa133ee225024313afabb7aa6a10f1e3d04
  languageName: node
  linkType: hard

"map-obj@npm:^1.0.0":
  version: 1.0.1
  resolution: "map-obj@npm:1.0.1"
  checksum: 9949e7baec2a336e63b8d4dc71018c117c3ce6e39d2451ccbfd3b8350c547c4f6af331a4cbe1c83193d7c6b786082b6256bde843db90cb7da2a21e8fcc28afed
  languageName: node
  linkType: hard

"map-obj@npm:^4.0.0, map-obj@npm:^4.1.0":
  version: 4.3.0
  resolution: "map-obj@npm:4.3.0"
  checksum: fbc554934d1a27a1910e842bc87b177b1a556609dd803747c85ece420692380827c6ae94a95cce4407c054fa0964be3bf8226f7f2cb2e9eeee432c7c1985684e
  languageName: node
  linkType: hard

"markdown-it@npm:^14.0.0":
  version: 14.1.0
  resolution: "markdown-it@npm:14.1.0"
  dependencies:
    argparse: ^2.0.1
    entities: ^4.4.0
    linkify-it: ^5.0.0
    mdurl: ^2.0.0
    punycode.js: ^2.3.1
    uc.micro: ^2.1.0
  bin:
    markdown-it: bin/markdown-it.mjs
  checksum: 07296b45ebd0b13a55611a24d1b1ad002c6729ec54f558f597846994b0b7b1de79d13cd99ff3e7b6e9e027f36b63125cdcf69174da294ecabdd4e6b9fff39e5d
  languageName: node
  linkType: hard

"mathml-tag-names@npm:^2.1.3":
  version: 2.1.3
  resolution: "mathml-tag-names@npm:2.1.3"
  checksum: 1201a25a137d6b9e328facd67912058b8b45b19a6c4cc62641c9476195da28a275ca6e0eca070af5378b905c2b11abc1114676ba703411db0b9ce007de921ad0
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 9d0128ed425a89f4cba8f787dca27ad9408b5cb1b220af2d938e2a0629d17d879a34d2cb19318bdb26c3f14c77dd5dfbae67211f5caaf07b61b1f2c5c8c7dc16
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: d6ac5ac7439a1607df44b22738ecf83f48e66a0874e4482d6424a61c52da5cde5750f1d1229b6f5fa1b80a492be89465390da685b11f97d62b8adcc6e88189aa
  languageName: node
  linkType: hard

"mdurl@npm:^2.0.0":
  version: 2.0.0
  resolution: "mdurl@npm:2.0.0"
  checksum: 880bc289ef668df0bb34c5b2b5aaa7b6ea755052108cdaf4a5e5968ad01cf27e74927334acc9ebcc50a8628b65272ae6b1fd51fae1330c130e261c0466e1a3b2
  languageName: node
  linkType: hard

"meow@npm:^10.1.5":
  version: 10.1.5
  resolution: "meow@npm:10.1.5"
  dependencies:
    "@types/minimist": ^1.2.2
    camelcase-keys: ^7.0.0
    decamelize: ^5.0.0
    decamelize-keys: ^1.1.0
    hard-rejection: ^2.1.0
    minimist-options: 4.1.0
    normalize-package-data: ^3.0.2
    read-pkg-up: ^8.0.0
    redent: ^4.0.0
    trim-newlines: ^4.0.2
    type-fest: ^1.2.2
    yargs-parser: ^20.2.9
  checksum: dd5f0caa4af18517813547dc66741dcbf52c4c23def5062578d39b11189fd9457aee5c1f2263a5cd6592a465023df8357e8ac876b685b64dbcf545e3f66c23a7
  languageName: node
  linkType: hard

"meow@npm:^9.0.0":
  version: 9.0.0
  resolution: "meow@npm:9.0.0"
  dependencies:
    "@types/minimist": ^1.2.0
    camelcase-keys: ^6.2.2
    decamelize: ^1.2.0
    decamelize-keys: ^1.1.0
    hard-rejection: ^2.1.0
    minimist-options: 4.1.0
    normalize-package-data: ^3.0.0
    read-pkg-up: ^7.0.1
    redent: ^3.0.0
    trim-newlines: ^3.0.0
    type-fest: ^0.18.0
    yargs-parser: ^20.2.3
  checksum: 99799c47247f4daeee178e3124f6ef6f84bde2ba3f37652865d5d8f8b8adcf9eedfc551dd043e2455cd8206545fd848e269c0c5ab6b594680a0ad4d3617c9639
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.5":
  version: 4.0.7
  resolution: "micromatch@npm:4.0.7"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 3cde047d70ad80cf60c787b77198d680db3b8c25b23feb01de5e2652205d9c19f43bd81882f69a0fd1f0cde6a7a122d774998aad3271ddb1b8accf8a0f480cf7
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"min-indent@npm:^1.0.0, min-indent@npm:^1.0.1":
  version: 1.0.1
  resolution: "min-indent@npm:1.0.1"
  checksum: bfc6dd03c5eaf623a4963ebd94d087f6f4bbbfd8c41329a7f09706b0cb66969c4ddd336abeb587bc44bc6f08e13bf90f0b374f9d71f9f01e04adc2cd6f083ef1
  languageName: node
  linkType: hard

"minimatch@npm:9.0.3":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 253487976bf485b612f16bf57463520a14f512662e592e95c571afdab1442a6a6864b6c88f248ce6fc4ff0b6de04ac7aa6c8bb51e868e99d1d65eb0658a708b5
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 7564208ef81d7065a370f788d337cd80a689e981042cb9a1d0e6580b6c6a8c9279eba80010516e258835a988363f99f54a6f711a315089b8b42694f5da9d0d77
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimatch@npm:~3.0.2":
  version: 3.0.8
  resolution: "minimatch@npm:3.0.8"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: 850cca179cad715133132693e6963b0db64ab0988c4d211415b087fc23a3e46321e2c5376a01bf5623d8782aba8bdf43c571e2e902e51fdce7175c7215c29f8b
  languageName: node
  linkType: hard

"minimist-options@npm:4.1.0":
  version: 4.1.0
  resolution: "minimist-options@npm:4.1.0"
  dependencies:
    arrify: ^1.0.1
    is-plain-obj: ^1.1.0
    kind-of: ^6.0.3
  checksum: 8c040b3068811e79de1140ca2b708d3e203c8003eb9a414c1ab3cd467fc5f17c9ca02a5aef23bedc51a7f8bfbe77f87e9a7e31ec81fba304cda675b019496f4e
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^1.3.2":
  version: 1.4.1
  resolution: "minipass-fetch@npm:1.4.1"
  dependencies:
    encoding: ^0.1.12
    minipass: ^3.1.0
    minipass-sized: ^1.0.3
    minizlib: ^2.0.0
  dependenciesMeta:
    encoding:
      optional: true
  checksum: ec93697bdb62129c4e6c0104138e681e30efef8c15d9429dd172f776f83898471bc76521b539ff913248cc2aa6d2b37b652c993504a51cc53282563640f29216
  languageName: node
  linkType: hard

"minipass-fetch@npm:^2.0.3":
  version: 2.1.2
  resolution: "minipass-fetch@npm:2.1.2"
  dependencies:
    encoding: ^0.1.13
    minipass: ^3.1.6
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3f216be79164e915fc91210cea1850e488793c740534985da017a4cbc7a5ff50506956d0f73bb0cb60e4fe91be08b6b61ef35101706d3ef5da2c8709b5f08f91
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.5
  resolution: "minipass-fetch@npm:3.0.5"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 8047d273236157aab27ab7cd8eab7ea79e6ecd63e8f80c3366ec076cb9a0fed550a6935bab51764369027c414647fd8256c2a20c5445fb250c483de43350de83
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.2, minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.0, minipass@npm:^3.1.1, minipass@npm:^3.1.3, minipass@npm:^3.1.6":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^2.0.0, minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mkdirp@npm:0.3.0":
  version: 0.3.0
  resolution: "mkdirp@npm:0.3.0"
  checksum: 3ec9cda8bd89b64892728e5092bc79e88382e444d4bbde040c2fb8d7034dc70682cfdd729e93241fd5243d2397324c420ef68c717d806db51bf96c0fc80f4b1d
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"moment@npm:^2.29.4":
  version: 2.30.1
  resolution: "moment@npm:2.30.1"
  checksum: 859236bab1e88c3e5802afcf797fc801acdbd0ee509d34ea3df6eea21eb6bcc2abd4ae4e4e64aa7c986aa6cba563c6e62806218e6412a765010712e5fa121ba6
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:^2.0.0":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mute-stream@npm:1.0.0":
  version: 1.0.0
  resolution: "mute-stream@npm:1.0.0"
  checksum: 36fc968b0e9c9c63029d4f9dc63911950a3bdf55c9a87f58d3a266289b67180201cade911e7699f8b2fa596b34c9db43dad37649e3f7fdd13c3bb9edb0017ee7
  languageName: node
  linkType: hard

"nan@npm:^2.17.0":
  version: 2.20.0
  resolution: "nan@npm:2.20.0"
  dependencies:
    node-gyp: latest
  checksum: eb09286e6c238a3582db4d88c875db73e9b5ab35f60306090acd2f3acae21696c9b653368b4a0e32abcef64ee304a923d6223acaddd16169e5eaaf5c508fb533
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6, nanoid@npm:^3.3.7":
  version: 3.3.7
  resolution: "nanoid@npm:3.3.7"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: d36c427e530713e4ac6567d488b489a36582ef89da1d6d4e3b87eded11eb10d7042a877958c6f104929809b2ab0bafa17652b076cdf84324aa75b30b722204f2
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.2, negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"next@npm:14.2.3":
  version: 14.2.3
  resolution: "next@npm:14.2.3"
  dependencies:
    "@next/env": 14.2.3
    "@next/swc-darwin-arm64": 14.2.3
    "@next/swc-darwin-x64": 14.2.3
    "@next/swc-linux-arm64-gnu": 14.2.3
    "@next/swc-linux-arm64-musl": 14.2.3
    "@next/swc-linux-x64-gnu": 14.2.3
    "@next/swc-linux-x64-musl": 14.2.3
    "@next/swc-win32-arm64-msvc": 14.2.3
    "@next/swc-win32-ia32-msvc": 14.2.3
    "@next/swc-win32-x64-msvc": 14.2.3
    "@swc/helpers": 0.5.5
    busboy: 1.6.0
    caniuse-lite: ^1.0.30001579
    graceful-fs: ^4.2.11
    postcss: 8.4.31
    styled-jsx: 5.1.1
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.41.2
    react: ^18.2.0
    react-dom: ^18.2.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-ia32-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: d34ea63adf23fe46efebe2a9c536c9127c0ee006d74c60d6d23aecbef650798c976b27c17910ca585f3bb1223b10924cb429b9ce930f3074aee1170d1519dccc
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.1, node-fetch@npm:^2.6.13, node-fetch@npm:^2.6.9":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-gyp@npm:^8.4.1":
  version: 8.4.1
  resolution: "node-gyp@npm:8.4.1"
  dependencies:
    env-paths: ^2.2.0
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^9.1.0
    nopt: ^5.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 341710b5da39d3660e6a886b37e210d33f8282047405c2e62c277bcc744c7552c5b8b972ebc3a7d5c2813794e60cc48c3ebd142c46d6e0321db4db6c92dd0355
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 10.2.0
  resolution: "node-gyp@npm:10.2.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^13.0.0
    nopt: ^7.0.0
    proc-log: ^4.1.0
    semver: ^7.3.5
    tar: ^6.2.1
    which: ^4.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 0233759d8c19765f7fdc259a35eb046ad86c3d09e22f7384613ae2b89647dd27fcf833fdf5293d9335041e91f9b1c539494225959cdb312a5c8080b7534b926f
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.18":
  version: 2.0.18
  resolution: "node-releases@npm:2.0.18"
  checksum: ef55a3d853e1269a6d6279b7692cd6ff3e40bc74947945101138745bfdc9a5edabfe72cb19a31a8e45752e1910c4c65c77d931866af6357f242b172b7283f5b3
  languageName: node
  linkType: hard

"node-sass@npm:^9.0.0":
  version: 9.0.0
  resolution: "node-sass@npm:9.0.0"
  dependencies:
    async-foreach: ^0.1.3
    chalk: ^4.1.2
    cross-spawn: ^7.0.3
    gaze: ^1.0.0
    get-stdin: ^4.0.1
    glob: ^7.0.3
    lodash: ^4.17.15
    make-fetch-happen: ^10.0.4
    meow: ^9.0.0
    nan: ^2.17.0
    node-gyp: ^8.4.1
    sass-graph: ^4.0.1
    stdout-stream: ^1.4.0
    true-case-path: ^2.2.1
  bin:
    node-sass: bin/node-sass
  checksum: b15fa76b1564c37d65cde7556731e3c09b49c74a6919cd5cff6f71ddbe454bd1ad9e458f5f02f0f81f43919b8755b5f56cf657fa4e32a0a2644a48fbc07147bb
  languageName: node
  linkType: hard

"nopt@npm:1.0.10":
  version: 1.0.10
  resolution: "nopt@npm:1.0.10"
  dependencies:
    abbrev: 1
  bin:
    nopt: ./bin/nopt.js
  checksum: f62575aceaa3be43f365bf37a596b89bbac2e796b001b6d2e2a85c2140a4e378ff919e2753ccba959c4fd344776fc88c29b393bc167fa939fb1513f126f4cd45
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: 1
  bin:
    nopt: bin/nopt.js
  checksum: d35fdec187269503843924e0114c0c6533fb54bbf1620d0f28b4b60ba01712d6687f62565c55cc20a504eff0fbe5c63e22340c3fad549ad40469ffb611b04f2f
  languageName: node
  linkType: hard

"nopt@npm:^7.0.0":
  version: 7.2.1
  resolution: "nopt@npm:7.2.1"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 6fa729cc77ce4162cfad8abbc9ba31d4a0ff6850c3af61d59b505653bef4781ec059f8890ecfe93ee8aa0c511093369cca88bfc998101616a2904e715bbbb7c9
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 7999112efc35a6259bc22db460540cae06564aa65d0271e3bdfa86876d08b0e578b7b5b0028ee61b23f1cae9fc0e7847e4edc0948d3068a39a2a82853efc8499
  languageName: node
  linkType: hard

"normalize-package-data@npm:^3.0.0, normalize-package-data@npm:^3.0.2":
  version: 3.0.3
  resolution: "normalize-package-data@npm:3.0.3"
  dependencies:
    hosted-git-info: ^4.0.1
    is-core-module: ^2.5.0
    semver: ^7.3.4
    validate-npm-package-license: ^3.0.1
  checksum: bbcee00339e7c26fdbc760f9b66d429258e2ceca41a5df41f5df06cc7652de8d82e8679ff188ca095cad8eff2b6118d7d866af2b68400f74602fbcbce39c160a
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 4a4944631173e7d521d6b80e4c85ccaeceb2870f315584fa30121f505a6dfd86439c5e3fdd8cd9e0e291290c41d0c3599f0cb12ab356722ed242584c30348e50
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 80b4904bb3857c52cc1bfd0b52c0352532ca12ed3b8a6ff06a90cd209dfda1b95cee059a7625eb9da29537027f68ac4619363491eedb2f5d3dddbba97494fd6c
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 28d476ee6c1049d68368c0dc922e7225e3b5600c3ede88fade8052837f9ed342625fdaa84a6209302587c8ddd9b664f71f0759833cbdb3a4cf81344057e63c63
  languageName: node
  linkType: hard

"orderedmap@npm:^2.0.0":
  version: 2.1.1
  resolution: "orderedmap@npm:2.1.1"
  checksum: 082cf970b0b66d1c5a904b07880534092ce8a2f2eea7a52cf111f6c956210fa88226c13866aef4d22a3abe56924f21ead12f7ee8c1dfaf2f63d897a4e7c23328
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 93a654c53dc805dd5b5891bab16eb0ea46db8f66c4bfd99336ae929323b1af2b70a8b0654f8f1eae924b2b73d037031366d645f1fd18b3d30cbd15950cc4b1d4
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-queue@npm:^6.6.2":
  version: 6.6.2
  resolution: "p-queue@npm:6.6.2"
  dependencies:
    eventemitter3: ^4.0.4
    p-timeout: ^3.2.0
  checksum: 832642fcc4ab6477b43e6d7c30209ab10952969ed211c6d6f2931be8a4f9935e3578c72e8cce053dc34f2eb6941a408a2c516a54904e989851a1a209cf19761c
  languageName: node
  linkType: hard

"p-timeout@npm:^3.2.0":
  version: 3.2.0
  resolution: "p-timeout@npm:3.2.0"
  dependencies:
    p-finally: ^1.0.0
  checksum: 3dd0eaa048780a6f23e5855df3dd45c7beacff1f820476c1d0d1bcd6648e3298752ba2c877aa1c92f6453c7dd23faaf13d9f5149fc14c0598a142e2c5e8d649c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.0
  resolution: "package-json-from-dist@npm:1.0.0"
  checksum: ac706ec856a5a03f5261e4e48fa974f24feb044d51f84f8332e2af0af04fbdbdd5bbbfb9cbbe354190409bc8307c83a9e38c6672c3c8855f709afb0006a009ea
  languageName: node
  linkType: hard

"papaparse@npm:^5.4.1":
  version: 5.4.1
  resolution: "papaparse@npm:5.4.1"
  checksum: fc9e52f7158dca3517c229e3309065b1ab5da6c7194572fba4f31ff138bc43e3c91182cc40365cc828f97fe10d0aca416068fd731661058bea0f69ddb84a411a
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0, parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.0.1":
  version: 1.0.1
  resolution: "picocolors@npm:1.0.1"
  checksum: fa68166d1f56009fc02a34cdfd112b0dd3cf1ef57667ac57281f714065558c01828cdf4f18600ad6851cbe0093952ed0660b1e0156bddf2184b6aaf5817553a5
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.2, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pify@npm:^5.0.0":
  version: 5.0.0
  resolution: "pify@npm:5.0.0"
  checksum: 443e3e198ad6bfa8c0c533764cf75c9d5bc976387a163792fb553ffe6ce923887cf14eebf5aea9b7caa8eab930da8c33612990ae85bd8c2bc18bedb9eae94ecb
  languageName: node
  linkType: hard

"postcss-calc@npm:^8.2.3":
  version: 8.2.4
  resolution: "postcss-calc@npm:8.2.4"
  dependencies:
    postcss-selector-parser: ^6.0.9
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.2
  checksum: 314b4cebb0c4ed0cf8356b4bce71eca78f5a7842e6a3942a3bba49db168d5296b2bd93c3f735ae1c616f2651d94719ade33becc03c73d2d79c7394fb7f73eabb
  languageName: node
  linkType: hard

"postcss-colormin@npm:^5.3.1":
  version: 5.3.1
  resolution: "postcss-colormin@npm:5.3.1"
  dependencies:
    browserslist: ^4.21.4
    caniuse-api: ^3.0.0
    colord: ^2.9.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: e5778baab30877cd1f51e7dc9d2242a162aeca6360a52956acd7f668c5bc235c2ccb7e4df0370a804d65ebe00c5642366f061db53aa823f9ed99972cebd16024
  languageName: node
  linkType: hard

"postcss-convert-values@npm:^5.1.3":
  version: 5.1.3
  resolution: "postcss-convert-values@npm:5.1.3"
  dependencies:
    browserslist: ^4.21.4
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: df48cdaffabf9737f9cfdc58a3dc2841cf282506a7a944f6c70236cff295d3a69f63de6e0935eeb8a9d3f504324e5b4e240abc29e21df9e35a02585d3060aeb5
  languageName: node
  linkType: hard

"postcss-discard-comments@npm:^5.1.2":
  version: 5.1.2
  resolution: "postcss-discard-comments@npm:5.1.2"
  peerDependencies:
    postcss: ^8.2.15
  checksum: abfd064ebc27aeaf5037643dd51ffaff74d1fa4db56b0523d073ace4248cbb64ffd9787bd6924b0983a9d0bd0e9bf9f10d73b120e50391dc236e0d26c812fa2a
  languageName: node
  linkType: hard

"postcss-discard-duplicates@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-discard-duplicates@npm:5.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 88d6964201b1f4ed6bf7a32cefe68e86258bb6e42316ca01d9b32bdb18e7887d02594f89f4a2711d01b51ea6e3fcca8c54be18a59770fe5f4521c61d3eb6ca35
  languageName: node
  linkType: hard

"postcss-discard-empty@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-discard-empty@npm:5.1.1"
  peerDependencies:
    postcss: ^8.2.15
  checksum: 970adb12fae5c214c0768236ad9a821552626e77dedbf24a8213d19cc2c4a531a757cd3b8cdd3fc22fb1742471b8692a1db5efe436a71236dec12b1318ee8ff4
  languageName: node
  linkType: hard

"postcss-discard-overridden@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-discard-overridden@npm:5.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: d64d4a545aa2c81b22542895cfcddc787d24119f294d35d29b0599a1c818b3cc51f4ee80b80f5a0a09db282453dd5ac49f104c2117cc09112d0ac9b40b499a41
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: ^4.0.0
    read-cache: ^1.0.0
    resolve: ^1.1.7
  peerDependencies:
    postcss: ^8.0.0
  checksum: 7bd04bd8f0235429009d0022cbf00faebc885de1d017f6d12ccb1b021265882efc9302006ba700af6cab24c46bfa2f3bc590be3f9aee89d064944f171b04e2a3
  languageName: node
  linkType: hard

"postcss-load-config@npm:^3.0.0":
  version: 3.1.4
  resolution: "postcss-load-config@npm:3.1.4"
  dependencies:
    lilconfig: ^2.0.5
    yaml: ^1.10.2
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 1c589504c2d90b1568aecae8238ab993c17dba2c44f848a8f13619ba556d26a1c09644d5e6361b5784e721e94af37b604992f9f3dc0483e687a0cc1cc5029a34
  languageName: node
  linkType: hard

"postcss-merge-longhand@npm:^5.1.7":
  version: 5.1.7
  resolution: "postcss-merge-longhand@npm:5.1.7"
  dependencies:
    postcss-value-parser: ^4.2.0
    stylehacks: ^5.1.1
  peerDependencies:
    postcss: ^8.2.15
  checksum: 81c3fc809f001b9b71a940148e242bdd6e2d77713d1bfffa15eb25c1f06f6648d5e57cb21645746d020a2a55ff31e1740d2b27900442913a9d53d8a01fb37e1b
  languageName: node
  linkType: hard

"postcss-merge-rules@npm:^5.1.4":
  version: 5.1.4
  resolution: "postcss-merge-rules@npm:5.1.4"
  dependencies:
    browserslist: ^4.21.4
    caniuse-api: ^3.0.0
    cssnano-utils: ^3.1.0
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 8ab6a569babe6cb412d6612adee74f053cea7edb91fa013398515ab36754b1fec830d68782ed8cdfb44cffdc6b78c79eab157bff650f428aa4460d3f3857447e
  languageName: node
  linkType: hard

"postcss-minify-font-values@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-minify-font-values@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 35e858fa41efa05acdeb28f1c76579c409fdc7eabb1744c3bd76e895bb9fea341a016746362a67609688ab2471f587202b9a3e14ea28ad677754d663a2777ece
  languageName: node
  linkType: hard

"postcss-minify-gradients@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-minify-gradients@npm:5.1.1"
  dependencies:
    colord: ^2.9.1
    cssnano-utils: ^3.1.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 27354072a07c5e6dab36731103b94ca2354d4ed3c5bc6aacfdf2ede5a55fa324679d8fee5450800bc50888dbb5e9ed67569c0012040c2be128143d0cebb36d67
  languageName: node
  linkType: hard

"postcss-minify-params@npm:^5.1.4":
  version: 5.1.4
  resolution: "postcss-minify-params@npm:5.1.4"
  dependencies:
    browserslist: ^4.21.4
    cssnano-utils: ^3.1.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: bd63e2cc89edcf357bb5c2a16035f6d02ef676b8cede4213b2bddd42626b3d428403849188f95576fc9f03e43ebd73a29bf61d33a581be9a510b13b7f7f100d5
  languageName: node
  linkType: hard

"postcss-minify-selectors@npm:^5.2.1":
  version: 5.2.1
  resolution: "postcss-minify-selectors@npm:5.2.1"
  dependencies:
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6fdbc84f99a60d56b43df8930707da397775e4c36062a106aea2fd2ac81b5e24e584a1892f4baa4469fa495cb87d1422560eaa8f6c9d500f9f0b691a5f95bab5
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.1.0
  resolution: "postcss-modules-extract-imports@npm:3.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: b9192e0f4fb3d19431558be6f8af7ca45fc92baaad9b2778d1732a5880cd25c3df2074ce5484ae491e224f0d21345ffc2d419bd51c25b019af76d7a7af88c17f
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.0":
  version: 4.0.5
  resolution: "postcss-modules-local-by-default@npm:4.0.5"
  dependencies:
    icss-utils: ^5.0.0
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: ca9b01f4a0a3dfb33e016299e2dfb7e85c3123292f7aec2efc0c6771b9955648598bfb4c1561f7ee9732fb27fb073681233661b32eef98baab43743f96735452
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.0.0":
  version: 3.2.0
  resolution: "postcss-modules-scope@npm:3.2.0"
  dependencies:
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.1.0
  checksum: 2ffe7e98c1fa993192a39c8dd8ade93fc4f59fbd1336ce34fcedaee0ee3bafb29e2e23fb49189256895b30e4f21af661c6a6a16ef7b17ae2c859301e4a4459ae
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: f7f2cdf14a575b60e919ad5ea52fed48da46fe80db2733318d71d523fc87db66c835814940d7d05b5746b0426e44661c707f09bdb83592c16aea06e859409db6
  languageName: node
  linkType: hard

"postcss-modules@npm:^4.0.0":
  version: 4.3.1
  resolution: "postcss-modules@npm:4.3.1"
  dependencies:
    generic-names: ^4.0.0
    icss-replace-symbols: ^1.1.0
    lodash.camelcase: ^4.3.0
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.0
    postcss-modules-scope: ^3.0.0
    postcss-modules-values: ^4.0.0
    string-hash: ^1.1.1
  peerDependencies:
    postcss: ^8.0.0
  checksum: fa592183bb3d96c4aaf535e3b9b3bcfc54274cbb5b337616543c24ec68cd56675e9fd8aabf994e627513af628d090e43d2f1f4928ff6cdd4b9d3b1ba3fce4d42
  languageName: node
  linkType: hard

"postcss-normalize-charset@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-charset@npm:5.1.0"
  peerDependencies:
    postcss: ^8.2.15
  checksum: e79d92971fc05b8b3c9b72f3535a574e077d13c69bef68156a0965f397fdf157de670da72b797f57b0e3bac8f38155b5dd1735ecab143b9cc4032d72138193b4
  languageName: node
  linkType: hard

"postcss-normalize-display-values@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-display-values@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: b6eb7b9b02c3bdd62bbc54e01e2b59733d73a1c156905d238e178762962efe0c6f5104544da39f32cade8a4fb40f10ff54b63a8ebfbdff51e8780afb9fbdcf86
  languageName: node
  linkType: hard

"postcss-normalize-positions@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-positions@npm:5.1.1"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: d9afc233729c496463c7b1cdd06732469f401deb387484c3a2422125b46ec10b4af794c101f8c023af56f01970b72b535e88373b9058ecccbbf88db81662b3c4
  languageName: node
  linkType: hard

"postcss-normalize-repeat-style@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-repeat-style@npm:5.1.1"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 2c6ad2b0ae10a1fda156b948c34f78c8f1e185513593de4d7e2480973586675520edfec427645fa168c337b0a6b3ceca26f92b96149741ca98a9806dad30d534
  languageName: node
  linkType: hard

"postcss-normalize-string@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-string@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6e549c6e5b2831e34c7bdd46d8419e2278f6af1d5eef6d26884a37c162844e60339340c57e5e06058cdbe32f27fc6258eef233e811ed2f71168ef2229c236ada
  languageName: node
  linkType: hard

"postcss-normalize-timing-functions@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-timing-functions@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: da550f50e90b0b23e17b67449a7d1efd1aa68288e66d4aa7614ca6f5cc012896be1972b7168eee673d27da36504faccf7b9f835c0f7e81243f966a42c8c030aa
  languageName: node
  linkType: hard

"postcss-normalize-unicode@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-unicode@npm:5.1.1"
  dependencies:
    browserslist: ^4.21.4
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 4c24d26cc9f4b19a9397db4e71dd600dab690f1de8e14a3809e2aa1452dbc3791c208c38a6316bbc142f29e934fdf02858e68c94038c06174d78a4937e0f273c
  languageName: node
  linkType: hard

"postcss-normalize-url@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-normalize-url@npm:5.1.0"
  dependencies:
    normalize-url: ^6.0.1
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 3bd4b3246d6600230bc827d1760b24cb3101827ec97570e3016cbe04dc0dd28f4dbe763245d1b9d476e182c843008fbea80823061f1d2219b96f0d5c724a24c0
  languageName: node
  linkType: hard

"postcss-normalize-whitespace@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-normalize-whitespace@npm:5.1.1"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 12d8fb6d1c1cba208cc08c1830959b7d7ad447c3f5581873f7e185f99a9a4230c43d3af21ca12c818e4690a5085a95b01635b762ad4a7bef69d642609b4c0e19
  languageName: node
  linkType: hard

"postcss-ordered-values@npm:^5.1.3":
  version: 5.1.3
  resolution: "postcss-ordered-values@npm:5.1.3"
  dependencies:
    cssnano-utils: ^3.1.0
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 6f3ca85b6ceffc68aadaf319d9ee4c5ac16d93195bf8cba2d1559b631555ad61941461cda6d3909faab86e52389846b2b36345cff8f0c3f4eb345b1b8efadcf9
  languageName: node
  linkType: hard

"postcss-reduce-initial@npm:^5.1.2":
  version: 5.1.2
  resolution: "postcss-reduce-initial@npm:5.1.2"
  dependencies:
    browserslist: ^4.21.4
    caniuse-api: ^3.0.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 55db697f85231a81f1969d54c894e4773912d9ddb914f9b03d2e73abc4030f2e3bef4d7465756d0c1acfcc2c2d69974bfb50a972ab27546a7d68b5a4fc90282b
  languageName: node
  linkType: hard

"postcss-reduce-transforms@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-reduce-transforms@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: 0c6af2cba20e3ff63eb9ad045e634ddfb9c3e5c0e614c020db2a02f3aa20632318c4ede9e0c995f9225d9a101e673de91c0a6e10bb2fa5da6d6c75d15a55882f
  languageName: node
  linkType: hard

"postcss-resolve-nested-selector@npm:^0.1.1":
  version: 0.1.4
  resolution: "postcss-resolve-nested-selector@npm:0.1.4"
  checksum: 8de7abd1ae129233480ac123be243e2a722a4bb73fa54fb09cbbe6f10074fac960b9caadad8bc658982b96934080bd7b7f01b622ca7d5d78a25dc9c0532b17cb
  languageName: node
  linkType: hard

"postcss-safe-parser@npm:^6.0.0":
  version: 6.0.0
  resolution: "postcss-safe-parser@npm:6.0.0"
  peerDependencies:
    postcss: ^8.3.3
  checksum: 06c733eaad83a3954367e7ee02ddfe3796e7a44d4299ccf9239f40964a4daac153c7d77613f32964b5a86c0c6c2f6167738f31d578b73b17cb69d0c4446f0ebe
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.13, postcss-selector-parser@npm:^6.0.2, postcss-selector-parser@npm:^6.0.4, postcss-selector-parser@npm:^6.0.5, postcss-selector-parser@npm:^6.0.9":
  version: 6.1.1
  resolution: "postcss-selector-parser@npm:6.1.1"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: 1c6a5adfc3c19c6e1e7d94f8addb89a5166fcca72c41f11713043d381ecbe82ce66360c5524e904e17b54f7fc9e6a077994ff31238a456bc7320c3e02e88d92e
  languageName: node
  linkType: hard

"postcss-svgo@npm:^5.1.0":
  version: 5.1.0
  resolution: "postcss-svgo@npm:5.1.0"
  dependencies:
    postcss-value-parser: ^4.2.0
    svgo: ^2.7.0
  peerDependencies:
    postcss: ^8.2.15
  checksum: d86eb5213d9f700cf5efe3073799b485fb7cacae0c731db3d7749c9c2b1c9bc85e95e0baeca439d699ff32ea24815fc916c4071b08f67ed8219df229ce1129bd
  languageName: node
  linkType: hard

"postcss-unique-selectors@npm:^5.1.1":
  version: 5.1.1
  resolution: "postcss-unique-selectors@npm:5.1.1"
  dependencies:
    postcss-selector-parser: ^6.0.5
  peerDependencies:
    postcss: ^8.2.15
  checksum: 637e7b786e8558265775c30400c54b6b3b24d4748923f4a39f16a65fd0e394f564ccc9f0a1d3c0e770618a7637a7502ea1d0d79f731d429cb202255253c23278
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1d8611341b073143ad90486fcdfeab49edd243377b1f51834dc4f6d028e82ce5190e4f11bb2633276864503654fb7cab28e67abdc0fbf9d1f88cad4a0ff0beea
  languageName: node
  linkType: hard

"postcss@npm:8.4.38":
  version: 8.4.38
  resolution: "postcss@npm:8.4.38"
  dependencies:
    nanoid: ^3.3.7
    picocolors: ^1.0.0
    source-map-js: ^1.2.0
  checksum: 649f9e60a763ca4b5a7bbec446a069edf07f057f6d780a5a0070576b841538d1ecf7dd888f2fbfd1f76200e26c969e405aeeae66332e6927dbdc8bdcb90b9451
  languageName: node
  linkType: hard

"postcss@npm:^8, postcss@npm:^8.4.28, postcss@npm:^8.4.5":
  version: 8.4.40
  resolution: "postcss@npm:8.4.40"
  dependencies:
    nanoid: ^3.3.7
    picocolors: ^1.0.1
    source-map-js: ^1.2.0
  checksum: afd0cc49d2169dcd96c0f17e155c5d75de048956306a3017f1cfa6a7d66b941592245bed20f7796ceeccb2d8967749b623be2c7b010a74f67ea10fb5bdb8ba28
  languageName: node
  linkType: hard

"preact@npm:^10.10.0":
  version: 10.23.1
  resolution: "preact@npm:10.23.1"
  checksum: fb7d28c7da1442ed80435444326fa2dcb563d4af05f631611fff8c08fb46a6f209d1a780aa7638a0bc2ac4d7a351f3304ff97bebfd6f5ee5bc4feb880d84a34f
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier@npm:^3.3.3":
  version: 3.3.3
  resolution: "prettier@npm:3.3.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: bc8604354805acfdde6106852d14b045bb20827ad76a5ffc2455b71a8257f94de93f17f14e463fe844808d2ccc87248364a5691488a3304f1031326e62d9276e
  languageName: node
  linkType: hard

"proc-log@npm:^4.1.0, proc-log@npm:^4.2.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 98f6cd012d54b5334144c5255ecb941ee171744f45fca8b43b58ae5a0c1af07352475f481cadd9848e7f0250376ee584f6aa0951a856ff8f021bdfbff4eb33fc
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"promise.series@npm:^0.2.0":
  version: 0.2.0
  resolution: "promise.series@npm:0.2.0"
  checksum: 26b5956b5463d032b43d39fd8d34fdacf453ed3352462eed9626494a11d44beb385f86d6544dd12e51482a6ca8f303e0dfdee8653db4703213ba27dd2234754a
  languageName: node
  linkType: hard

"prop-types@npm:^15.7.2":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"prosemirror-changeset@npm:^2.2.1":
  version: 2.2.1
  resolution: "prosemirror-changeset@npm:2.2.1"
  dependencies:
    prosemirror-transform: ^1.0.0
  checksum: e1a661d3508453a8d50ee3079c098ac89c953665f30bed00f89fab940aee0515f0ad4584b4ca8cea218cdaa39808c1392698eecd92072647a70551cb316050c4
  languageName: node
  linkType: hard

"prosemirror-collab@npm:^1.3.1":
  version: 1.3.1
  resolution: "prosemirror-collab@npm:1.3.1"
  dependencies:
    prosemirror-state: ^1.0.0
  checksum: 674fd2227d2070b6b28d1982748c4e60d5e637c460a160d732e398f131ba960500476f745aff7de9426d2cc9bbb33e33bcd2bdc56a345cb691b33c54f8ccff37
  languageName: node
  linkType: hard

"prosemirror-commands@npm:^1.0.0, prosemirror-commands@npm:^1.6.0":
  version: 1.6.2
  resolution: "prosemirror-commands@npm:1.6.2"
  dependencies:
    prosemirror-model: ^1.0.0
    prosemirror-state: ^1.0.0
    prosemirror-transform: ^1.10.2
  checksum: aeb6652e4d2d33057184a60609edff73e9921e02dd54a7451aa8071ab93d44f5a8a55a95a95b74eb68008e2d65544673cc90eed3cba2f79ab2cd7d3bd6da1def
  languageName: node
  linkType: hard

"prosemirror-dropcursor@npm:^1.8.1":
  version: 1.8.1
  resolution: "prosemirror-dropcursor@npm:1.8.1"
  dependencies:
    prosemirror-state: ^1.0.0
    prosemirror-transform: ^1.1.0
    prosemirror-view: ^1.1.0
  checksum: 9c9c58502c101548bd66d8d6ead6aceac92330e52a951b49698adaca9877245955db49b1e7abac906d50929ed3f6efb5f2ab158dbc02db2a497d75fb82d79433
  languageName: node
  linkType: hard

"prosemirror-gapcursor@npm:^1.3.2":
  version: 1.3.2
  resolution: "prosemirror-gapcursor@npm:1.3.2"
  dependencies:
    prosemirror-keymap: ^1.0.0
    prosemirror-model: ^1.0.0
    prosemirror-state: ^1.0.0
    prosemirror-view: ^1.0.0
  checksum: a1a359f9cb701417f00b330d24b70aaba48ef48a906bc1a7425de1c81c3fa67b19352c432075419ec363827006799964ab47f1ca192e25a2c4fb696e6d1db3ed
  languageName: node
  linkType: hard

"prosemirror-history@npm:^1.0.0, prosemirror-history@npm:^1.4.1":
  version: 1.4.1
  resolution: "prosemirror-history@npm:1.4.1"
  dependencies:
    prosemirror-state: ^1.2.2
    prosemirror-transform: ^1.0.0
    prosemirror-view: ^1.31.0
    rope-sequence: ^1.3.0
  checksum: 90f9bf59bc95957fabd57044f881d9a05f603771f1c3b5ef8957c25d99464af3cdfb3bec32dfe509e2ef971f1231b2f60fb33502c7adcb3a18ff4ffd3b87d753
  languageName: node
  linkType: hard

"prosemirror-inputrules@npm:^1.4.0":
  version: 1.4.0
  resolution: "prosemirror-inputrules@npm:1.4.0"
  dependencies:
    prosemirror-state: ^1.0.0
    prosemirror-transform: ^1.0.0
  checksum: b893eff382b585aa934e41a7bcbb02cd9ce5199bc6e939ef3f1629aeaf181320f2b52a0d088cb6432aa10c4536c3b5ea67a15e66ef24714a3b3f9e89d0f29ef4
  languageName: node
  linkType: hard

"prosemirror-keymap@npm:^1.0.0, prosemirror-keymap@npm:^1.1.2, prosemirror-keymap@npm:^1.2.2":
  version: 1.2.2
  resolution: "prosemirror-keymap@npm:1.2.2"
  dependencies:
    prosemirror-state: ^1.0.0
    w3c-keyname: ^2.2.0
  checksum: 85fe4fc3038499b6dabd9a16581c0ee7a4358835c200f8a6a17dbe05733ea3df1f2571b0d02c071dbd51ce32e909da3ebb9227f25434587238e89f8c9ba293f9
  languageName: node
  linkType: hard

"prosemirror-markdown@npm:^1.13.0":
  version: 1.13.1
  resolution: "prosemirror-markdown@npm:1.13.1"
  dependencies:
    "@types/markdown-it": ^14.0.0
    markdown-it: ^14.0.0
    prosemirror-model: ^1.20.0
  checksum: fb5487760ec58c80b53fda2e1095e316b0ecf9921a2cecfa71bb6e74b29e4c486fd7fa571661b26c93b64d7c218ab19180890c928aaa562322a5c9db04f9665a
  languageName: node
  linkType: hard

"prosemirror-menu@npm:^1.2.4":
  version: 1.2.4
  resolution: "prosemirror-menu@npm:1.2.4"
  dependencies:
    crelt: ^1.0.0
    prosemirror-commands: ^1.0.0
    prosemirror-history: ^1.0.0
    prosemirror-state: ^1.0.0
  checksum: 9fddd7053935e0c6129ebb7f628b5b914a12490fb4655388757485702b1f52734eb9a5ac71a7ff8a7b6f2f318268b5e87fb84d18080d20a0068f63b692de21d1
  languageName: node
  linkType: hard

"prosemirror-model@npm:^1.0.0, prosemirror-model@npm:^1.19.0, prosemirror-model@npm:^1.20.0, prosemirror-model@npm:^1.21.0, prosemirror-model@npm:^1.22.3, prosemirror-model@npm:^1.8.1":
  version: 1.23.0
  resolution: "prosemirror-model@npm:1.23.0"
  dependencies:
    orderedmap: ^2.0.0
  checksum: f5ab1593cbe09b77fcf8841442dbedf01c883da11f3c5a999f8fab93df1ce95cc5d0352c3ab424e94c1b1417a8fdaf5dd0fb3ffed0ed288d778ab3244ec2ffc3
  languageName: node
  linkType: hard

"prosemirror-schema-basic@npm:^1.2.3":
  version: 1.2.3
  resolution: "prosemirror-schema-basic@npm:1.2.3"
  dependencies:
    prosemirror-model: ^1.19.0
  checksum: 49711ec1ee65a20adec4dbaa245ba0870fd8650633af8a624d1f0ce1377822ab2e04c333155a373aeb81e0f5f96693d40973ef89e17f4469f6ee53534268114a
  languageName: node
  linkType: hard

"prosemirror-schema-list@npm:^1.4.1":
  version: 1.4.1
  resolution: "prosemirror-schema-list@npm:1.4.1"
  dependencies:
    prosemirror-model: ^1.0.0
    prosemirror-state: ^1.0.0
    prosemirror-transform: ^1.7.3
  checksum: dde0c96cc2ca1bc132ce0f2c5a3ccd73d264b1e39dce4c7da65db00282225d0e9bf3a2c08789430488e6095abc8104a377c8b7b6727d2e5f53dd14dec9f4658d
  languageName: node
  linkType: hard

"prosemirror-state@npm:^1.0.0, prosemirror-state@npm:^1.2.2, prosemirror-state@npm:^1.3.1, prosemirror-state@npm:^1.4.3":
  version: 1.4.3
  resolution: "prosemirror-state@npm:1.4.3"
  dependencies:
    prosemirror-model: ^1.0.0
    prosemirror-transform: ^1.0.0
    prosemirror-view: ^1.27.0
  checksum: 28857d935c443efae185407e2b6fe4ab481840a3609dfac344ee16eeeaebf39765207c8e525bd628d72755f9257cd51a743e543c8c9d4357b7e67ab22c9dc44c
  languageName: node
  linkType: hard

"prosemirror-tables@npm:^1.4.0":
  version: 1.6.0
  resolution: "prosemirror-tables@npm:1.6.0"
  dependencies:
    prosemirror-keymap: ^1.1.2
    prosemirror-model: ^1.8.1
    prosemirror-state: ^1.3.1
    prosemirror-transform: ^1.2.1
    prosemirror-view: ^1.13.3
  checksum: 39aa6e409cfcf268a8cc9155fd98029d115d82cdda1202866c400f7ba03de65682b7863b47e5e2941ae4bf06e2bd760eb58efa57111849ce6dc9de7375741abc
  languageName: node
  linkType: hard

"prosemirror-trailing-node@npm:^3.0.0":
  version: 3.0.0
  resolution: "prosemirror-trailing-node@npm:3.0.0"
  dependencies:
    "@remirror/core-constants": 3.0.0
    escape-string-regexp: ^4.0.0
  peerDependencies:
    prosemirror-model: ^1.22.1
    prosemirror-state: ^1.4.2
    prosemirror-view: ^1.33.8
  checksum: ba8081fb01a4be3f89f0eddbe5da245b296f0d333016791c63dbc4277a0ebcc6d261792c433210d5a78990cca4a711ca1555b2e265dabd6ab78594dd35f48268
  languageName: node
  linkType: hard

"prosemirror-transform@npm:^1.0.0, prosemirror-transform@npm:^1.1.0, prosemirror-transform@npm:^1.10.0, prosemirror-transform@npm:^1.10.2, prosemirror-transform@npm:^1.2.1, prosemirror-transform@npm:^1.7.3":
  version: 1.10.2
  resolution: "prosemirror-transform@npm:1.10.2"
  dependencies:
    prosemirror-model: ^1.21.0
  checksum: 93b0c03affe579b0995b0c1e9a0bd54a1d7147ea1508ffc283ed41ee463e8ebc7a58b3b24ce5cc5fac542176f1fbd5c2ba1f32dd27037b1e0e33074b3997899f
  languageName: node
  linkType: hard

"prosemirror-view@npm:^1.0.0, prosemirror-view@npm:^1.1.0, prosemirror-view@npm:^1.13.3, prosemirror-view@npm:^1.27.0, prosemirror-view@npm:^1.31.0, prosemirror-view@npm:^1.34.3":
  version: 1.34.3
  resolution: "prosemirror-view@npm:1.34.3"
  dependencies:
    prosemirror-model: ^1.20.0
    prosemirror-state: ^1.0.0
    prosemirror-transform: ^1.1.0
  checksum: 622a9e40616da70a3f0368979a8b1e1639c90781ebfd3528c1cbf9863d2eed3186d862c9fa76ff04d13b5b277bc10d3fb28c72ff665f8a018e0db068c9a80c79
  languageName: node
  linkType: hard

"proto3-json-serializer@npm:^2.0.2":
  version: 2.0.2
  resolution: "proto3-json-serializer@npm:2.0.2"
  dependencies:
    protobufjs: ^7.2.5
  checksum: 21b8aa65be6dac2bb24920e5bdabef48b249bdf65b1498ae7e69ac4e70722275b083cd60a21d2b4be3ead9d768de2f6f5fb6b188bd177d51c824a539b5ba55cc
  languageName: node
  linkType: hard

"protobufjs@npm:^7.2.5, protobufjs@npm:^7.3.2":
  version: 7.3.2
  resolution: "protobufjs@npm:7.3.2"
  dependencies:
    "@protobufjs/aspromise": ^1.1.2
    "@protobufjs/base64": ^1.1.2
    "@protobufjs/codegen": ^2.0.4
    "@protobufjs/eventemitter": ^1.1.0
    "@protobufjs/fetch": ^1.1.0
    "@protobufjs/float": ^1.0.2
    "@protobufjs/inquire": ^1.1.0
    "@protobufjs/path": ^1.1.2
    "@protobufjs/pool": ^1.1.0
    "@protobufjs/utf8": ^1.1.0
    "@types/node": ">=13.7.0"
    long: ^5.0.0
  checksum: cfb2a744787f26ee7c82f3e7c4b72cfc000e9bb4c07828ed78eb414db0ea97a340c0cc3264d0e88606592f847b12c0351411f10e9af255b7ba864eec44d7705f
  languageName: node
  linkType: hard

"punycode.js@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode.js@npm:2.3.1"
  checksum: 13466d7ed5e8dacdab8c4cc03837e7dd14218a59a40eb14a837f1f53ca396e18ef2c4ee6d7766b8ed2fc391d6a3ac489eebf2de83b3596f5a54e86df4a251b72
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qs@npm:^6.5.1 < 6.10":
  version: 6.9.7
  resolution: "qs@npm:6.9.7"
  checksum: 5bbd263332ccf320a1f36d04a2019a5834dc20bcb736431eaccde2a39dcba03fb26d2fd00174f5d7bc26aaad1cad86124b18440883ac042ea2a0fca6170c1bf1
  languageName: node
  linkType: hard

"query-string@npm:^7.1.0":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: ^0.2.2
    filter-obj: ^1.1.0
    split-on-first: ^1.0.0
    strict-uri-encode: ^2.0.0
  checksum: 91af02dcd9cc9227a052841d5c2eecb80a0d6489d05625df506a097ef1c59037cfb5e907f39b84643cbfd535c955abec3e553d0130a7b510120c37d06e0f4346
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"quick-lru@npm:^4.0.1":
  version: 4.0.1
  resolution: "quick-lru@npm:4.0.1"
  checksum: bea46e1abfaa07023e047d3cf1716a06172c4947886c053ede5c50321893711577cb6119360f810cc3ffcd70c4d7db4069c3cee876b358ceff8596e062bd1154
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"raf@npm:^3.4.1":
  version: 3.4.1
  resolution: "raf@npm:3.4.1"
  dependencies:
    performance-now: ^2.1.0
  checksum: 50ba284e481c8185dbcf45fc4618ba3aec580bb50c9121385d5698cb6012fe516d2015b1df6dd407a7b7c58d44be8086108236affbce1861edd6b44637c8cd52
  languageName: node
  linkType: hard

"react-bootstrap-icons@npm:^1.10.2":
  version: 1.11.4
  resolution: "react-bootstrap-icons@npm:1.11.4"
  dependencies:
    prop-types: ^15.7.2
  peerDependencies:
    react: ">=16.8.6"
  checksum: 3b3a67bbbc72354d239519f12fef880ec88981ce51c9c2f3c14740ad7e1bab702308fd7e29297a2ab92abae9ee45c6e0fa615abdad5a20912c32c05f718c8c87
  languageName: node
  linkType: hard

"react-dom@npm:latest":
  version: 18.3.1
  resolution: "react-dom@npm:18.3.1"
  dependencies:
    loose-envify: ^1.1.0
    scheduler: ^0.23.2
  peerDependencies:
    react: ^18.3.1
  checksum: 298954ecd8f78288dcaece05e88b570014d8f6dce5db6f66e6ee91448debeb59dcd31561dddb354eee47e6c1bb234669459060deb238ed0213497146e555a0b9
  languageName: node
  linkType: hard

"react-error-boundary@npm:^4.0.13":
  version: 4.0.13
  resolution: "react-error-boundary@npm:4.0.13"
  dependencies:
    "@babel/runtime": ^7.12.5
  peerDependencies:
    react: ">=16.13.1"
  checksum: 50398d080015d51d22c6f94c56f4ea336d10232d72345b36ee6f15b6b643666d20b072829b02f091a80e5af68fe67f68a62ef0d2b649dbd759ead929304449af
  languageName: node
  linkType: hard

"react-icons@npm:^4.10.1":
  version: 4.12.0
  resolution: "react-icons@npm:4.12.0"
  peerDependencies:
    react: "*"
  checksum: db82a141117edcd884ade4229f0294b2ce15d82f68e0533294db07765d6dce00b129cf504338ec7081ce364fe899b296cb7752554ea08665b1d6bad811134e79
  languageName: node
  linkType: hard

"react-instantsearch-core@npm:7.13.3":
  version: 7.13.3
  resolution: "react-instantsearch-core@npm:7.13.3"
  dependencies:
    "@babel/runtime": ^7.1.2
    algoliasearch-helper: 3.22.5
    instantsearch.js: 4.75.0
    use-sync-external-store: ^1.0.0
  peerDependencies:
    algoliasearch: ">= 3.1 < 6"
    react: ">= 16.8.0 < 19"
  checksum: 871abce14bcd6b96d36faa3c3157ef300ed1a6ce401188678eaa209af9fa9b036b05380056d3ce41acdcd0e27d670beaa854eba67de816a90892142a5ca07802
  languageName: node
  linkType: hard

"react-instantsearch@npm:latest":
  version: 7.13.3
  resolution: "react-instantsearch@npm:7.13.3"
  dependencies:
    "@babel/runtime": ^7.1.2
    instantsearch-ui-components: 0.9.0
    instantsearch.js: 4.75.0
    react-instantsearch-core: 7.13.3
  peerDependencies:
    algoliasearch: ">= 3.1 < 6"
    react: ">= 16.8.0 < 19"
    react-dom: ">= 16.8.0 < 19"
  checksum: 019d7b4dccc400714d1edf5a1372d3b7914bbbefdbdf9e23be2829b10602dedcd7b12a913238f855bc358cde16788cb70c3b87b7e05b2d64ff48775cdcdf3784
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-redux@npm:^9.1.2":
  version: 9.1.2
  resolution: "react-redux@npm:9.1.2"
  dependencies:
    "@types/use-sync-external-store": ^0.0.3
    use-sync-external-store: ^1.0.0
  peerDependencies:
    "@types/react": ^18.2.25
    react: ^18.0
    redux: ^5.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
    redux:
      optional: true
  checksum: 1ee9cf41f29f68267320b4fc3bcf6a76a3825c82441612582678ddd827a2b60834f687d2a8b755c905885dfce476a1eb41af42b36f4dd71f8ee9991296a1e515
  languageName: node
  linkType: hard

"react-share@npm:^5.0.3":
  version: 5.1.0
  resolution: "react-share@npm:5.1.0"
  dependencies:
    classnames: ^2.3.2
    jsonp: ^0.2.1
  peerDependencies:
    react: ^17 || ^18
  checksum: c20c84b6ad203504f1bf83db63810793619f5b6ae63834960e7f441a5eeff8a3955894353f67c2d897470e24b75694b63b54d007764d4ee133150dbea6c0fe5b
  languageName: node
  linkType: hard

"react-tooltip@npm:^5.21.4":
  version: 5.27.1
  resolution: "react-tooltip@npm:5.27.1"
  dependencies:
    "@floating-ui/dom": ^1.6.1
    classnames: ^2.3.0
  peerDependencies:
    react: ">=16.14.0"
    react-dom: ">=16.14.0"
  checksum: 7af008df637e9e8e2d27fc453c787bfa4680230e750a72c12848023879669409fec31ae236c18ac411d8dbbe11a0564fe31398588f730eb065e731835a9160a2
  languageName: node
  linkType: hard

"react@npm:latest":
  version: 18.3.1
  resolution: "react@npm:18.3.1"
  dependencies:
    loose-envify: ^1.1.0
  checksum: a27bcfa8ff7c15a1e50244ad0d0c1cb2ad4375eeffefd266a64889beea6f6b64c4966c9b37d14ee32d6c9fcd5aa6ba183b6988167ab4d127d13e7cb5b386a376
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: ^2.3.0
  checksum: cffc728b9ede1e0667399903f9ecaf3789888b041c46ca53382fa3a06303e5132774dc0a96d0c16aa702dbac1ea0833d5a868d414f5ab2af1e1438e19e6657c6
  languageName: node
  linkType: hard

"read-pkg-up@npm:^7.0.1":
  version: 7.0.1
  resolution: "read-pkg-up@npm:7.0.1"
  dependencies:
    find-up: ^4.1.0
    read-pkg: ^5.2.0
    type-fest: ^0.8.1
  checksum: e4e93ce70e5905b490ca8f883eb9e48b5d3cebc6cd4527c25a0d8f3ae2903bd4121c5ab9c5a3e217ada0141098eeb661313c86fa008524b089b8ed0b7f165e44
  languageName: node
  linkType: hard

"read-pkg-up@npm:^8.0.0":
  version: 8.0.0
  resolution: "read-pkg-up@npm:8.0.0"
  dependencies:
    find-up: ^5.0.0
    read-pkg: ^6.0.0
    type-fest: ^1.0.1
  checksum: fe4c80401656b40b408884457fffb5a8015c03b1018cfd8e48f8d82a5e9023e24963603aeb2755608d964593e046c15b34d29b07d35af9c7aa478be81805209c
  languageName: node
  linkType: hard

"read-pkg@npm:^5.2.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^2.5.0
    parse-json: ^5.0.0
    type-fest: ^0.6.0
  checksum: eb696e60528b29aebe10e499ba93f44991908c57d70f2d26f369e46b8b9afc208ef11b4ba64f67630f31df8b6872129e0a8933c8c53b7b4daf0eace536901222
  languageName: node
  linkType: hard

"read-pkg@npm:^6.0.0":
  version: 6.0.0
  resolution: "read-pkg@npm:6.0.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^3.0.2
    parse-json: ^5.2.0
    type-fest: ^1.0.1
  checksum: 0cebdff381128e923815c643074a87011070e5fc352bee575d327d6485da3317fab6d802a7b03deeb0be7be8d3ad1640397b3d5d2f044452caf4e8d1736bf94f
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"redent@npm:^3.0.0":
  version: 3.0.0
  resolution: "redent@npm:3.0.0"
  dependencies:
    indent-string: ^4.0.0
    strip-indent: ^3.0.0
  checksum: fa1ef20404a2d399235e83cc80bd55a956642e37dd197b4b612ba7327bf87fa32745aeb4a1634b2bab25467164ab4ed9c15be2c307923dd08b0fe7c52431ae6b
  languageName: node
  linkType: hard

"redent@npm:^4.0.0":
  version: 4.0.0
  resolution: "redent@npm:4.0.0"
  dependencies:
    indent-string: ^5.0.0
    strip-indent: ^4.0.0
  checksum: 6944e7b1d8f3fd28c2515f5c605b9f7f0ea0f4edddf41890bbbdd4d9ee35abb7540c3b278f03ff827bd278bb6ff4a5bd8692ca406b748c5c1c3ce7355e9fbf8f
  languageName: node
  linkType: hard

"redux-thunk@npm:^2.4.2":
  version: 2.4.2
  resolution: "redux-thunk@npm:2.4.2"
  peerDependencies:
    redux: ^4
  checksum: c7f757f6c383b8ec26152c113e20087818d18ed3edf438aaad43539e9a6b77b427ade755c9595c4a163b6ad3063adf3497e5fe6a36c68884eb1f1cfb6f049a5c
  languageName: node
  linkType: hard

"redux@npm:^4.2.1":
  version: 4.2.1
  resolution: "redux@npm:4.2.1"
  dependencies:
    "@babel/runtime": ^7.9.2
  checksum: f63b9060c3a1d930ae775252bb6e579b42415aee7a23c4114e21a0b4ba7ec12f0ec76936c00f546893f06e139819f0e2855e0d55ebfce34ca9c026241a6950dd
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.7":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 27481628d22a1c4e3ff551096a683b424242a216fee44685467307f14d58020af1e19660bf2e26064de946bad7eff28950eae9f8209d55723e2d9351e632bbb4
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 9f57c93277b5585d3c83b0cf76be47b473ae8c6d9142a46ce8b0291a04bb2cf902059f0f8445dcabb3fb7378e5fe4bb4ea1e008876343d42e46d3b484534ce38
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"requireindex@npm:^1.1.0":
  version: 1.2.0
  resolution: "requireindex@npm:1.2.0"
  checksum: 50d8b10a1ff1fdf6aea7a1870bc7bd238b0fb1917d8d7ca17fd03afc38a65dcd7a8a4eddd031f89128b5f0065833d5c92c4fef67f2c04e8624057fe626c9cf94
  languageName: node
  linkType: hard

"reselect@npm:^4.1.8":
  version: 4.1.8
  resolution: "reselect@npm:4.1.8"
  checksum: a4ac87cedab198769a29be92bc221c32da76cfdad6911eda67b4d3e7136dca86208c3b210e31632eae31ebd2cded18596f0dd230d3ccc9e978df22f233b5583e
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.10.0, resolve@npm:^1.19.0, resolve@npm:^1.21.0, resolve@npm:^1.22.1":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: f8a26958aa572c9b064562750b52131a37c29d072478ea32e129063e2da7f83e31f7f11e7087a18225a8561cfe8d2f0df9dbea7c9d331a897571c0a2527dbb4c
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.1.7#~builtin<compat/resolve>, resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.19.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.21.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.22.1#~builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#~builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5479b7d431cacd5185f8db64bfcb7286ae5e31eb299f4c4f404ad8aa6098b77599563ac4257cb2c37a42f59dfc06a1bec2bcf283bb448f319e37f0feb9a09847
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry-request@npm:^7.0.0":
  version: 7.0.2
  resolution: "retry-request@npm:7.0.2"
  dependencies:
    "@types/request": ^2.48.8
    extend: ^3.0.2
    teeny-request: ^9.0.0
  checksum: 2d7307422333f548e5f40524978a344b62193714f6209c4f6a41057ae279804eb9bc8e0a277791e7b6f2d5d76068bdaca8590662a909cf1e6cfc3ab789e4c6b6
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rgbcolor@npm:^1.0.1":
  version: 1.0.1
  resolution: "rgbcolor@npm:1.0.1"
  checksum: bd062ac007a3e979e2f83dc69feb3cc4f9bca7d8631899548394160e30c47e4f7e52b31aa3f66a69061ad56e899e812ec52f5c33686c085d72c9b3d22faed1c8
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rollup-plugin-babel@npm:^4.4.0":
  version: 4.4.0
  resolution: "rollup-plugin-babel@npm:4.4.0"
  dependencies:
    "@babel/helper-module-imports": ^7.0.0
    rollup-pluginutils: ^2.8.1
  peerDependencies:
    "@babel/core": 7 || ^7.0.0-rc.2
    rollup: ">=0.60.0 <3"
  checksum: 5b8ed7c0a4192d7c74689074c910c1670eb07dfc875b1f4af5694a94c46bcb168ba85e2c9753030131efd6261ece7c252b9695953d0ea96d944977c6e79930d3
  languageName: node
  linkType: hard

"rollup-plugin-postcss@npm:^4.0.2":
  version: 4.0.2
  resolution: "rollup-plugin-postcss@npm:4.0.2"
  dependencies:
    chalk: ^4.1.0
    concat-with-sourcemaps: ^1.1.0
    cssnano: ^5.0.1
    import-cwd: ^3.0.0
    p-queue: ^6.6.2
    pify: ^5.0.0
    postcss-load-config: ^3.0.0
    postcss-modules: ^4.0.0
    promise.series: ^0.2.0
    resolve: ^1.19.0
    rollup-pluginutils: ^2.8.2
    safe-identifier: ^0.4.2
    style-inject: ^0.3.0
  peerDependencies:
    postcss: 8.x
  checksum: 67875e024fa36ba4bd43604dc50d02eabba0c93626cc372588260ae42aae3f98015ea1b0c3a78bcbd345ebea465ef636e5cb0f60dbc8b2e94fbe2514384395f0
  languageName: node
  linkType: hard

"rollup-plugin-styles@npm:^4.0.0":
  version: 4.0.0
  resolution: "rollup-plugin-styles@npm:4.0.0"
  dependencies:
    "@rollup/pluginutils": ^4.1.2
    "@types/cssnano": ^5.0.0
    cosmiconfig: ^7.0.1
    cssnano: ^5.0.15
    fs-extra: ^10.0.0
    icss-utils: ^5.1.0
    mime-types: ^2.1.34
    p-queue: ^6.6.2
    postcss: ^8.4.5
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.0
    postcss-modules-scope: ^3.0.0
    postcss-modules-values: ^4.0.0
    postcss-value-parser: ^4.2.0
    query-string: ^7.1.0
    resolve: ^1.21.0
    source-map-js: ^1.0.1
    tslib: ^2.3.1
  peerDependencies:
    rollup: ^2.63.0
  checksum: 857e5fd6820aedf2f751878d2ef4fa40210094fb548d1baaa1dafbcdd7f7ea9775bac1c899b305bd27a23026d0694940651692a39901ac2ba2f8ce9f2bd10eb1
  languageName: node
  linkType: hard

"rollup-pluginutils@npm:^2.8.1, rollup-pluginutils@npm:^2.8.2":
  version: 2.8.2
  resolution: "rollup-pluginutils@npm:2.8.2"
  dependencies:
    estree-walker: ^0.6.1
  checksum: 339fdf866d8f4ff6e408fa274c0525412f7edb01dc46b5ccda51f575b7e0d20ad72965773376fb5db95a77a7fcfcab97bf841ec08dbadf5d6b08af02b7a2cf5e
  languageName: node
  linkType: hard

"rollup@npm:^3.29.0":
  version: 3.29.4
  resolution: "rollup@npm:3.29.4"
  dependencies:
    fsevents: ~2.3.2
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 8bb20a39c8d91130825159c3823eccf4dc2295c9a0a5c4ed851a5bf2167dbf24d9a29f23461a54c955e5506395e6cc188eafc8ab0e20399d7489fb33793b184e
  languageName: node
  linkType: hard

"rope-sequence@npm:^1.3.0":
  version: 1.3.4
  resolution: "rope-sequence@npm:1.3.4"
  checksum: 95cca2f99af3d0d1f2f5e2781b6ae352c05e024c25f17f68a9b3ff31c651c8c46f096c70c46b561898e0bc94d261dfed60148f3aa009d1e98280e14ab0fe1438
  languageName: node
  linkType: hard

"run-async@npm:^3.0.0":
  version: 3.0.0
  resolution: "run-async@npm:3.0.0"
  checksum: 280c03d5a88603f48103fc6fd69f07fb0c392a1e0d319c34ec96a2516030e07ba06f79231a563c78698b882649c2fc1fda601bc84705f57d50efcd1fa506cfc0
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rxjs@npm:^7.8.1":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: ^2.1.0
  checksum: de4b53db1063e618ec2eca0f7965d9137cabe98cf6be9272efe6c86b47c17b987383df8574861bcced18ebd590764125a901d5506082be84a8b8e364bf05f119
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-identifier@npm:^0.4.2":
  version: 0.4.2
  resolution: "safe-identifier@npm:0.4.2"
  checksum: 67e28ed89a74cf20b827419003d3cb60a0ebaec0771c2c818f4b2239bf4f96e01ad90aa8db6dc57ee90c0c438b6f46323e4b5a3d955d18d8c4e158ea035cabdd
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sass-graph@npm:^4.0.1":
  version: 4.0.1
  resolution: "sass-graph@npm:4.0.1"
  dependencies:
    glob: ^7.0.0
    lodash: ^4.17.11
    scss-tokenizer: ^0.4.3
    yargs: ^17.2.1
  bin:
    sassgraph: bin/sassgraph
  checksum: 896f99253bd77a429a95e483ebddee946e195b61d3f84b3e1ccf8ad843265ec0585fa40bf55fbf354c5f57eb9fd0349834a8b190cd2161ab1234cb9af10e3601
  languageName: node
  linkType: hard

"sass@npm:^1.64.2":
  version: 1.77.8
  resolution: "sass@npm:1.77.8"
  dependencies:
    chokidar: ">=3.0.0 <4.0.0"
    immutable: ^4.0.0
    source-map-js: ">=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: 6b5dce17faa1bd1e349b4825bf7f76559a32f3f95d789cd2847623c88ee9635e1485d3458532a05fa5b9134cfbce79a4bad3f13dc63c2433632347674db0abae
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.2":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 3e82d1f419e240ef6219d794ff29c7ee415fbdc19e038f680a10c067108e06284f1847450a210b29bbaf97b9d8a97ced5f624c31c681248ac84c80d56ad5a2c4
  languageName: node
  linkType: hard

"scss-tokenizer@npm:^0.4.3":
  version: 0.4.3
  resolution: "scss-tokenizer@npm:0.4.3"
  dependencies:
    js-base64: ^2.4.9
    source-map: ^0.7.3
  checksum: f3697bb155ae23d88c7cd0275988a73231fe675fbbd250b4e56849ba66319fc249a597f3799a92f9890b12007f00f8f6a7f441283e634679e2acdb2287a341d1
  languageName: node
  linkType: hard

"search-insights@npm:^2.15.0":
  version: 2.15.0
  resolution: "search-insights@npm:2.15.0"
  checksum: 0982b39c5aea8d5c6ac1d44f5dc39be24ebe6df2781c9ba89572e1f31c86a110fae772511ae2ebfae6a2559b65907469c4eec46e1bab1aea781ee8818ea7035a
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^7.3.4, semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.5.4":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"shallowequal@npm:1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: f4c1de0837f106d2dbbfd5d0720a5d059d1c66b42b580965c8f06bb1db684be8783538b684092648c981294bf817869f743a066538771dbecb293df78f765e00
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"size-sensor@npm:^1.0.1":
  version: 1.0.2
  resolution: "size-sensor@npm:1.0.2"
  checksum: de7050178ae9afee3388eb9191af0902b30ef83c26e8c9d9c203e1b560e270b947d978e4f56d211802112d09ef296931fa612f69155a483900f3b4717a0750d7
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    astral-regex: ^2.0.0
    is-fullwidth-code-point: ^3.0.0
  checksum: 4a82d7f085b0e1b070e004941ada3c40d3818563ac44766cca4ceadd2080427d337554f9f99a13aaeb3b4a94d9964d9466c807b3d7b7541d1ec37ee32d308756
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^6.0.0":
  version: 6.2.1
  resolution: "socks-proxy-agent@npm:6.2.1"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 9ca089d489e5ee84af06741135c4b0d2022977dad27ac8d649478a114cdce87849e8d82b7c22b51501a4116e231241592946fc7fae0afc93b65030ee57084f58
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.4
  resolution: "socks-proxy-agent@npm:8.0.4"
  dependencies:
    agent-base: ^7.1.1
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b2ec5051d85fe49072f9a250c427e0e9571fd09d5db133819192d078fd291276e1f0f50f6dbc04329b207738b1071314cee8bdbb4b12e27de42dbcf1d4233c67
  languageName: node
  linkType: hard

"socks@npm:^2.6.2, socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0, source-map-js@npm:^1.0.1, source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.0":
  version: 1.2.0
  resolution: "source-map-js@npm:1.2.0"
  checksum: 791a43306d9223792e84293b00458bf102a8946e7188f3db0e4e22d8d530b5f80a4ce468eb5ec0bf585443ad55ebbd630bf379c98db0b1f317fd902500217f97
  languageName: node
  linkType: hard

"source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"source-map@npm:^0.7.3":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 01cc5a74b1f0e1d626a58d36ad6898ea820567e87f18dfc9d24a9843a351aaa2ec09b87422589906d6ff1deed29693e176194dc88bcae7c9a852dc74b311dbf5
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: e9ae98d22f69c88e7aff5b8778dc01c361ef635580e82d29e5c60a6533cc8f4d820803e67d7432581af0cc4fb49973125076ee3b90df191d153e223c004193b2
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: bb127d6e2532de65b912f7c99fc66097cdea7d64c10d3ec9b5e96524dbbd7d20e01cba818a6ddb2ae75e62bb0c63d5e277a7e555a85cbc8ab40044984fa4ae15
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.18
  resolution: "spdx-license-ids@npm:3.0.18"
  checksum: 457825df5dd1fc0135b0bb848c896143f70945cc2da148afc71c73ed0837d1d651f809006e406d82109c9dd71a8cb39785a3604815fe46bc0548e9d3976f6b69
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 16ff85b54ddcf17f9147210a4022529b343edbcbea4ce977c8f30e38408b8d6e0f25f92cd35b86a524d4797f455e29ab89eb8db787f3c10708e0b47ebf528d30
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"ssf@npm:~0.11.2":
  version: 0.11.2
  resolution: "ssf@npm:0.11.2"
  dependencies:
    frac: ~1.1.2
  checksum: 6ecef6ae0a90e67dc4b05bc3cca883e2dffad9773b41124af36ee308884e4a29f98bde66d6c8d2bd1ccf5f860ea4f6c49338bd8d733007fc42ebe02dd5295dcf
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.6
  resolution: "ssri@npm:10.0.6"
  dependencies:
    minipass: ^7.0.3
  checksum: 4603d53a05bcd44188747d38f1cc43833b9951b5a1ee43ba50535bdfc5fe4a0897472dbe69837570a5417c3c073377ef4f8c1a272683b401857f72738ee57299
  languageName: node
  linkType: hard

"ssri@npm:^8.0.0, ssri@npm:^8.0.1":
  version: 8.0.1
  resolution: "ssri@npm:8.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: bc447f5af814fa9713aa201ec2522208ae0f4d8f3bda7a1f445a797c7b929a02720436ff7c478fb5edc4045adb02b1b88d2341b436a80798734e2494f1067b36
  languageName: node
  linkType: hard

"ssri@npm:^9.0.0":
  version: 9.0.1
  resolution: "ssri@npm:9.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: fb58f5e46b6923ae67b87ad5ef1c5ab6d427a17db0bead84570c2df3cd50b4ceb880ebdba2d60726588272890bae842a744e1ecce5bd2a2a582fccd5068309eb
  languageName: node
  linkType: hard

"stable@npm:^0.1.8":
  version: 0.1.8
  resolution: "stable@npm:0.1.8"
  checksum: 2ff482bb100285d16dd75cd8f7c60ab652570e8952c0bfa91828a2b5f646a0ff533f14596ea4eabd48bb7f4aeea408dce8f8515812b975d958a4cc4fa6b9dfeb
  languageName: node
  linkType: hard

"stackblur-canvas@npm:^2.0.0":
  version: 2.7.0
  resolution: "stackblur-canvas@npm:2.7.0"
  checksum: 05b37ef9f1ba3aac2a1dda2f2c078cacd0668426ef689dbbfac7e90c79ef05e8dfad8e0d8474a1cc52776c5810e224ef163cbee2ec52f0a320dec8352ab2dece
  languageName: node
  linkType: hard

"statsig-js@npm:5.1.0":
  version: 5.1.0
  resolution: "statsig-js@npm:5.1.0"
  dependencies:
    js-sha256: ^0.11.0
    uuid: ^9.0.1
  checksum: e37da3e33050e38df67b575c0e9c81251e8a2732088db172d15fd5fa5b5b857babc6b34d4275f6f189967c17c3c906f2d4950152ac539d386cf89154eaa476ce
  languageName: node
  linkType: hard

"statsig-node@npm:^5.23.1":
  version: 5.25.0
  resolution: "statsig-node@npm:5.25.0"
  dependencies:
    ip3country: ^5.0.0
    node-fetch: ^2.6.13
    ua-parser-js: ^1.0.2
    uuid: ^8.3.2
  checksum: acf9bced8b368a74155439d050884dc217dd2cb350aaedee5e8922fb68304feb2116f06c9870214d3a9e0b3dafa68b5a151361f3945a4dd63beb2a8f0e9d7724
  languageName: node
  linkType: hard

"statsig-react@npm:^2.1.0":
  version: 2.1.0
  resolution: "statsig-react@npm:2.1.0"
  dependencies:
    statsig-js: 5.1.0
  peerDependencies:
    react: ^16.6.3 || ^17.0.0 || ^18.0.0
  checksum: be351523c4405553aa33690aed5b5e2c3b6d453b2c3c042dc5a193109078c326191e3135ba98b4a9046304be3774679ef7a563f81c30fef1014ebf3f57490861
  languageName: node
  linkType: hard

"stdout-stream@npm:^1.4.0":
  version: 1.4.1
  resolution: "stdout-stream@npm:1.4.1"
  dependencies:
    readable-stream: ^2.0.1
  checksum: 205bee8c3ba4e1e1d471b9302764405d2ee5dd272af6e9a71c95a9af6cf2ad8f4d102099a917c591ba9e14c1b2b5f5244f7a526e9d3cf311327cecd7c2bd4c2e
  languageName: node
  linkType: hard

"stream-events@npm:^1.0.5":
  version: 1.0.5
  resolution: "stream-events@npm:1.0.5"
  dependencies:
    stubs: ^3.0.0
  checksum: 969ce82e34bfbef5734629cc06f9d7f3705a9ceb8fcd6a526332f9159f1f8bbfdb1a453f3ced0b728083454f7706adbbe8428bceb788a0287ca48ba2642dc3fc
  languageName: node
  linkType: hard

"stream-shift@npm:^1.0.2":
  version: 1.0.3
  resolution: "stream-shift@npm:1.0.3"
  checksum: a24c0a3f66a8f9024bd1d579a533a53be283b4475d4e6b4b3211b964031447bdf6532dd1f3c2b0ad66752554391b7c62bd7ca4559193381f766534e723d50242
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: eaac4cf978b6fbd480f1092cab8b233c9b949bcabfc9b598dd79a758f7243c28765ef7639c876fa72940dac687181b35486ea01ff7df3e65ce3848c64822c581
  languageName: node
  linkType: hard

"string-hash@npm:^1.1.1":
  version: 1.1.3
  resolution: "string-hash@npm:1.1.3"
  checksum: 104b8667a5e0dc71bfcd29fee09cb88c6102e27bfb07c55f95535d90587d016731d52299380052e514266f4028a7a5172e0d9ac58e2f8f5001be61dc77c0754d
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-bom@npm:2.0.0"
  dependencies:
    is-utf8: ^0.2.0
  checksum: 08efb746bc67b10814cd03d79eb31bac633393a782e3f35efbc1b61b5165d3806d03332a97f362822cf0d4dd14ba2e12707fcff44fe1c870c48a063a0c9e4944
  languageName: node
  linkType: hard

"strip-indent@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-indent@npm:3.0.0"
  dependencies:
    min-indent: ^1.0.0
  checksum: 18f045d57d9d0d90cd16f72b2313d6364fd2cb4bf85b9f593523ad431c8720011a4d5f08b6591c9d580f446e78855c5334a30fb91aa1560f5d9f95ed1b4a0530
  languageName: node
  linkType: hard

"strip-indent@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-indent@npm:4.0.0"
  dependencies:
    min-indent: ^1.0.1
  checksum: 06cbcd93da721c46bc13caeb1c00af93a9b18146a1c95927672d2decab6a25ad83662772417cea9317a2507fb143253ecc23c4415b64f5828cef9b638a744598
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"stubs@npm:^3.0.0":
  version: 3.0.0
  resolution: "stubs@npm:3.0.0"
  checksum: dec7b82186e3743317616235c59bfb53284acc312cb9f4c3e97e2205c67a5c158b0ca89db5927e52351582e90a2672822eeaec9db396e23e56893d2a8676e024
  languageName: node
  linkType: hard

"style-inject@npm:^0.3.0":
  version: 0.3.0
  resolution: "style-inject@npm:0.3.0"
  checksum: fa5f5f6730c3eb4ccc5735347935703c7c02759d4ddb5983d037ed0efda3c50a80640c2fed4f4d4c5ea600c97cdfdb45f79f734630324fa21a3a86723c0472da
  languageName: node
  linkType: hard

"style-search@npm:^0.1.0":
  version: 0.1.0
  resolution: "style-search@npm:0.1.0"
  checksum: 3cfefe335033aad6d47da0725cb48f5db91a73935954c77eab77d9e415e6668cdb406da4a4f7ef9f1aca77853cf5ba7952c45e869caa5bd6439691d88098d468
  languageName: node
  linkType: hard

"styled-components@npm:^6.0.0-rc.1":
  version: 6.1.12
  resolution: "styled-components@npm:6.1.12"
  dependencies:
    "@emotion/is-prop-valid": 1.2.2
    "@emotion/unitless": 0.8.1
    "@types/stylis": 4.2.5
    css-to-react-native: 3.2.0
    csstype: 3.1.3
    postcss: 8.4.38
    shallowequal: 1.1.0
    stylis: 4.3.2
    tslib: 2.6.2
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
  checksum: ce88075297588ee3910e00d9f8dba09a2d31e6dd0b329d96a7c4afed3d6fddddf6cfb4a29e63b91d7a3137a9e774fafeaaf589237269ea6bd5240a838bdf93e9
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.1":
  version: 5.1.1
  resolution: "styled-jsx@npm:5.1.1"
  dependencies:
    client-only: 0.0.1
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 523a33b38603492547e861b98e29c873939b04e15fbe5ef16132c6f1e15958126647983c7d4675325038b428a5e91183d996e90141b18bdd1bbadf6e2c45b2fa
  languageName: node
  linkType: hard

"stylehacks@npm:^5.1.1":
  version: 5.1.1
  resolution: "stylehacks@npm:5.1.1"
  dependencies:
    browserslist: ^4.21.4
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.2.15
  checksum: 11175366ef52de65bf06cefba0ddc9db286dc3a1451fd2989e74c6ea47091a02329a4bf6ce10b1a36950056927b6bbbe47c5ab3a1f4c7032df932d010fbde5a2
  languageName: node
  linkType: hard

"stylelint-config-recommended@npm:^13.0.0":
  version: 13.0.0
  resolution: "stylelint-config-recommended@npm:13.0.0"
  peerDependencies:
    stylelint: ^15.10.0
  checksum: a56eb6d1a7c7f3a7a172b54bc34218859ba22a5a06816fb4d0964f66cb83cf372062f2c97830e994ad68243548e15fc49abf28887c3261ab1b471b3aa69f8e82
  languageName: node
  linkType: hard

"stylelint-config-standard@npm:^34.0.0":
  version: 34.0.0
  resolution: "stylelint-config-standard@npm:34.0.0"
  dependencies:
    stylelint-config-recommended: ^13.0.0
  peerDependencies:
    stylelint: ^15.10.0
  checksum: 536249800c04b48a9c354067765f042713982e8222be17bb897a27d26546e50adfb87e6f1e4541807d720de3554345da99ab470e13e8d7ab0ab326c73ae3df61
  languageName: node
  linkType: hard

"stylelint@npm:^15.10.3":
  version: 15.11.0
  resolution: "stylelint@npm:15.11.0"
  dependencies:
    "@csstools/css-parser-algorithms": ^2.3.1
    "@csstools/css-tokenizer": ^2.2.0
    "@csstools/media-query-list-parser": ^2.1.4
    "@csstools/selector-specificity": ^3.0.0
    balanced-match: ^2.0.0
    colord: ^2.9.3
    cosmiconfig: ^8.2.0
    css-functions-list: ^3.2.1
    css-tree: ^2.3.1
    debug: ^4.3.4
    fast-glob: ^3.3.1
    fastest-levenshtein: ^1.0.16
    file-entry-cache: ^7.0.0
    global-modules: ^2.0.0
    globby: ^11.1.0
    globjoin: ^0.1.4
    html-tags: ^3.3.1
    ignore: ^5.2.4
    import-lazy: ^4.0.0
    imurmurhash: ^0.1.4
    is-plain-object: ^5.0.0
    known-css-properties: ^0.29.0
    mathml-tag-names: ^2.1.3
    meow: ^10.1.5
    micromatch: ^4.0.5
    normalize-path: ^3.0.0
    picocolors: ^1.0.0
    postcss: ^8.4.28
    postcss-resolve-nested-selector: ^0.1.1
    postcss-safe-parser: ^6.0.0
    postcss-selector-parser: ^6.0.13
    postcss-value-parser: ^4.2.0
    resolve-from: ^5.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    style-search: ^0.1.0
    supports-hyperlinks: ^3.0.0
    svg-tags: ^1.0.0
    table: ^6.8.1
    write-file-atomic: ^5.0.1
  bin:
    stylelint: bin/stylelint.mjs
  checksum: 9835f8a3e3976a3b81a35569d08f5f4a9c3b5cff415f1345a505870afc0c3231acff27f119d937c5bb11fdbc98d554af564c2a648a52604280a59a11974fcbfc
  languageName: node
  linkType: hard

"stylis@npm:4.3.2":
  version: 4.3.2
  resolution: "stylis@npm:4.3.2"
  checksum: 0faa8a97ff38369f47354376cd9f0def9bf12846da54c28c5987f64aaf67dcb6f00dce88a8632013bfb823b2c4d1d62a44f4ac20363a3505a7ab4e21b70179fc
  languageName: node
  linkType: hard

"supercluster@npm:^8.0.1":
  version: 8.0.1
  resolution: "supercluster@npm:8.0.1"
  dependencies:
    kdbush: ^4.0.2
  checksum: 39d141f768a511efa53260252f9dab9a2ce0228b334e55482c8d3019e151932f05e1a9a0252d681737651b13c741c665542a6ddb40ec27de96159ea7ad41f7f4
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^3.0.0":
  version: 3.0.0
  resolution: "supports-hyperlinks@npm:3.0.0"
  dependencies:
    has-flag: ^4.0.0
    supports-color: ^7.0.0
  checksum: 41021305de5255b10d821bf93c7a781f783e1693d0faec293d7fc7ccf17011b90bde84b0295fa92ba75c6c390351fe84fdd18848cad4bf656e464a958243c3e7
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-pathdata@npm:^6.0.3":
  version: 6.0.3
  resolution: "svg-pathdata@npm:6.0.3"
  checksum: f0e55be50c654be5d259d70945ed7e5354bf78e51c6039b4045d9f7c49d703a0c33dda36751815aec2824d046c417c35226e7491246ffff3e9164735ea428446
  languageName: node
  linkType: hard

"svg-tags@npm:^1.0.0":
  version: 1.0.0
  resolution: "svg-tags@npm:1.0.0"
  checksum: 407e5ef87cfa2fb81c61d738081c2decd022ce13b922d035b214b49810630bf5d1409255a4beb3a940b77b32f6957806deff16f1bf0ce1ab11c7a184115a0b7f
  languageName: node
  linkType: hard

"svgo@npm:^2.7.0":
  version: 2.8.0
  resolution: "svgo@npm:2.8.0"
  dependencies:
    "@trysound/sax": 0.2.0
    commander: ^7.2.0
    css-select: ^4.1.3
    css-tree: ^1.1.3
    csso: ^4.2.0
    picocolors: ^1.0.0
    stable: ^0.1.8
  bin:
    svgo: bin/svgo
  checksum: b92f71a8541468ffd0b81b8cdb36b1e242eea320bf3c1a9b2c8809945853e9d8c80c19744267eb91cabf06ae9d5fff3592d677df85a31be4ed59ff78534fa420
  languageName: node
  linkType: hard

"table@npm:^6.8.1":
  version: 6.8.2
  resolution: "table@npm:6.8.2"
  dependencies:
    ajv: ^8.0.1
    lodash.truncate: ^4.4.2
    slice-ansi: ^4.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  checksum: 61188652f53a980d1759ca460ca8dea5c5322aece3210457e7084882f053c2b6a870041295e08a82cb1d676e31b056406845d94b0abf3c79a4b104777bec413b
  languageName: node
  linkType: hard

"tar@npm:^6.0.2, tar@npm:^6.1.11, tar@npm:^6.1.2, tar@npm:^6.2.1":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: f1322768c9741a25356c11373bce918483f40fa9a25c69c59410c8a1247632487edef5fe76c5f12ac51a6356d2f1829e96d2bc34098668a2fc34d76050ac2b6c
  languageName: node
  linkType: hard

"teeny-request@npm:^9.0.0":
  version: 9.0.0
  resolution: "teeny-request@npm:9.0.0"
  dependencies:
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    node-fetch: ^2.6.9
    stream-events: ^1.0.5
    uuid: ^9.0.0
  checksum: 9cb0ad83f9ca6ce6515b3109cbb30ceb2533cdeab8e41c3a0de89f509bd92c5a9aabd27b3adf7f3e49516e106a358859b19fa4928a1937a4ab95809ccb7d52eb
  languageName: node
  linkType: hard

"text-segmentation@npm:^1.0.3":
  version: 1.0.3
  resolution: "text-segmentation@npm:1.0.3"
  dependencies:
    utrie: ^1.0.2
  checksum: 2e24632d59567c55ab49ac324815e2f7a8043e63e26b109636322ac3e30692cee8679a448fd5d0f0598a345f407afd0e34ba612e22524cf576d382d84058c013
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"tippy.js@npm:^6.3.7":
  version: 6.3.7
  resolution: "tippy.js@npm:6.3.7"
  dependencies:
    "@popperjs/core": ^2.9.0
  checksum: cac955318a65288e8d2dca05059878b003c6e66f92c94f7810f5bc5448eb6646abdf7dacc9bd00020e2611592598d0aae3a28ec9a45349a159603c3fdddce5fb
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 902d7aceb74453ea02abbf58c203f4a8fc1cead89b60b31e354f74ed5b3fb09ea817f94fb310f884a5d16987dd9fa5a735412a7c2dd088dd3d415aa819ae3a28
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"topojson-client@npm:^3.1.0":
  version: 3.1.0
  resolution: "topojson-client@npm:3.1.0"
  dependencies:
    commander: 2
  bin:
    topo2geo: bin/topo2geo
    topomerge: bin/topomerge
    topoquantize: bin/topoquantize
  checksum: 8c029a4f18324ace0b8b55dd90edbd40c9e3c6de18bafbb5da37ca20ebf20e26fbd4420891acb3c2c264e214185f7557871f5651a9eee517028663be98d836de
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"trim-newlines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-newlines@npm:3.0.1"
  checksum: b530f3fadf78e570cf3c761fb74fef655beff6b0f84b29209bac6c9622db75ad1417f4a7b5d54c96605dcd72734ad44526fef9f396807b90839449eb543c6206
  languageName: node
  linkType: hard

"trim-newlines@npm:^4.0.2":
  version: 4.1.1
  resolution: "trim-newlines@npm:4.1.1"
  checksum: 5b09f8e329e8f33c1111ef26906332ba7ba7248cde3e26fc054bb3d69f2858bf5feedca9559c572ff91f33e52977c28e0d41c387df6a02a633cbb8c2d8238627
  languageName: node
  linkType: hard

"true-case-path@npm:^2.2.1":
  version: 2.2.1
  resolution: "true-case-path@npm:2.2.1"
  checksum: fd5f1c2a87a122a65ffb1f84b580366be08dac7f552ea0fa4b5a6ab0a013af950b0e752beddb1c6c1652e6d6a2b293b7b3fd86a5a1706242ad365b68f1b5c6f1
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1":
  version: 1.3.0
  resolution: "ts-api-utils@npm:1.3.0"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: c746ddabfdffbf16cb0b0db32bb287236a19e583057f8649ee7c49995bb776e1d3ef384685181c11a1a480369e022ca97512cb08c517b2d2bd82c83754c97012
  languageName: node
  linkType: hard

"ts-dedent@npm:^2.2.0":
  version: 2.2.0
  resolution: "ts-dedent@npm:2.2.0"
  checksum: 93ed8f7878b6d5ed3c08d99b740010eede6bccfe64bce61c5a4da06a2c17d6ddbb80a8c49c2d15251de7594a4f93ffa21dd10e7be75ef66a4dc9951b4a94e2af
  languageName: node
  linkType: hard

"tslib@npm:2.3.0":
  version: 2.3.0
  resolution: "tslib@npm:2.3.0"
  checksum: 8869694c26e4a7b56d449662fd54a4f9ba872c889d991202c74462bd99f10e61d5bd63199566c4284c0f742277736292a969642cc7b590f98727a7cae9529122
  languageName: node
  linkType: hard

"tslib@npm:2.6.2":
  version: 2.6.2
  resolution: "tslib@npm:2.6.2"
  checksum: 329ea56123005922f39642318e3d1f0f8265d1e7fcb92c633e0809521da75eeaca28d2cf96d7248229deb40e5c19adf408259f4b9640afd20d13aecc1430f3ad
  languageName: node
  linkType: hard

"tslib@npm:^1.8.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.1.0, tslib@npm:^2.3.1, tslib@npm:^2.4.0":
  version: 2.6.3
  resolution: "tslib@npm:2.6.3"
  checksum: 74fce0e100f1ebd95b8995fbbd0e6c91bdd8f4c35c00d4da62e285a3363aaa534de40a80db30ecfd388ed7c313c42d930ee0eaf108e8114214b180eec3dbe6f5
  languageName: node
  linkType: hard

"tsutils@npm:^3.21.0":
  version: 3.21.0
  resolution: "tsutils@npm:3.21.0"
  dependencies:
    tslib: ^1.8.1
  peerDependencies:
    typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"
  checksum: 1843f4c1b2e0f975e08c4c21caa4af4f7f65a12ac1b81b3b8489366826259323feb3fc7a243123453d2d1a02314205a7634e048d4a8009921da19f99755cdc48
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-fest@npm:^0.18.0":
  version: 0.18.1
  resolution: "type-fest@npm:0.18.1"
  checksum: e96dcee18abe50ec82dab6cbc4751b3a82046da54c52e3b2d035b3c519732c0b3dd7a2fa9df24efd1a38d953d8d4813c50985f215f1957ee5e4f26b0fe0da395
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 4fb3272df21ad1c552486f8a2f8e115c09a521ad7a8db3d56d53718d0c907b62c6e9141ba5f584af3f6830d0872c521357e512381f24f7c44acae583ad517d73
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: b2188e6e4b21557f6e92960ec496d28a51d68658018cba8b597bd3ef757721d1db309f120ae987abeeda874511d14b776157ff809f23c6d1ce8f83b9b2b7d60f
  languageName: node
  linkType: hard

"type-fest@npm:^0.8.1":
  version: 0.8.1
  resolution: "type-fest@npm:0.8.1"
  checksum: d61c4b2eba24009033ae4500d7d818a94fd6d1b481a8111612ee141400d5f1db46f199c014766b9fa9b31a6a7374d96fc748c6d688a78a3ce5a33123839becb7
  languageName: node
  linkType: hard

"type-fest@npm:^1.0.1, type-fest@npm:^1.2.1, type-fest@npm:^1.2.2":
  version: 1.4.0
  resolution: "type-fest@npm:1.4.0"
  checksum: b011c3388665b097ae6a109a437a04d6f61d81b7357f74cbcb02246f2f5bd72b888ae33631b99871388122ba0a87f4ff1c94078e7119ff22c70e52c0ff828201
  languageName: node
  linkType: hard

"typescript@npm:5.1.6":
  version: 5.1.6
  resolution: "typescript@npm:5.1.6"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b2f2c35096035fe1f5facd1e38922ccb8558996331405eb00a5111cc948b2e733163cc22fab5db46992aba7dd520fff637f2c1df4996ff0e134e77d3249a7350
  languageName: node
  linkType: hard

"typescript@patch:typescript@5.1.6#~builtin<compat/typescript>":
  version: 5.1.6
  resolution: "typescript@patch:typescript@npm%3A5.1.6#~builtin<compat/typescript>::version=5.1.6&hash=5da071"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: f53bfe97f7c8b2b6d23cf572750d4e7d1e0c5fff1c36d859d0ec84556a827b8785077bc27676bf7e71fae538e517c3ecc0f37e7f593be913d884805d931bc8be
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.2":
  version: 1.0.38
  resolution: "ua-parser-js@npm:1.0.38"
  checksum: d0772b22b027338d806ab17d1ac2896ee7485bdf9217c526028159f3cd6bb10272bb18f6196d2f94dde83e3b36dc9d2533daf08a414764f6f4f1844842383838
  languageName: node
  linkType: hard

"uc.micro@npm:^2.0.0, uc.micro@npm:^2.1.0":
  version: 2.1.0
  resolution: "uc.micro@npm:2.1.0"
  checksum: 37197358242eb9afe367502d4638ac8c5838b78792ab218eafe48287b0ed28aaca268ec0392cc5729f6c90266744de32c06ae938549aee041fc93b0f9672d6b2
  languageName: node
  linkType: hard

"undici-types@npm:~6.13.0":
  version: 6.13.0
  resolution: "undici-types@npm:6.13.0"
  checksum: 9d0ef6bf58994bebbea6a4ab75f381c69a89a7ed151bfbae0d4ef95450d56502c9eccb323abf17b7d099c1d9c1cbae62e909e4dfeb8d204612d2f1fdada24707
  languageName: node
  linkType: hard

"unique-filename@npm:^1.1.1":
  version: 1.1.1
  resolution: "unique-filename@npm:1.1.1"
  dependencies:
    unique-slug: ^2.0.0
  checksum: cf4998c9228cc7647ba7814e255dec51be43673903897b1786eff2ac2d670f54d4d733357eb08dea969aa5e6875d0e1bd391d668fbdb5a179744e7c7551a6f80
  languageName: node
  linkType: hard

"unique-filename@npm:^2.0.0":
  version: 2.0.1
  resolution: "unique-filename@npm:2.0.1"
  dependencies:
    unique-slug: ^3.0.0
  checksum: 807acf3381aff319086b64dc7125a9a37c09c44af7620bd4f7f3247fcd5565660ac12d8b80534dcbfd067e6fe88a67e621386dd796a8af828d1337a8420a255f
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: ^4.0.0
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^2.0.0":
  version: 2.0.2
  resolution: "unique-slug@npm:2.0.2"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 5b6876a645da08d505dedb970d1571f6cebdf87044cb6b740c8dbb24f0d6e1dc8bdbf46825fd09f994d7cf50760e6f6e063cfa197d51c5902c00a861702eb75a
  languageName: node
  linkType: hard

"unique-slug@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-slug@npm:3.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 49f8d915ba7f0101801b922062ee46b7953256c93ceca74303bd8e6413ae10aa7e8216556b54dc5382895e8221d04f1efaf75f945c2e4a515b4139f77aa6640c
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 0884b58365af59f89739e6f71e3feacb5b1b41f2df2d842d0757933620e6de08eff347d27e9d499b43c40476cbaf7988638d3acb2ffbcb9d35fd035591adfd15
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.0":
  version: 1.1.0
  resolution: "update-browserslist-db@npm:1.1.0"
  dependencies:
    escalade: ^3.1.2
    picocolors: ^1.0.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b74694d96f0c360f01b702e72353dc5a49df4fe6663d3ee4e5c628f061576cddf56af35a3a886238c01dd3d8f231b7a86a8ceaa31e7a9220ae31c1c1238e562
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.0.0, use-sync-external-store@npm:^1.2.2":
  version: 1.2.2
  resolution: "use-sync-external-store@npm:1.2.2"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: fe07c071c4da3645f112c38c0e57beb479a8838616ff4e92598256ecce527f2888c08febc7f9b2f0ce2f0e18540ba3cde41eb2035e4fafcb4f52955037098a81
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utrie@npm:^1.0.2":
  version: 1.0.2
  resolution: "utrie@npm:1.0.2"
  dependencies:
    base64-arraybuffer: ^1.0.2
  checksum: c96fbb7d4d8855a154327da0b18e39b7511cc70a7e4bcc3658e24f424bb884312d72b5ba777500b8858e34d365dc6b1a921dc5ca2f0d341182519c6b78e280a5
  languageName: node
  linkType: hard

"uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "uuid@npm:10.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 4b81611ade2885d2313ddd8dc865d93d8dccc13ddf901745edca8f86d99bc46d7a330d678e7532e7ebf93ce616679fb19b2e3568873ac0c14c999032acb25869
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0, uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"w3c-keyname@npm:^2.2.0":
  version: 2.2.8
  resolution: "w3c-keyname@npm:2.2.8"
  checksum: 95bafa4c04fa2f685a86ca1000069c1ec43ace1f8776c10f226a73296caeddd83f893db885c2c220ebeb6c52d424e3b54d7c0c1e963bbf204038ff1a944fbb07
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which@npm:^1.3.1":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: f17e84c042592c21e23c8195108cff18c64050b9efb8459589116999ea9da6dd1509e6a1bac3aeebefd137be00fabbb61b5c2bc0aa0f8526f32b58ee2f545651
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"wmf@npm:~1.0.1":
  version: 1.0.2
  resolution: "wmf@npm:1.0.2"
  checksum: d336acb2c76fa868ef006fbb06c4e64c7c1ed5ff77d16c48a273cf1f4d0a44e35df209b8fde28d93dd4a924d652a9c4fc8a92ad57885a5e437df0b0900769e3b
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"word@npm:~0.3.0":
  version: 0.3.0
  resolution: "word@npm:0.3.0"
  checksum: f84e7061883380c1bcb0d98bd183b7f2b281688011924af7a96d3ed3ee20aeb12cc59a0451b66e5e57520338a056725ff8e0c07b358c0afecf5488a9557c19fe
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 6cd96a410161ff617b63581a08376f0cb9162375adeb7956e10c8cd397821f7eb2a6de24eb22a0b28401300bf228c86e50617cd568209b5f6775b93c97d2fe3a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^5.0.1":
  version: 5.0.1
  resolution: "write-file-atomic@npm:5.0.1"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^4.0.1
  checksum: 8dbb0e2512c2f72ccc20ccedab9986c7d02d04039ed6e8780c987dc4940b793339c50172a1008eed7747001bfacc0ca47562668a069a7506c46c77d7ba3926a9
  languageName: node
  linkType: hard

"xlsx@npm:^0.18.5":
  version: 0.18.5
  resolution: "xlsx@npm:0.18.5"
  dependencies:
    adler-32: ~1.3.0
    cfb: ~1.2.1
    codepage: ~1.15.0
    crc-32: ~1.2.1
    ssf: ~0.11.2
    wmf: ~1.0.1
    word: ~0.3.0
  bin:
    xlsx: bin/xlsx.njs
  checksum: c5774d3c6abdf2db24f33a7b786e305255dac0e41a4e6bf6167c3f8517bfb5bfcb98e8207c39d5105f8304aa7416758da0400a993fb79aaf8e2ea4cfa8801f2e
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0, yaml@npm:^1.10.2":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: ce4ada136e8a78a0b08dc10b4b900936912d15de59905b2bf415b4d33c63df1d555d23acb2a41b23cf9fb5da41c256441afca3d6509de7247daa062fd2c5ea5f
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.3, yargs-parser@npm:^20.2.9":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 8bb69015f2b0ff9e17b2c8e6bfe224ab463dd00ca211eece72a4cd8a906224d2703fb8a326d36fdd0e68701e201b2a60ed7cf81ce0fd9b3799f9fe7745977ae3
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^17.2.1, yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"yoctocolors-cjs@npm:^2.1.2":
  version: 2.1.2
  resolution: "yoctocolors-cjs@npm:2.1.2"
  checksum: 1c474d4b30a8c130e679279c5c2c33a0d48eba9684ffa0252cc64846c121fb56c3f25457fef902edbe1e2d7a7872130073a9fc8e795299d75e13fa3f5f548f1b
  languageName: node
  linkType: hard

"yt-player@npm:^3.6.1":
  version: 3.6.1
  resolution: "yt-player@npm:3.6.1"
  dependencies:
    load-script2: ^2.0.1
  checksum: 05d16c15b8f4a3adea8f2d6e8ea63c10e9f26c69fca744fe047b4eeeff8c7cf2a14d30c93f2cb319adb08b268bc1a8fb10f1e17f1c441130f2415711d9e95ed5
  languageName: node
  linkType: hard

"zrender@npm:5.6.0":
  version: 5.6.0
  resolution: "zrender@npm:5.6.0"
  dependencies:
    tslib: 2.3.0
  checksum: 58875a8ed8b813c739f6b6e727eb5eead39b66278143ff231cdd270349a5743af034f7f20567c7895eecb1cadbdae499e17708d96f1faae958ad3acdb3d46687
  languageName: node
  linkType: hard
