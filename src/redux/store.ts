import { configureStore } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import appReducer from './slices/appSlice'
import filtersReducer from './slices/filterSlice/filtersSlice'
import formReducer from './slices/formSlice'
import languageSlice from './slices/languageSlice'
import loginReducer from './slices/loginSlice'
import popupReducer from './slices/popupSlice'
import previewReducer from './slices/previewSlice'
import searchReducer from './slices/searchSlice'
import tagMapReducer from './slices/tagMapSlice'

const store = configureStore({
  reducer: {
    // page: pageReducer,
    app: appReducer,
    form: formReducer,
    search: searchReducer,
    filters: filtersReducer,
    tagsMap: tagMapReducer,
    popup: popupReducer,
    login: loginReducer,
    language: languageSlice,
    preview: previewReducer,
    //...add other redcers here
  },
})
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch: () => AppDispatch = useDispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector

export default store
