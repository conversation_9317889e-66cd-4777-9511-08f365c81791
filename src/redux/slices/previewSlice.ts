import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import componentFields from '../../lib/data/fieldConfig.json'
import { lowerCaseFirstLetter } from '../../systems/AFS/AFSPages/utils'
import { fetchPageDataFromSlug } from '../../utils/pageUtils'

const initialState = {
  page: {},
  editingForm: {
    activerId: '',
    componentType: '',
    Template: '',
    fields: [],
    formValues: {}
  },
}


export const fetchPageBySlug = createAsyncThunk(
  'page/fetchBySlug',
  async (slug: string) => {
    const response = await fetchPageDataFromSlug(slug, "en-CA")
    return await JSON.parse(
      response?.data?.recordCollection?.items?.[0]?.props?.json
        ?.content?.[0]?.content?.[0]?.value
    )
  },
)
const getFormValues = (obj, componentId, state) => {


  if (Array.isArray(obj)) {
    obj?.forEach((item) => {
      getFormValues(item, componentId, state)
    })
  } else if (obj?.sys && obj?.sys?.id === componentId) {
    if (lowerCaseFirstLetter(obj?.__typename) in obj) {
      state.editingForm.formValues = obj[lowerCaseFirstLetter(obj?.__typename)]
    } else {
      state.editingForm.formValues = obj
    }
    // console.log("formValues found component", current(formValues), current(obj));
  } else if (typeof obj === 'object') {
    // Recursively traverse nested objects and arrays
    for (let key in obj) {
      if (typeof obj[key] === 'object') {
        getFormValues(obj[key], componentId, state)
      }
    }
  }
}

const previewSlice = createSlice({
  name: 'preview',
  initialState,
  reducers: {
    // Generic update function
    updateComponentProp: (state, action) => {
      const { componentId, propName, value, isAppendChild } = action.payload

      // Recursively search for the component in hero object
      const updateComponent = (obj) => {
        if (Array.isArray(obj)) {
          obj?.forEach((item) => {
            updateComponent(item)
          })
        } else if (obj?.sys && obj?.sys?.id === componentId) {
          if (lowerCaseFirstLetter(obj?.__typename) in obj) {
            obj[lowerCaseFirstLetter(obj?.__typename)][propName] = value
          } else if (propName in obj) {
            if (isAppendChild && obj[propName].items && Array.isArray(obj[propName].items)) {
              obj[propName].items.push(value);  // Append the child
            } else {
              obj[propName] = value // Update the property
            }
          }
        } else if (typeof obj === 'object') {
          // Recursively traverse nested objects and arrays
          for (let key in obj) {
            if (typeof obj[key] === 'object') {
              updateComponent(obj[key])
            }
          }
        }
      }
      updateComponent(state.page)
    },
    updateEditingForm: (state, action) => {
      const { componentId, componentType } = action.payload
      // Update the activerId and componentType in editingForm
      state.editingForm.activerId = componentId
      state.editingForm.componentType = componentType
      const formFieldsData = componentFields?.components?.find(
        (el) => el?.id === componentType
      )

      // console.log('fieldValues', fieldValues, current(state.page), componentId);
      // Dynamically add fields based on componentType
      // console.log('formFieldsData', formFieldsData, componentId, componentType, state.editingForm);



      if (formFieldsData) {
        // Merge the new fields from componentFields into the existing editingForm
        state.editingForm = {
          ...state.editingForm,
          ...formFieldsData,
          //   fields: formFieldsData?.fields,
        }
        getFormValues(state.page, componentId, state)
      } else {
        // state.editingForm = {
        //   ...state.editingForm,
        //   Template: '',
        //   fields: [],
        // }
      }
    },
    // Optionally reset the editing form to its initial state
    resetEditingForm: (state) => {
      state.editingForm = {
        activerId: '',
        componentType: '',
        Template: '',
        fields: [],
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchPageBySlug.fulfilled, (state, action) => {
      state.page = action.payload
    })
  },
})

export const { updateComponentProp, updateEditingForm, resetEditingForm } =
  previewSlice.actions
export default previewSlice.reducer
