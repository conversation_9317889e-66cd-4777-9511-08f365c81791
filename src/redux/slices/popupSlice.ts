import { createSlice } from '@reduxjs/toolkit'
import { PayloadAction } from '@reduxjs/toolkit/dist/createAction'

// define initial state here
const initialState = {
  isShow: false,
  popupData: {},
}

const popupSlice = createSlice({
  name: 'popup',
  initialState,
  reducers: {
    setShow: (state, action: PayloadAction<boolean>) => {
      state.isShow = action.payload
    },
    setPopupData: (state, action: PayloadAction<{}>) => {
      state.popupData = action.payload
    },
  },
})

//export actions like this..
export const { setShow, setPopupData } = popupSlice.actions

export default popupSlice.reducer
