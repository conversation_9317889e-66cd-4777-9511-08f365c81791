import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { formFieldsData } from '../../systems/AltusForms/Form/api-data'
// import { RootState, useAppSelector } from '../store'

// define initial state here
const initialState = {
  isFormVisible: true,
  values: {},
  progress: {
    isLoading: false,
    status: null,
    message: null,
  },
  altusFormId: null,
  formData: {},
  altusFormFields: formFieldsData.values,
  altusEndpointUrl: null,
  hasValidationError: false,
  emailValidationMsg: null,
  floatingFormEmailError: null,
  isFormActivated: {},
}

const formSlice = createSlice({
  name: 'form',
  initialState,
  reducers: {
    setIsFormActivated: (
      state,
      action: PayloadAction<{ contentfulFormId: string }>
    ) => {
      state.isFormActivated = {
        ...state.isFormActivated,
        [action.payload.contentfulFormId]: true,
      }
    },
    deleteFormActivation: (
      state,
      action: PayloadAction<{ contentfulFormId: string }>
    ) => {
      const temp = { ...state.isFormActivated }
      delete temp[action.payload.contentfulFormId]
      state.isFormActivated = { ...temp }
    },
    setIsFormVisible: (state, action: PayloadAction<boolean>) => {
      state.isFormVisible = action.payload
    },
    setFormData: (state, action: PayloadAction<any>) => {
      state.formData = action.payload
    },
    changeFormValues: (state, action: PayloadAction<any>) => {
      state.altusFormFields = action.payload
    },
    setFormFields: (state, action: PayloadAction<any>) => {
      state.altusFormFields = action.payload
    },
    setFormId: (state, action: PayloadAction<any>) => {
      state.altusFormId = action.payload
    },
    setEndpointUrl: (state, action: PayloadAction<any>) => {
      state.altusEndpointUrl = action.payload
    },
    changeFormValueMain: (state, action: PayloadAction<any>) => {
      state.values = { ...state.values, ...action.payload }
    },
    assignFormValues: (state, action: PayloadAction<any>) => {
      state.values = { ...action.payload ?? {} }
    },
    resetFormValueMain: (state) => {
      state.values = {}
    },
    setProgress: (state, action: PayloadAction<any>) => {
      state.progress = { ...state.progress, ...action.payload }
    },
    setHasValidationError: (state, action: PayloadAction<boolean>) => {
      state.hasValidationError = action.payload
    },
    setEmailValidationMsg: (state, action: PayloadAction<string | null>) => {
      state.emailValidationMsg = action.payload
      state.hasValidationError = !!action.payload
    },
    setFloatingFormEmailError: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.floatingFormEmailError = action.payload
    },
  },
})

//export actions like this..
export const {
  setIsFormActivated,
  deleteFormActivation,
  setIsFormVisible,
  setFormData,
  changeFormValues,
  setFormFields,
  setFormId,
  setEndpointUrl,
  changeFormValueMain,
  assignFormValues,
  resetFormValueMain,
  setProgress,
  setHasValidationError,
  setEmailValidationMsg,
  setFloatingFormEmailError,
} = formSlice.actions

export default formSlice.reducer
