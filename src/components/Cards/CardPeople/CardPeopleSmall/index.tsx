'use client'

import { RouteToUrl } from '../../../../globals/utils'
import { ImageAvatar } from '../../../Avatars'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import SimpleCTAs from '../../../CTAs/SimpleCTAs'
import SimpleCardPeople from '../SimpleCardPeople'
import { CardPeopleSmallD } from './defaults'
import styles from './index.module.scss'
import { CardPeopleSmallI } from './interface'

export default function CardPeopleSmall(props: CardPeopleSmallI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardPeopleSmallI = {
    ...CardPeopleSmallD,
    ...props,
  }

  //     getCardPeopleSmallSEObj(updatedProps)

  const RedirectFunction = () => {
    updatedProps.cta &&
      RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }

  const color = updatedProps.isLightMode ? 'cn2' : 'cn8'
  const headingStyles = updatedProps.isLightMode
    ? styles.heading
    : styles.headingDark

  return (
    <SimpleCardPeople
      {...updatedProps}
      htmlAttr={{
        onClick: RedirectFunction,
        className: `${styles.cardProfile}  ${updatedProps.cta ? styles.hoverClass : styles.nohover
          } ${updatedProps.htmlAttr?.className}`,
      }}
      isInteractive={updatedProps.cta == null ? false : true}
    >
      {updatedProps.children}

      {/* Card */}
      <div className={styles.profileInfo}>
        {/* Conditionally render ImageAvatar */}
        {updatedProps?.person?.avatar?.src && (
          <ImageAvatar
            src={updatedProps?.person?.avatar?.src}
            alt={`${updatedProps?.person?.avatar?.alt}'s Profile`}
            htmlAttr={{
              ...updatedProps?.person?.avatar?.htmlAttr,
              className: `${styles.profilePhoto} ${updatedProps?.person?.avatar?.htmlAttr?.className}`,
            }}
          />
        )}
      </div>

      {/* Person Info */}
      <div className={`${styles.details} ${color}`}>
        {updatedProps.cta ? (
          <SimpleCTAs
            {...updatedProps.cta}
            locale={props?.locale}
            htmlAttr={{ className: styles.cta }}
          >
            {' '}
            <SimpleHeading
              htmlAttr={{ className: headingStyles }}
              textContent={updatedProps?.person?.fullName?.textContent}
              as='h6'
            />
          </SimpleCTAs>
        ) : (
          <SimpleHeading
            htmlAttr={{ className: `${headingStyles}` }}
            textContent={updatedProps?.person?.fullName?.textContent}
            as='h6'
          />
        )}
        <SimpleParagraph
          textContent={updatedProps?.person?.jobTitle?.textContent}
          htmlAttr={{ className: styles.jobTitle }}
        />
      </div>
    </SimpleCardPeople>
  )
}
