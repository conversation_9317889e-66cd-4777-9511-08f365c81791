'use client'
import { RouteToUrl } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import { ImageAvatar } from '../../../Avatars'
import { Tag } from '../../../Bubbles'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import CardPeopleSmall from '../CardPeopleSmall'
import SimpleCardPeople from '../SimpleCardPeople'
import { CardPeopleLandscapeD } from './defaults'
import styles from './index.module.scss'
import { CardPeopleLandscapeI } from './interface'

export default function CardPeopleLandscape(props: CardPeopleLandscapeI) {
  // const [dimensions, setDimensions] = useState({
  //     width: window.innerWidth,
  //     height: window.innerHeight

  /*     // Check if the screen width is less than a certain breakpoint (e.g., 768px) and set orientation to landscape
          const isMobile = dimensions.width < 733 */

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardPeopleLandscapeI = {
    ...CardPeopleLandscapeD,
    ...props,
  }

  //     getCardPeopleLandscapeSEObj(updatedProps)

  const { size } = useWindowSize()

  const RedirectHandle = () => {
    updatedProps?.cta &&
      RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }

  /*    useEffect(() => {
             // Handler to call on window resize
             function handleResize() {
                 // Set window width/height to state
                 setDimensions({
                     width: window.innerWidth,
                     height: window.innerHeight
                 })
             }

             // Add event listener
             window.addEventListener('resize', handleResize)
             // Call handler right away so state gets updated with initial window size
             handleResize()

             // Remove event listener on cleanup
             return () => window.removeEventListener('resize', handleResize)
         }, []) */

  const Tags = (
    <div className={styles.tags}>
      {updatedProps.tags?.map((tag, idx) => (
        <Tag
          {...tag}
          size='small'
          htmlAttr={{
            style: {
              marginRight: '9px',
            },
          }}
          key={idx}
          isIcon={false}
        />
      ))}
    </div>
  )

  const descriptionStyle = updatedProps.isLightMode
    ? styles.description
    : styles.descriptionDark
  const color = updatedProps.isLightMode ? 'cn2' : 'cn8'
  const headingStyles = updatedProps.isLightMode
    ? styles.heading
    : styles.headingDark
  const companyStyles = updatedProps.isLightMode
    ? styles.companyName
    : styles.companyNameDark
  const bioStyles = updatedProps.isLightMode ? styles.bio : styles.bioDark
  const profileCtaStyle = updatedProps.isLightMode
    ? styles.viewProfileButton
    : styles.viewProfileButtonDark

  const profileCtaStyle1 = updatedProps.isLightMode
    ? styles.viewProfileButton1
    : styles.viewProfileButtonDark1
  const content =
    size === 'small' ? (
      <CardPeopleSmall {...updatedProps} cta={updatedProps.cta} />
    ) : (
      <SimpleCardPeople
        {...updatedProps}
        htmlAttr={{
          className: `${updatedProps?.htmlAttr?.className} ${styles.cardProfile
            } ${updatedProps.cta ? styles.hoverClass : styles.nohover}`,
          onClick: RedirectHandle,
        }}
      >
        {updatedProps.children}

        <div className={styles.profileInfo}>
          {/* Person Avatar */}
          <ImageAvatar
            src={updatedProps?.person?.avatar?.src}
            alt={`${updatedProps?.person?.avatar?.alt}'s Profile`}
            htmlAttr={{ className: styles.image }}
          />

          {/* Person Info */}
          <div className={`${styles.details} ${color}`}>
            <SimpleHeading
              htmlAttr={{ className: headingStyles }}
              textContent={updatedProps?.person?.fullName?.textContent}
              as='h6'
            />
            <SimpleParagraph
              textContent={updatedProps?.person?.jobTitle?.textContent}
              htmlAttr={{ className: styles.jobTitle }}
            />
            <SimpleParagraph
              textContent={updatedProps?.person?.companyName?.textContent}
              htmlAttr={{ className: companyStyles }}
            />
            {!updatedProps?.person?.bio &&
              updatedProps.tags?.length === 0 &&
              updatedProps.cta && (
                <SimpleButtonWIcon
                  htmlAttr={{ className: profileCtaStyle1 }}
                  {...updatedProps.cta}
                  locale={props?.locale}
                  isLightMode={updatedProps.isLightMode}
                />
              )}
            {Tags}
          </div>
        </div>

        {/* Bio */}
        {updatedProps?.person?.bio && (
          <div className={descriptionStyle}>
            {/* <SimpleParagraph
                    textContent={truncateText(updatedProps.person.bio?.textContent ?? '', 30)}
                    htmlAttr={{className: bioStyles}}
                /> */}
            <Richtext
              {...updatedProps?.person?.bio}
              htmlAttr={{ className: bioStyles }}
            />
          </div>
        )}

        {/* Link */}
        {updatedProps.cta &&
          (updatedProps?.person?.bio || updatedProps?.tags?.length > 0) && (
            <SimpleButtonWIcon
              htmlAttr={{ className: profileCtaStyle }}
              {...updatedProps.cta}
              locale={props?.locale}
              isLightMode={updatedProps.isLightMode}
            />
          )}
      </SimpleCardPeople>
    )

  return content
}
