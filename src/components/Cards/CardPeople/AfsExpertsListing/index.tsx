import React, { useEffect } from 'react'
import { handleElementScrollIntoView } from '../../../../globals/utils'
import { getSortOptions } from '../../../../systems/AFS/AFSPages/Insights/afsInsights.mapping'
import { sortBy } from '../../../../systems/AFS/lib/sorting'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import Dropdown from '../../../Controls/Dropdown'
import Pagination from '../../../Navs/Pagination'
import SimpleCardGroup from '../../CardGroupSimple'
import CardPeopleLandscapeRow from '../CardPeopleLandscapeRow'
import { AfsExpertsListingD } from './default'
import styles from './index.module.scss'
import { AfsExpertsListingI } from './interface'

export default function AfsExpertsListing(props: AfsExpertsListingI) {
  const updateProps: AfsExpertsListingI = {
    ...AfsExpertsListingD,
    ...props,
    Sort: getSortOptions(props?.locale),
  }

  const [sortValue, setSortValue] = React.useState<string>('name')
  const [cards, setCards] = React.useState(updateProps?.cards)
  const [currPage, setCurrPage] = React.useState(1)

  const onPageChange = (page: number) => {
    setCurrPage(page)
  }

  // update sort value
  const updateSortValue = (value) => {
    setSortValue(value)
  }

  // handle scroll into view on page change
  useEffect(() => {
    handleElementScrollIntoView(`.${styles.EventContainer}`, 60, 305)
  }, [currPage])

  // Effect to perform sorting when sort value changes
  useEffect(() => {
    const newCards = sortBy(props?.cards, sortValue.toLowerCase())
    setCards(newCards)
  }, [sortValue])

  // sorting for every new props
  useEffect(() => {
    const cards = sortBy(updateProps?.cards, sortValue.toLowerCase())
    setCards(cards)
  }, [updateProps.cards])

  let children = updateProps.children
  const cardComponents =
    cards &&
    cards.slice(currPage * 12 - 12, currPage * 12).map((card, index) => (
      <div key={index} className={styles.cardContent}>
        <CardPeopleLandscapeRow
          {...card}
          isLightMode={updateProps.isLightMode}
        />
        {index !== updateProps.cards.length - 1 && (
          <hr className={styles.hrLine} />
        )}
      </div>
    ))

  return (
    <SimpleCardGroup {...updateProps}>
      <div className={styles.EventContainer}>
        <div className={styles.simpleFlex}>
          <div className={styles.dropDown}>
            <Dropdown
              {...updateProps.Sort}
              dropDownTab={{
                ...updateProps.Sort?.dropDownTab,
                fixedWidth: false,
                htmlAttr: { className: styles.dropDownTab },
              }}
              isLightMode={updateProps.isLightMode}
              onChange={updateSortValue}
              selected={sortValue}
            />
          </div>

          <SimpleHeading {...updateProps.results} as='h6' displayAs='h6' />
        </div>
        <div>{cardComponents}</div>
        {cards && cards.length > 12 && (
          <Pagination
            onPageChange={onPageChange}
            currentPage={currPage}
            totalCount={updateProps.cards.length}
            pageSize={12}
          />
        )}
      </div>
      {children}
    </SimpleCardGroup>
  )
}
