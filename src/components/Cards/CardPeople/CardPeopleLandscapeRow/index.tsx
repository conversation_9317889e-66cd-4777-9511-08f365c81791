'use client'
import { useState } from 'react'
import { RouteToUrl } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import { ImageAvatar } from '../../../Avatars'
import { Tag } from '../../../Bubbles'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import CardPeopleSmall from '../CardPeopleSmall'
import SimpleCardPeople from '../SimpleCardPeople'
import { CardPeopleLandscapeRowD } from './defaults'
import styles from './index.module.scss'
import { CardPeopleLandscapeRowI } from './interface'

export default function CardPeopleLandscapeRow(props: CardPeopleLandscapeRowI) {
  // const [dimensions, setDimensions] = useState({
  //     width: window.innerWidth,
  //     height: window.innerHeight

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardPeopleLandscapeRowI = {
    ...CardPeopleLandscapeRowD,
    ...props,
  }
  const [height, setHeight] = useState<number>(0)

  //     getCardPeopleLandscapeRowSEObj(updatedProps)

  const RedirectHandle = () => {
    updatedProps.cta &&
      RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }

  const { size } = useWindowSize()

  const Tags = (
    <div className={styles.tags}>
      {updatedProps.tags?.map((tag, idx) => (
        <Tag
          {...tag}
          size='small'
          htmlAttr={{
            style: {
              marginRight: '9px',
            },
          }}
          key={idx}
          isIcon={false}
        />
      ))}
    </div>
  )

  const descriptionStyle = updatedProps.isLightMode
    ? styles.description
    : styles.descriptionDark
  const color = updatedProps.isLightMode ? 'cn2' : 'cn8'
  const headingStyles = updatedProps.isLightMode
    ? styles.heading
    : styles.headingDark
  const companyStyles = updatedProps.isLightMode
    ? styles.companyName
    : styles.companyNameDark
  const bioStyles = updatedProps.isLightMode ? styles.bio : styles.bioDark
  const profileCtaStyle = updatedProps.isLightMode
    ? styles.viewProfileButton
    : styles.viewProfileButtonDark

  /* let hideTextClass=`${
      updatedProps.isLightMode ? styles.hideOverflow : styles.hideOverflowDark
  }` */

  //blur effect logic
  /*  const headingRef = useRef<HTMLHeadingElement | null>(null)

       useEffect(() => {
         if (headingRef.current) {
           const Height = headingRef.current.clientHeight
         setHeight(Height)
       }
       }, [size, updatedProps?.person?.bio]);

       let linHeight=22;

       let hideClass= height>=linHeight*3 ? hideTextClass:'' */

  const content =
    size === 'small' ? (
      <CardPeopleSmall {...updatedProps} cta={updatedProps.cta} />
    ) : (
      <SimpleCardPeople
        {...updatedProps}
        htmlAttr={{
          className: `${styles.cardProfile} ${updatedProps.cta ? styles.hoverClass : styles.nohover
            }`,
          onClick: RedirectHandle,
        }}
      >
        {updatedProps.children}

        {/* Card */}
        <div className={styles.profileInfo}>
          {/* Person Avatar */}
          <ImageAvatar
            src={updatedProps?.person?.avatar?.src}
            alt={`${updatedProps?.person?.avatar?.alt}'s Profile`}
            htmlAttr={{
              className: styles.profilePhoto,
            }}
          />
        </div>

        {/* Person Info */}
        <div className={`${styles.details} ${color}`}>
          <SimpleHeading
            htmlAttr={{ className: headingStyles }}
            textContent={updatedProps?.person?.fullName?.textContent}
            as='h6'
          />
          <SimpleParagraph
            textContent={updatedProps?.person?.jobTitle?.textContent}
            htmlAttr={{ className: styles.jobTitle }}
          />
          <SimpleParagraph
            textContent={updatedProps?.person?.companyName?.textContent}
            htmlAttr={{ className: companyStyles }}
          />

          {Tags}

          {/* Bio */}
          {updatedProps?.person?.bio && (
            <div className={descriptionStyle}>
              <Richtext
                {...updatedProps?.person?.bio}
                htmlAttr={{ className: `${bioStyles}` }}
              />
            </div>
          )}

          {/* Link */}
          {
            /* updatedProps?.showButton === true && */ updatedProps.cta ? (
              <SimpleButtonWIcon
                htmlAttr={{ className: profileCtaStyle }}
                {...updatedProps.cta}
                locale={props?.locale}
              />
            ) : undefined
          }
        </div>
      </SimpleCardPeople>
    )

  return content
}
