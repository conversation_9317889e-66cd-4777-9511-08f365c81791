//Default SCSS variables are prepended in next.config.js

.cardGenericPortraitRoot {
  max-width: 436px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;

  .image {
    height: 150px;
    border-radius: 4px 4px 0 0;

    img {
      border-radius: 4px 4px 0 0;
      transition: transform 0.3s ease;
      object-fit: cover;
    }
  }

  .cardInfo {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    text-wrap: wrap;

    .date {
      font-family: $fSansReg;
      font-size: $fs3;
      line-height: 22px;
      margin-bottom: 10px;

      @media screen and (max-width: $smScreenSize) {
        font-size: $fs2;
      }
    }

    .hideOverflow {
      max-height: 90px;
      overflow: hidden;

      &::after {
        content: '';
        /* Required for pseudo-elements */
        position: absolute;
        /* Position it absolutely relative to the paragraph */
        bottom: 0;
        /* Align it with the top of the paragraph */
        right: 0;
        /* Align it with the right side of the paragraph */
        width: 150px;
        /* Adjust the width of the blurred area as needed */
        height: 30px;
        /* Make it cover the entire height of the line */
        background: linear-gradient(270deg, $cs2 21.79%, #ffffff00 94.87%);

        /* Use a linear gradient for the blur effect */
        @media screen and (max-width: $smScreenSize) {
          height: 28px;
        }
      }

      @media screen and (max-width: $smScreenSize) {
        max-height: 78px;
      }
    }

    .hideOverflowDark {
      max-height: 90px;
      overflow: hidden;

      &::after {
        content: '';
        /* Required for pseudo-elements */
        position: absolute;
        /* Position it absolutely relative to the paragraph */
        bottom: 0;
        /* Align it with the top of the paragraph */
        right: 0;
        /* Align it with the right side of the paragraph */
        width: 150px;
        /* Adjust the width of the blurred area as needed */
        height: 30px;
        /* Make it cover the entire height of the line */
        background: linear-gradient(270deg, $cp1 21.79%, #ffffff00 94.87%);

        /* Use a linear gradient for the blur effect */
        @media screen and (max-width: $smScreenSize) {
          height: 28px;
        }
      }

      @media screen and (max-width: $smScreenSize) {
        max-height: 78px;
      }
    }

    .margin {
      margin-bottom: 20px;

      @media screen and (max-width: $smScreenSize) {
        margin-bottom: 30px;
      }
    }

    .heading {
      color: $cp1;
      position: relative;
      height: fit-content;
    }

    .headingDark {
      color: $cs2;
      position: relative;
      height: fit-content;
    }

    .description {
      font-size: $fs4;
      line-height: 24px;
      margin-bottom: 30px;

      @media screen and (max-width: $smScreenSize) {
        display: none;
      }
    }
  }

  .button {
    // color: $cp1;
  }

  .darkButton {
    // color: $cn8;
  }

  &:hover {
    img {
      transform: scale(1.1);
      /* Zoom in by 10% on hover */
    }

    .heading {
      // color: $cp2;
    }

    .headingDark {
      // color: $cs1;
    }

    .button {
      //color: $cp2;
    }

    .darkButton {
      // color: $cs1;
    }

    &.darkMode {
      background: $cp2;

      .heading,
      .date,
      .description {
        color: $cs2;
      }

      .button {
        color: $cs1;
      }

      .hideOverflow::after {
        background: linear-gradient(
          270deg,
          #0028d7 21.79%,
          rgba(0, 40, 215, 0.1) 94.87%
        );
      }
    }
  }

  @media screen and (max-width: $smScreenSize) {
    min-width: $minWidth;
    max-width: 436px;
  }
}

.cta {
  &:hover {
    text-decoration: none !important;
  }
}
.margin {
  margin-bottom: 20px;

  @media screen and (max-width: $smScreenSize) {
    margin-bottom: 30px;
  }
}
.block{
  display: block;
}