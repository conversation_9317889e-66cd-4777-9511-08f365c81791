'use client'
import { useEffect, useRef, useState } from 'react'
import { RouteToUrl, useTranslation } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import { SimpleImage } from '../../../Multimedia/Images'
import SimpleCardGeneric from '../SimpleCardGeneric'
import styles from './index.module.scss'
import { CardGenericPortraitI } from './interface'

export default function CardGenericPortrait(props: CardGenericPortraitI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  let getLocaleStr = ''
  if (!props?.cta?.href?.includes('lang')) {
    let localePrefix = ''

    const isSearchParams = props.cta?.href?.split('?').length > 1

    localePrefix =
      props.locale && props.locale !== 'en-CA'
        ? `${isSearchParams ? '&' : '?'}lang=${props.locale.split('-')[0]}`
        : ''
    getLocaleStr = localePrefix
  }

  const updatedProps: CardGenericPortraitI = {
    //...CardGenericPortraitD,
    ...props,
    cta: {
      ...props.cta,
      href: props?.cta?.href + getLocaleStr,
      textContent:
        props?.cta?.textContent === 'Read more'
          ? useTranslation('readMore', props.locale)
          : props?.cta?.textContent,
    },
  }
  const [height, setHeight] = useState<number>(0)

  let dateColor = `${!updatedProps.isLightMode ? 'cs2' : 'cn2'}`
  let ExcerptColor = `${!updatedProps.isLightMode ? 'cs2' : 'cp1'}`
  let heading = `${
    !updatedProps.isLightMode ? styles.headingDark : styles.heading
  }`
  let button = `${updatedProps.isLightMode ? styles.button : styles.darkButton}`

  let hideTextClass = `${
    updatedProps.isLightMode ? styles.hideOverflow : styles.hideOverflowDark
  }`

  const { size } = useWindowSize()
  const headingRef = useRef<HTMLHeadingElement | null>(null)
  //blur effect logic
  useEffect(() => {
    if (headingRef.current) {
      const Height = headingRef.current.clientHeight
      setHeight(Height)
    }
  }, [size, updatedProps.heading])

  let linHeight = size === 'small' ? 26 : 30

  let hideClass = height >= linHeight * 3 ? hideTextClass : ''

  // const date = getDateAndTime(updatedProps.date?.textContent!, null)
  // const text = truncateText(updatedProps.heading?.textContent!, 11) //to be confirmed with felipe
  let image, excerpt, linkButton

  //if image is provided then image component will render
  if (updatedProps.thumbnail) {
    image = (
      <div className={styles.image}>
        {/* Display the image */}
        {updatedProps?.thumbnail?.src && (
          <SimpleImage
            {...updatedProps.thumbnail}
            width={'100%'}
            height={'100%'}
          />
        )}
      </div>
    )
  }

  //if description is provided then paragraph component will render
  if (
    updatedProps.excerpt?.data &&
    updatedProps.excerpt?.data?.content?.length > 0 &&
    updatedProps.excerpt?.data?.content?.at(0)?.content?.at(0)?.text
  ) {
    excerpt = (
      <Richtext
        {...updatedProps.excerpt}
        htmlAttr={{
          className: `${styles.description} ${ExcerptColor} ${updatedProps?.excerpt?.htmlAttr?.className}`,
        }}
      />
    )
  }

  //if link is provided then it will show link
  if (updatedProps.cta?.textContent) {
    linkButton = (
      <SimpleButtonWIcon
        {...updatedProps.cta}
        isLightMode={updatedProps.isLightMode}
        htmlAttr={{
          className: `${button} ${updatedProps?.cta?.htmlAttr?.className}`,
        }}
      />
    )
  }

  const HandleCardClick = () => {
    RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }

  //return SimpleCardGenericPortrait
  return (
    <SimpleCardGeneric
      {...updatedProps}
      htmlAttr={{
        className: `${styles.cardGenericPortraitRoot} ${
          updatedProps?.isLightMode && styles.darkMode
        } ${updatedProps.htmlAttr?.className}`,
        onClick: HandleCardClick,
      }}
    >
      {/* Create a link wrapping the image */}
      {image}
      {/* Create a card information container */}
      <div
        className={styles.cardInfo}
        style={updatedProps.thumbnail ? { padding: '30px' } : {}}
      >
        {/* Display the date */}
        {updatedProps?.date && (
          <SimpleParagraph
            {...updatedProps.date}
            textContent={updatedProps?.date?.textContent}
            htmlAttr={{
              className: `${dateColor} ${styles.date} ${updatedProps?.date?.htmlAttr?.className}`,
            }}
          />
        )}
        {/* Create a link wrapping the heading */}
        {/* Display the heading */}

        {/* <SimpleCTAs {...updatedProps.cta} htmlAttr={{ className: styles.cta }}> */}
        <div ref={headingRef} className={styles.margin}>
          <SimpleHeading
            {...updatedProps.heading}
            as='h3'
            displayAs='h4'
            htmlAttr={{
              className: `${heading} ${hideClass} ${updatedProps.heading?.htmlAttr?.className}`,
            }}
          />
        </div>
        {/* </SimpleCTAs> */}

        {/* Display the description */}
        {excerpt}
        {/* Create a link with text and an icon */}
        {linkButton}
      </div>
    </SimpleCardGeneric>
  )
}
