'use client'
import { useEffect, useRef, useState } from 'react'
import { l } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import { SimpleImage } from '../../../Multimedia/Images'
import CardImageHome from '../../CardIconLogo/CardImageHome'
import SimpleCardGeneric from '../SimpleCardGeneric'
import styles from './index.module.scss'
import { CardLandscapeHomeI } from './interface'

export default function CardLandscapeHome(props: CardLandscapeHomeI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardLandscapeHomeI = {
    //  ...CardLandscapeHomeD,

    ...props,
  }
  const [height, setHeight] = useState<number>(0)
  l('cards', updatedProps?.cards)

  const { size } = useWindowSize()

  const headingRef = useRef<HTMLHeadingElement | null>(null)
  //blur effect logic
  useEffect(() => {
    if (headingRef.current) {
      const Height = headingRef.current.clientHeight
      setHeight(Height)
    }
  }, [size, updatedProps.heading])

  const cardImages = updatedProps?.cards?.map((item, index) => {
    return <CardImageHome {...item} />
  })

  const isExcerpt =
    updatedProps.excerpt?.data &&
    updatedProps.excerpt?.data?.content?.length > 0 &&
    updatedProps.excerpt?.data?.content?.at(0)?.content?.at(0)?.text

  return (
    <SimpleCardGeneric
      {...updatedProps}
      htmlAttr={{
        className: `${styles.CardLandscapeHomeRoot} ${updatedProps?.isLightMode && styles.darkMode} ${updatedProps?.htmlAttr?.className} `,
      }}
    >
      {/* Display the image */}
      <div className={styles.image}>
        {updatedProps?.thumbnail?.src && (
          <SimpleImage {...updatedProps.thumbnail} width='100%' height='100%' />
        )}
      </div>

      {/* Create a card information container */}
      <div className={styles.cardInfo}>
        {/* Display the heading */}
        <div ref={headingRef} className={styles.heading}>
          <SimpleHeading {...updatedProps.heading} as={'h2'} />
        </div>

        {/* Display the description */}
        {isExcerpt && (
          <Richtext
            {...updatedProps.excerpt}
            htmlAttr={{ className: `${styles.description}` }}
          />
        )}

        <div className={styles.cards}>{cardImages}</div>
      </div>
    </SimpleCardGeneric>
  )
}
