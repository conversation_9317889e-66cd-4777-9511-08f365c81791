import CardGenericPortrait from '../CardGenericPortrait'
import style from './index.module.scss'
import { CardGenericHeadlineI } from './interface'

export default function CardGenericHeadline(props: CardGenericHeadlineI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardGenericHeadlineI = {
    //  ...CardGenericHeadlineD,
    ...props,
  }

  // const date = getDateAndTime(updatedProps.date?.textContent!, null)
  // const text = truncateText(updatedProps.heading?.textContent!, 11)
  let dateColor = `${!updatedProps.isLightMode ? style.darkDate : style.date}`
  let bgColor = `${!updatedProps.isLightMode ? style.bgDark : style.bg}`
  let heading = `${
    !updatedProps.isLightMode ? style.headingdark : style.heading
  }`

  return (
    <CardGenericPortrait
      {...updatedProps}
      isLightMode={updatedProps.isLightMode}
      date={
        updatedProps.date && {
          ...updatedProps.date,
          htmlAttr: { className: dateColor },
        }
      }
      cta={{ ...updatedProps.cta, textContent: undefined }}
      heading={{
        ...updatedProps.heading,
        as: 'h4',
        displayAs: 'h4',
        htmlAttr: { ...updatedProps.heading, className: heading },
      }}
      locale={updatedProps.locale}
      htmlAttr={{
        className: `${style.headlineRoot} ${bgColor} ${updatedProps?.htmlAttr?.className}`,
      }}
    />
  )
}
