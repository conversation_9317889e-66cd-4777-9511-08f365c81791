import LinkRouter from '../../../../lib/componentsRouter/LinkRouter'
import SimpleCarousel from '../../../Carousels/SimpleCarousel'
import FlexContainer from '../../../Containers/FlexContainer'
import Layout from '../../../Containers/Layout'
import LayoutContainer from '../../../Containers/LayoutContainer'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import SimpleCardGroup from '../../CardGroupSimple'
import '../CardGenericPortrait/index.module.scss'
import CardGenericSmall from '../CardGenericSmall'
import styles from './index.module.scss'
import { DynamicWidgetInsightsI } from './interface'

export default function DynamicWidgetInsights(props: DynamicWidgetInsightsI) {
  const updatedProps = { ...props }

  // useEffect(() => {
  //     getDynamicWidgetInsightsProps(props).then((res) => {
  //         setProps({...res})
  //     })
  // }, [props])

  // useEffect(() => {
  //     const firstCard = document.querySelector(`.${styles.firstCard}`);
  //     if (firstCard) {
  //       firstCard.classList.add(styles.showCard);
  //     }
  //   }, []);
  let children = updatedProps?.children
  // const { size } = isBrowser() ? useWindowSize() : { size: 'large' }

  const cardComponents = updatedProps?.Cards?.map((card, index) => (
    <FlexContainer
      width={'auto'}
      htmlAttr={{ className: styles.resizeContainer }}
      flex={'1'}
    >
      <CardGenericSmall
        {...card}
        key={index}
        isLightMode={updatedProps.isLightMode}
        locale={updatedProps.locale}
      />
    </FlexContainer>
  ))

  //logic to add extra column
  let extraCol = cardComponents?.length % 3 === 2 ? true : false
  let extra2Col = cardComponents?.length % 3 === 1 ? true : false

  const CarouselcardComponents = updatedProps?.Cards?.map((card, index) => (
    <CardGenericSmall
      {...card}
      key={index}
      isLightMode={updatedProps.isLightMode}
      htmlAttr={{
        ...card?.htmlAttr,
        className: `${styles.card} ${card?.htmlAttr?.className}`,
      }}
      locale={updatedProps.locale}
    />
  ))

  const cards =
    CarouselcardComponents?.length > 4
      ? CarouselcardComponents?.slice(0, 3)
      : CarouselcardComponents

  const carousel = (
    <SimpleCarousel
      isPresent
      isVisible
      {...updatedProps}
      htmlAttr={{ className: styles.carousel }}
      indicatorsPosition={'Normal'}
      indicatorActiveColour={'bp2'}
      indicatorInactiveColour={'bn3'}
      items={cards}
      isProgressBar={false}
      isLogoProgress={false}
      isTabProgress={false}
      timer={5000}
    >
      {props.children}
    </SimpleCarousel>
  )

  return updatedProps ? (
    <SimpleCardGroup
      {...props}
      isFullXBleed
      htmlAttr={{
        ...updatedProps?.htmlAttr,
        className: `${updatedProps?.htmlAttr?.className}`,
      }}
    >
      <LayoutContainer>
        <div className={styles.headingFlex}>
          <ContextualInformation
            {...updatedProps.contextualInformation}
            showButtons={false}
            subHeading={{
              ...updatedProps.contextualInformation?.subHeading,
              fontSize: 'fs4',
              colour: 'cp2',
            }}
            heading={{
              ...updatedProps.contextualInformation?.heading,
              as: 'h2',
            }}
            isLightMode={updatedProps.isLightMode}
          />

          <div className={styles.button}>
            <LinkRouter {...updatedProps.button} />
          </div>
        </div>

        {carousel}

        <Layout
          htmlAttr={{
            className: `${styles.layout} ${styles.layoutContainer} py2 defaultLayout `,
          }}
          flexWrap={'wrap'}
        >
          {cardComponents}
          {extraCol && (
            <FlexContainer
              width={'auto'}
              flex={'1'}
              htmlAttr={{ className: styles.resizeContainer }}
            />
          )}
          {extra2Col && (
            <>
              <FlexContainer
                width={'auto'}
                flex={'1'}
                htmlAttr={{ className: styles.resizeContainer }}
              />
              <FlexContainer
                width={'auto'}
                htmlAttr={{ className: styles.resizeContainer }}
                flex={'1'}
              />
            </>
          )}
        </Layout>

        <div className={styles.button2}>
          <LinkRouter {...updatedProps.button} />
        </div>

        {children}
      </LayoutContainer>
    </SimpleCardGroup>
  ) : (
    <p>loading...</p>
  )
}
