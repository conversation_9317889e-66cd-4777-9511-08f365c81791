'use client'
import { RouteToUrl } from '../../../../globals/utils'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleCTAs from '../../../CTAs/SimpleCTAs'
import { SimpleImage } from '../../../Multimedia/Images'
import SimpleCardGeneric from '../SimpleCardGeneric'
import styles from './index.module.scss'
import { CardGenericSearchI } from './interface'

export default function CardGenericSearch(props: CardGenericSearchI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardGenericSearchI = {
    // ...CardGenericSearchD,
    ...props,
  }

  let HeadingColor = `${!updatedProps.isLightMode ? styles.darkHeading : styles.heading
    }`
  let ExcerptColor = `${!updatedProps.isLightMode ? 'cs2' : 'cn3'}`
  let background = updatedProps.isLightMode ? styles.light : styles.dark

  // const text = truncateText(updatedProps.heading?.textContent!, 11) //to be confirmed with felipe

  const HandleCardClick = () => {
    RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }

  const isExcerpt =
    updatedProps.excerpt?.data &&
    updatedProps.excerpt?.data?.content?.length > 0 &&
    updatedProps.excerpt?.data?.content?.at(0)?.content?.at(0)?.text

  return (
    <SimpleCardGeneric
      {...updatedProps}
      htmlAttr={{
        className: `${styles.CardGenericSearchRoot} ${background} ${updatedProps?.htmlAttr?.className}`,
        onClick: HandleCardClick,
      }}
    >
      {/* Create a link wrapping the image */}
      <div className={styles.linkImage}>
        {/* Display the image */}
        {updatedProps?.thumbnail?.src && (
          <SimpleImage
            {...updatedProps.thumbnail}
            width={'100%'}
            height={'100%'}
          />
        )}
      </div>
      {/* Create a card information container */}
      <div className={styles.cardInfo}>
        {/* Display the heading */}
        <SimpleCTAs {...updatedProps.cta}
          locale={props?.locale}
          htmlAttr={{ className: styles.cta }}>
          <SimpleHeading
            {...updatedProps.heading}
            as='h6'
            htmlAttr={{
              className: `${styles.heading} ${HeadingColor}`,
            }}
          />
        </SimpleCTAs>

        {/* Display the description */}

        {/* <SimpleParagraph {...updatedProps.excerpt} htmlAttr={{className: `${styles.description} ${ExcerptColor}`}}/> */}
        {isExcerpt && (
          <Richtext
            {...updatedProps.excerpt}
            htmlAttr={{ className: `${styles.description} ${ExcerptColor}` }}
          />
        )}
        {/* Create a link with text and an icon */}
      </div>
    </SimpleCardGeneric>
  )
}
