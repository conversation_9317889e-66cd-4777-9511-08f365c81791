'use client'

import React, { useEffect } from 'react'

import SCSSVars from '../../../../brands/altus/styles/_nextSCSSVars.module.scss'
import { handleElementScrollIntoView } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import { getSortOptions } from '../../../../systems/AFS/AFSPages/Insights/afsInsights.mapping'
import { sortBy } from '../../../../systems/AFS/lib/sorting'
import FlexContainer from '../../../Containers/FlexContainer'
import Layout from '../../../Containers/Layout'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import Dropdown from '../../../Controls/Dropdown'
import Pagination from '../../../Navs/Pagination'
import SimpleCardGroup from '../../CardGroupSimple'
import CardGenericLandscape from '../CardGenericLandscape'
import CardGenericPortrait from '../CardGenericPortrait'
import { AfsInsightsListingD } from './default'
import styles from './index.module.scss'
import { AfsInsightsListingI } from './interface'

export default function AfsInsightsListing(props: AfsInsightsListingI) {
  const updateProps: AfsInsightsListingI = {
    ...AfsInsightsListingD,
    ...props,
    Sort: getSortOptions(props?.locale),
  }
  const [isPageChanged, setIsPageChanged] = React.useState(false)

  const [sortValue, setSortValue] = React.useState<string>('newest')
  const [cards, setCards] = React.useState(updateProps?.cards)
  const [currPage, setCurrPage] = React.useState(1)
  const [isFeaturedActive, setIsFeaturedActive] = React.useState()
  const { size } = useWindowSize()
  const onPageChange = (page: number) => {
    setCurrPage(page)
    setIsPageChanged(true)
  }

  // update sort value
  const updateSortValue = (value) => {
    setSortValue(value)
    setIsFeaturedActive(false)
  }

  // handle scroll into view on page change
  useEffect(() => {
    if (!isPageChanged) return

    handleElementScrollIntoView(`.${styles.insightsContainer}`, 60, 305)
  }, [currPage])

  // handle featured
  useEffect(() => {
    setIsFeaturedActive(props?.isFeaturedActive)
  }, [props.isFeaturedActive])

  // Effect to perform sorting when sort value changes
  useEffect(() => {
    const newCards = sortBy(props?.cards, sortValue.toLowerCase())
    setCards(newCards)
  }, [sortValue])

  // sorting for every new props
  useEffect(() => {
    const cards = sortBy(updateProps?.cards, sortValue.toLowerCase())
    setCards(cards)
  }, [updateProps.cards])

  // featured Card component
  const featuredCard = updateProps?.cards?.filter((card) => {
    return card?.tags?.some((tag) => tag === 'featured')
  })

  let children = updateProps.children
  const cardComponents =
    cards &&
    cards.slice(currPage * 12 - 12, currPage * 12).map((card, index) => {
      return (
        <FlexContainer
          width={'auto'}
          flex={'1'}
          minWidth={size === 'large' ? '29%' : SCSSVars.minW}
        >
          <CardGenericPortrait
            key={index}
            {...card}
            heading={{ ...card.heading, as: 'h3' }}
            thumbnail={{ ...card.thumbnail, height: '100%', width: '100%' }}
            isLightMode={updateProps.isLightMode}
            locale={updateProps.locale}
          />
        </FlexContainer>
      )
    })

  //logic to add extra column
  let extraCol =
    size !== 'small' && cardComponents?.length % 3 === 2 ? true : false
  let extra2Col =
    size !== 'small' && cardComponents?.length % 3 === 1 ? true : false

  //margin bottom class logic if pagination is not present
  const mb = cards?.length <= 12 && size !== 'mid' && styles.mb
  const extraColMb =
    size === 'mid' && cards?.length < 12 && cards?.length % 3 === 2 && styles.mb

  return (
    <SimpleCardGroup {...updateProps}>
      <div className={styles.insightsContainer}>
        <div className={styles.simpleFlex}>
          <div className={styles.dropDown}>
            <Dropdown
              {...updateProps.Sort}
              dropDownTab={{
                ...updateProps.Sort?.dropDownTab,
                fixedWidth: false,
                // tabText: {
                //     textContent: `${useTranslation("sortBy", props.locale)} ${lowerCaseFirstLetter(sortValue)}`
                // },
                htmlAttr: { className: styles.dropDownTab },
              }}
              isLightMode={updateProps.isLightMode}
              onChange={updateSortValue}
              selected={sortValue}
            />
          </div>

          <SimpleHeading
            {...updateProps.results}
            as='h6'
            displayAs='h6'
            isLightMode={updateProps.isLightMode}
          />
        </div>
        {isFeaturedActive && featuredCard.length > 0 && (
          <div className={styles.CardLarge}>
            <CardGenericLandscape {...featuredCard[0]} />
          </div>
        )}

        <Layout
          htmlAttr={{
            className: `${styles.layout} defaultLayout ${mb} ${extraColMb}`,
          }}
          flexWrap={'wrap'}
        >
          {cardComponents}
          {extraCol && (
            <FlexContainer
              width={'auto'}
              flex={'1'}
              minWidth={size === 'large' ? '29%' : SCSSVars.minW}
            >
              {' '}
            </FlexContainer>
          )}
          {extra2Col && (
            <>
              <FlexContainer
                width={'auto'}
                flex={'1'}
                minWidth={size === 'large' ? '29%' : SCSSVars.minW}
              >
                {' '}
              </FlexContainer>
              <FlexContainer
                width={'auto'}
                flex={'1'}
                minWidth={size === 'large' ? '29%' : SCSSVars.minW}
              >
                {' '}
              </FlexContainer>
            </>
          )}
        </Layout>
        {cards && cards.length > 12 && (
          <Pagination
            onPageChange={onPageChange}
            currentPage={currPage}
            totalCount={updateProps.cards.length}
            pageSize={12}
          />
        )}
      </div>
      {children}
    </SimpleCardGroup>
  )
}
