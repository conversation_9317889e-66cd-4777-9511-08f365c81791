'use client'
import { useEffect, useRef, useState } from 'react'
import { RouteToUrl } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import { SimpleImage } from '../../../Multimedia/Images'
import SimpleCardGeneric from '../SimpleCardGeneric'
import styles from './index.module.scss'
import { CardGenericLandscapeI } from './interface'

export default function CardGenericLandscape(props: CardGenericLandscapeI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardGenericLandscapeI = {
    //  ...CardGenericLandscapeD,

    ...props,
  }
  const [height, setHeight] = useState<number>(0)

  let dateColor = `${!updatedProps.isLightMode ? 'cs2' : 'cn2'}`
  let ExcerptColor = `${!updatedProps.isLightMode ? 'cs2' : 'cp1'}`
  let heading = `${!updatedProps.isLightMode ? styles.darkHeading : styles.heading
    }`
  let hideTextClass = `${updatedProps.isLightMode ? styles.hideOverflow : styles.hideOverflowDark
    }`

  const { size } = useWindowSize()

  const headingRef = useRef<HTMLHeadingElement | null>(null)
  //blur effect logic
  useEffect(() => {
    if (headingRef.current) {
      const Height = headingRef.current.clientHeight
      setHeight(Height)
    }
  }, [size, updatedProps.heading])

  let linHeight = size === 'small' || size === 'mid' ? 26 : 30

  let hideClass = height >= linHeight * 3 ? hideTextClass : ''

  //   const date = getDateAndTime(updatedProps.date?.textContent!, null)
  //   const date = updatedProps?.date?.textContent
  // const text = truncateText(updatedProps.heading?.textContent!, 11) //to be confirmed with felipe

  //handle click
  const HandleCardClick = () => {
    RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }
  const isExcerpt =
    updatedProps.excerpt?.data &&
    updatedProps.excerpt?.data?.content?.length > 0 &&
    updatedProps.excerpt?.data?.content?.at(0)?.content?.at(0)?.text

  return (
    <SimpleCardGeneric
      {...updatedProps}
      htmlAttr={{
        className: `${styles.cardGenericLandScapeRoot} ${updatedProps?.isLightMode && styles.darkMode} ${updatedProps?.htmlAttr?.className} `,
        onClick: HandleCardClick,
      }}
    >
      {/* Display the image */}
      <div className={styles.image}>
        {updatedProps?.thumbnail?.src && (
          <SimpleImage {...updatedProps.thumbnail} width='100%' height='100%' />
        )}
      </div>

      {/* Create a card information container */}
      <div className={styles.cardInfo}>
        {/* Display the date */}
        {updatedProps?.date && (
          <SimpleParagraph
            {...updatedProps.date}
            textContent={updatedProps?.date?.textContent}
            htmlAttr={{ className: `${dateColor} ${styles.date}` }}
          />
        )}

        {/* Display the heading */}
        <div ref={headingRef} className={styles.margin}>
          <SimpleHeading
            {...updatedProps.heading}
            as={'h3'}
            displayAs='h4'
            htmlAttr={{
              className: `${heading} ${hideClass}`,
            }}
          />
        </div>

        {/* Display the description */}
        {isExcerpt && (
          <Richtext
            {...updatedProps.excerpt}
            htmlAttr={{ className: `${styles.description} ${ExcerptColor}` }}
          />
        )}
        {/* Create a link with text and an icon */}
        <SimpleButtonWIcon
          {...updatedProps.cta}
          locale={props?.locale}
          isLightMode={updatedProps.isLightMode}
          htmlAttr={{ className: styles.cta }}
        />
      </div>
    </SimpleCardGeneric>
  )
}
