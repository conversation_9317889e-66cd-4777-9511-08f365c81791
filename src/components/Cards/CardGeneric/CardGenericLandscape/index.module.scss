//Default SCSS variables are prepended in next.config.js

.cardGenericLandScapeRoot {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-radius: 4px;
  overflow: hidden;

  .image {
    width: 50%;

    img {
      transition: transform 0.3s ease;
      object-fit: cover;
      border-radius: 4px 0 0 4px;

      @media screen and (max-width: $smScreenSize) {
        border-radius: 4px 4px 0 0;
      }

      @media screen and (max-width: $mScreenSize) {
        border-radius: 4px 4px 0 0;
      }
    }

    @media screen and (max-width: $smScreenSize) {
      width: 100%;
      height: 150px;
    }

    @media screen and (max-width: $mScreenSize) {
      width: 100%;
      height: 150px;
    }
  }

  .cardInfo {
    width: 50%;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    text-wrap: wrap;

    .date {
      font-size: $fs4;
      line-height: 24px;
      font-family: $fSansReg;
      margin-bottom: 30px;

      @media screen and (max-width: $mScreenSize) {
        font-size: $fs2;
        line-height: 22px;
        margin-bottom: 10px;
      }

      @media screen and (max-width: $smScreenSize) {
        font-size: $fs2;
        line-height: 22px;
      }
    }

    .hideOverflow {
      max-height: 90px;
      overflow: hidden;

      &::after {
        content: '';
        /* Required for pseudo-elements */
        position: absolute;
        /* Position it absolutely relative to the paragraph */
        bottom: 0;
        /* Align it with the top of the paragraph */
        right: 0;
        /* Align it with the right side of the paragraph */
        width: 150px;
        /* Adjust the width of the blurred area as needed */
        height: 30px;
        /* Make it cover the entire height of the line */
        background: linear-gradient(270deg, $cs2 21.79%, #ffffff00 94.87%);

        /* Use a linear gradient for the blur effect */
        @media screen and (max-width: $mScreenSize) {
          height: 28px;
        }
      }

      @media screen and (max-width: $smScreenSize) {
        max-height: 78px;
      }
    }

    .hideOverflowDark {
      max-height: 90px;
      overflow: hidden;

      &::after {
        content: '';
        /* Required for pseudo-elements */
        position: absolute;
        /* Position it absolutely relative to the paragraph */
        bottom: 0;
        /* Align it with the top of the paragraph */
        right: 0;
        /* Align it with the right side of the paragraph */
        width: 150px;
        /* Adjust the width of the blurred area as needed */
        height: 30px;
        /* Make it cover the entire height of the line */
        background: linear-gradient(270deg, $cp1 21.79%, #ffffff00 94.87%);

        /* Use a linear gradient for the blur effect */
        @media screen and (max-width: $mScreenSize) {
          height: 28px;
        }
      }

      @media screen and (max-width: $smScreenSize) {
        max-height: 78px;
      }
    }

    .margin {
      margin-bottom: 30px;
    }

    .heading {
      color: $cp1;
      position: relative;
      height: fit-content;
    }

    .darkHeading {
      color: $cs2;
      position: relative;
      height: fit-content;
    }

    .description {
      font-size: $fs5;
      line-height: 28px;
      margin-bottom: 40px;

      @media screen and (max-width: $smScreenSize) {
        display: none;
      }

      @media screen and (max-width: $mScreenSize) {
      }
    }

    @media screen and (max-width: $smScreenSize) {
      width: 100%;
      padding: 30px;
    }

    @media screen and (max-width: $mScreenSize) {
      width: 100%;
      padding: 30px;
    }
  }

  &:hover {
    img {
      transform: scale(1.1);
      /* Zoom in by 10% on hover */
    }

    .heading {
      color: $cp2;
    }

    .darkHeading {
      //color: $cs1;
    }

    &.darkMode {
      background: $cp2;

      .heading,
      .date,
      .description {
        color: $cs2;
      }

      .cta {
        color: $cs1;
      }

      .hideOverflow::after {
        background: linear-gradient(
          270deg,
          #0028d7 21.79%,
          rgba(0, 40, 215, 0.1) 94.87%
        );
      }
    }
  }

  @media screen and (max-width: 733px) {
    min-width: $minWidth;
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 4px 4px 0 0;
  }

  @media screen and (max-width: $mScreenSize) {
    min-width: $minWidth;
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
  }
}
