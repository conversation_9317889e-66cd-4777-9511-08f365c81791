import SimpleCTAs from '../../CTAs/SimpleCTAs'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import SimpleCard from '../SimpleCard'
import style from './index.module.scss'
import { CardFeaturedRowsListI } from './interface'

export default function CardFeaturedRowsList(props: CardFeaturedRowsListI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardFeaturedRowsListI = {
    // ...CardFeaturedRowsListD,
    ...props,
  }
  let numberColor = `${updatedProps.isLightMode ? 'cp2' : ' cs1'} ${
    updatedProps.htmlAttr?.className
  } ${style.number}`
  const themColor = `${updatedProps.isLightMode ? ' cs2' : ' cp1'} ${
    updatedProps.htmlAttr?.className
  } card`
  const dateClass = `${
    updatedProps.isLightMode ? style.date : style.darkDate
  }  ${updatedProps.date.htmlAttr?.className}`
  const headingClass = `${
    updatedProps.isLightMode ? style.excerpt : style.darkExcerpt
  }  ${updatedProps.heading.htmlAttr?.className}`

  // const date = getDateAndTime(updatedProps.date?.textContent!, null)
  console.log(updatedProps.heading, 'updatedProps.heading')
  return (
    <SimpleCard
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: `${style.CardFeaturedRowsListRoot} ${themColor}  `,
      }}
    >
      <SimpleHeading
        {...updatedProps.number}
        as='h1'
        htmlAttr={{ className: numberColor }}
      />
      <div className={style.description}>
        <SimpleParagraph
          {...updatedProps.date}
          htmlAttr={{ className: dateClass }}
          textContent={updatedProps?.date?.textContent}
        />
        {updatedProps.cta ? (
          <SimpleCTAs
            {...updatedProps.cta}
            htmlAttr={{
              className: style.cta,
              'aria-label': updatedProps?.heading?.textContent,
            }}
            locale={props?.locale}
          >
            {' '}
            <SimpleHeading
              {...updatedProps.heading}
              as='h6'
              htmlAttr={{
                className: headingClass,
              }}
            />
          </SimpleCTAs>
        ) : (
          <SimpleHeading
            {...updatedProps.heading}
            as='h6'
            htmlAttr={{ className: headingClass }}
          />
        )}
      </div>
    </SimpleCard>
  )
}
