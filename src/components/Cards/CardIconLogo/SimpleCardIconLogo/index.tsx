import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import SimpleCard from '../../SimpleCard'
import styles from './index.module.scss'
import { SimpleCardIconLogoI } from './interface'

export default function SimpleCardIconLogo(props: SimpleCardIconLogoI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: SimpleCardIconLogoI = {
    ...props,
    isInteractive: false,
  }

  const landscape =
    updatedProps?.alignment === 'Image Left'
      ? styles.landscapeCardLeft
      : styles.landscapeCardTop
  const portrait =
    updatedProps?.alignment === 'Image Left'
      ? styles.portraitCardLeft
      : styles.portraitCardTop
  const orientationClass =
    updatedProps?.orientation === 'landscape' ? landscape : portrait
  // Determine the flex direction based on the alignment prop
  const alignmentClass =
    updatedProps.alignment === 'Image Top'
      ? styles.imageTop
      : updatedProps.alignment === 'Image Left'
        ? styles.imageLeft
        : ''
  return (
    <SimpleCard
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: ` ${updatedProps?.htmlAttr?.className ? updatedProps.htmlAttr.className : ''} 
         ${updatedProps.cta && updatedProps?.cta?.textContent
            ? styles.simpleCardwithcta
            : styles.simpleCard
          } ${orientationClass}`,
      }}
      isInteractive={updatedProps.cta?.textContent ? true : false}
    >
      <div className={alignmentClass}>
        {updatedProps.children}
        <div className={styles.textContainer}>
          <ContextualInformation
            {...updatedProps.contextualInformation}
            showButtons={false}
            subHeading={undefined}
            heading={{
              ...updatedProps.contextualInformation?.heading,
              as: 'h4',
              htmlAttr: { className: styles.heading },
            }}
            excerpt={{
              ...updatedProps.contextualInformation?.excerpt,
              htmlAttr: { className: styles.excerpt },
            }}
            isLightMode={updatedProps?.isLightMode}
          />
          {updatedProps.cta && updatedProps.cta.textContent && (
            <div className={styles.ctas}>
              <SimpleButtonWIcon
                {...updatedProps.cta}
                locale={props.locale}
                isLightMode={updatedProps.isLightMode}
              />
            </div>
          )}
        </div>
      </div>
    </SimpleCard>
  )
}
