//Default SCSS variables are prepended in next.config.js

.simpleCard {
  width: 100%;
  display: flex;

  &:hover {
    cursor: context-menu !important;
  }

  @media (max-width: $smScreenSize) {
    .simpleCard {
      width: 100%;
    }
  }
}

.simpleCardwithcta {
  width: 100%;
  display: flex;

  @media (max-width: $smScreenSize) {
    .simpleCard {
      min-width: $minWidth;
      width: 100%;
    }
  }
}

.textContainer {
}

.heading {
  margin-bottom: 30px;
  margin-top: 0 !important;
}

.headingDark {
  margin-bottom: 30px;
  margin-top: 20px;
}

.excerpt {
  color: $cn2;
  line-height: $fs6;
  font-size: $fs4;
  margin: 0;
}

.imageTop {
  display: flex;
  flex-direction: column;

  .textContainer {
    margin-top: 32px;
  }
}

.imageLeft {
  display: flex;
  //flex-shrink: 0;
  flex-direction: row;
  gap: 20px;

  .textContainer {
    //margin-left: 27px;
  }
}

.ctas {
  margin-top: 37px;
  width: 100%;

  a {
    width: 100%;
    text-wrap: wrap;
    justify-content: start;
  }
}

.portraitCardTop {
  max-width: 350px;
}

.landscapeCardTop {
  max-width: 580px;
}

.portraitCardLeft {
  max-width: 450px;
}

.landscapeCardLeft {
  max-width: 690px;
}
