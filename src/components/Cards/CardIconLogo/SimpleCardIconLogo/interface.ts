import { ORIENTATION } from '../../../../globals/types'
import { SimpleCTAsI } from '../../../CTAs/SimpleCTAs/interface'
import { ContextualInformationI } from '../../../ContentBlocks/ContextualInformation/interface'
import { SimpleCardI } from '../../SimpleCard/interface'

export interface SimpleCardIconLogoI extends SimpleCardI {
  //...enlist other
  contextualInformation?: ContextualInformationI
  cta?: SimpleCTAsI
  alignment?: 'Image Top' | 'Image Left'
  orientation?: ORIENTATION
}
