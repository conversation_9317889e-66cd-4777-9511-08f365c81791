import { getDateAndTime } from '../../../../globals/utils'
import { ContextualInformationD } from '../../../ContentBlocks/ContextualInformation/defaults'
import { SimpleParagraphD } from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph/defaults'
import { ButtonGroupD } from '../../../CTAs/ButtonGroup/defaults'
import { LinkD } from '../../../CTAs/Link/defaults'
import { KernelD } from '../../../Kernel'
import { SimpleCardD } from '../../SimpleCard/defaults'
import { CardEventSimpleI } from './interface'

const dateAndTime = () => {
  const data = getDateAndTime(
    '2023-10-10T10:00:00.000+05:30',
    '2023-10-10T10:00:00.000+05:30'
  )
  return data
}
// Date format must be 2023-10-10T10:00:00.000+05:30.

export const CardEventsSimpleD: CardEventSimpleI = {
  ...SimpleCardD,
  _conf: {
    ...KernelD._conf,
    name: 'Card Event Simple Card',
    family: 'CARD',
    genus: 'CardEvent/SimpleCard',
    status: 'DEV',
  },
  contextualInfo: {
    ...ContextualInformationD,
    subHeading: {
      textContent: 'Live event',
      colour: 'cp2',
    },
    excerpt: {
      data: {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            attrs: {
              textAlign: 'left',
            },
            content: [
              {
                text: '',
                type: 'text',
              },
            ],
          },
        ],
      },
    },
    heading: {
      // as: 'h6',
      // displayAs: 'h3',
      textContent: 'Altus Connect 2023',
    },
    showButtons: false,
  },
  orientation: 'landscape',
  eventBanner: {
    src: 'https://images.ctfassets.net/8jgyidtgyr4v/6bh5flMMwSTq9F6cmzvC0R/da008f43bdbb02cb29d3b3d530c72c06/Podcast_hero.jpg',
    alt: 'banner',
    // objectFit: 'cover',
    // width: '333px',
    // height: '280px',
  },
  eventText: {
    ...SimpleParagraphD,
    data: {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          attrs: {
            textAlign: 'left',
          },
          content: [
            {
              type: 'text',
              text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat',
            },
          ],
        },
      ],
    },
  },
  liveEventDetail: {
    venue: {
      ...SimpleParagraphD,
      textContent: '1 Market Pl , San Diego, San Diego, United States',
    },
    date: {
      ...SimpleParagraphD,
      // textContent: dateAndTime().date, // using DateAndTime Util
    },
    time: {
      ...SimpleParagraphD,
      // textContent: dateAndTime().time, // using DateAndTime Util
    },
    mapLink: {
      ...LinkD,
      textContent: '(View map)',
    },
  },
  /*  onDemandDetails: {
       onDemand: {
         ...SimpleParagraphD,
         textContent: 'On-demand',
       },
     }, */
  EventTypeText: {
    ...SimpleParagraphD,
    textContent: 'On-demand',
  },
  cta: {
    ...ButtonGroupD,
    variant: 'iconSuffix',
    btnGroupType: 'page',
    button1: {
      href: 'https://www.altusgroup.com',
      textContent: 'Register now',
      type: 'SimpleButtonWIcon',
    },
    button2: {
      href: 'https://www.altusgroup.com',
      textContent: 'Tertiary',
      type: 'SimpleButtonWIcon',
    },
  },
}
