//Default SCSS variables are prepended in next.config.js

.container {
  width: 100%;
  max-width: $maxWidth;
  min-width: $minWidth;
  padding: 70px 0;
  border-bottom: 1px solid $cn4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 150px;
  cursor: auto !important;

  // to be discussed
  @media screen and (max-width: 1250px) {
    gap: 40px;
  }

  @media screen and (max-width: $smScreenSize) {
    gap: 20px;
    flex-wrap: wrap;
  }

  .left {
    width: 100%;

    @media screen and (max-width: $smScreenSize) {
      order: 2;
    }
  }

  .right {
    @media screen and (max-width: $smScreenSize) {
      width: 100%;
    }
  }
}

.eventBanner {
  width: 333px;
  height: 280px;
  border-radius: 4px;

  @media screen and (max-width: $smScreenSize) {
    width: 100% !important;
    height: 136px !important;
  }
}

.content {
  font-family: $fSansReg; // need to add '<PERSON>eonik' as font family but not able to find it
  line-height: 24px;
  color: $cp1;

  @media screen and (max-width: $smScreenSize) {
    font-size: $fs3;
    line-height: 22px;
  }
}

.flexColumn {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.upperPart {
  div {
    color: $cp2;
  }

  h3 {
    margin: 30px 0;
    line-height: 40px;
    color: $cn2;

    @media screen and (max-width: $smScreenSize) {
      font-size: $sH3FontSize;
      margin-top: 0;
      margin: 20px 0;
    }
  }
}

.lowerPart {
  p {
    font-size: $fs4;
    line-height: 30px;
  }
}

.subHeading {
  margin: 0;
  margin-bottom: 0 !important;
  color: $cp2;
}

.heading {
  //line-height: normal;
  margin: 30px 0 !important;
  color: $cn2;

  @media screen and (max-width: $smScreenSize) {
    margin: 20px 0 !important;
  }
}

.flex {
  display: flex;
  align-items: center;
  gap: 20px;
  column-gap: 48px;
  margin-bottom: 30px;
  flex-wrap: wrap;

  @media screen and (max-width: $smScreenSize) {
    flex-wrap: wrap;
    margin-bottom: 20px;
    column-gap: 40px;
  }
}

.iconText {
  display: inline-flex;
  align-items: center;
  font-size: $fs2;
  color: $cn2;
  line-height: 20px;
  font-style: normal;

  @media screen and (max-width: $smScreenSize) {
    font-size: $fs3;
    line-height: 22px;
  }

  .icon {
    height: fit-content;
    cursor: default;
  }

  .locationIcon {
    align-self: start;
  }

  svg {
    margin-right: 10px;
  }
}

.link {
  font-size: $fs2;
  text-decoration: none;
  white-space: nowrap;
  // margin-left: 8px;
  color: $cp2;
}

.darkMode {
  .iconText,
  .content {
    color: $cs2;
  }
}

.linkGroup {
  margin-top: 30px;
}

.btnOne {
  width: max-content;
}
