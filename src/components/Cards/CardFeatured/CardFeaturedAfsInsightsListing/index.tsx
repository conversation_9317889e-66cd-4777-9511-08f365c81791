import React, { useEffect } from 'react'
import SCSSVars from '../../../../brands/altus/styles/_nextSCSSVars.module.scss'
import { handleElementScrollIntoView } from '../../../../globals/utils'
import { getSortOptions } from '../../../../systems/AFS/AFSPages/Insights/afsInsights.mapping'
import { sortBy } from '../../../../systems/AFS/lib/sorting'
import FlexContainer from '../../../Containers/FlexContainer'
import Layout from '../../../Containers/Layout'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import Dropdown from '../../../Controls/Dropdown'
import Pagination from '../../../Navs/Pagination'
import styles from '../../CardFeatured/CardFeaturedAfsInsightsListing/index.module.scss'
import SimpleCardGroup from '../../CardGroupSimple'
import ColumnCardFeaturedLandscape from '../CardFeaturedLandscape'
import CardFeaturedPortrait from '../CardFeaturedPortrait'
import { CardFeaturedAfsInsightsListingD } from './default'
import { CardFeaturedAfsInsightsListingI } from './interface'

export default function CardFeaturedAfsInsightsListing(
  props: CardFeaturedAfsInsightsListingI
) {
  const updateProps: CardFeaturedAfsInsightsListingI = {
    ...CardFeaturedAfsInsightsListingD,
    ...props,
    Sort: getSortOptions(props?.locale),
  }

  const [sortValue, setSortValue] = React.useState<string>('newest')
  const [cards, setCards] = React.useState(updateProps.cards)
  const [currPage, setCurrPage] = React.useState(1)
  const [isFeaturedActive, setIsFeaturedActive] = React.useState()

  const onPageChange = (page: number) => {
    setCurrPage(page)
  }

  // update sort value
  const updateSortValue = (value) => {
    setSortValue(value)
    setIsFeaturedActive(false)
  }

  // handle scroll into view on page change
  useEffect(() => {
    handleElementScrollIntoView(`.${styles.mainContainer}`, 60, 305)
  }, [currPage])

  // handle featured
  useEffect(() => {
    setIsFeaturedActive(props.isFeaturedActive)
  }, [props.isFeaturedActive])

  // Effect to perform sorting when sort value changes
  useEffect(() => {
    const newCards = sortBy(props.cards, sortValue.toLowerCase())
    setCards(newCards)
  }, [sortValue])

  // sorting for every new props
  useEffect(() => {
    const cards = sortBy(updateProps.cards, sortValue.toLowerCase())
    setCards(cards)
  }, [updateProps.cards])

  let children = updateProps.children
  const cardComponents =
    cards &&
    cards.slice(currPage * 12 - 12, currPage * 12).map((card, index) => (
      <FlexContainer width={'auto'} flex={'1'} minWidth={SCSSVars.minW}>
        <CardFeaturedPortrait
          {...card}
          key={index}
          isLightMode={updateProps.isLightMode}
        />
      </FlexContainer>
    ))

  return (
    <SimpleCardGroup {...updateProps}>
      <div className={styles.mainContainer}>
        <div className={styles.insightsContainer}>
          <div className={styles.simpleFlex}>
            <div className={styles.dropDown}>
              <Dropdown
                {...updateProps.Sort}
                dropDownTab={{
                  ...updateProps.Sort?.dropDownTab,
                  fixedWidth: false,
                  htmlAttr: { className: styles.dropDownTab },
                }}
                isLightMode={updateProps.isLightMode}
                onChange={updateSortValue}
                selected={sortValue}
              />
            </div>
            <SimpleHeading
              {...updateProps.results}
              as='h6'
              displayAs='h6'
              isLightMode={updateProps.isLightMode}
            />
          </div>
          {isFeaturedActive && updateProps.cardFeaturedLarge && (
            <div className={styles.CardLarge}>
              <ColumnCardFeaturedLandscape
                {...updateProps.cardFeaturedLarge}
                isLightMode={updateProps.isLightMode}
              />
            </div>
          )}
          {/*  <div className={styles.flexContainer}>
            <FlexContainer
              {...updateProps.cardsContainer}
              children={cardComponents}
            />
          </div> */}
          <Layout
            htmlAttr={{ className: `${styles.layout} defaultLayout ` }}
            flexWrap={'wrap'}
          >
            {cardComponents}
            {cardComponents?.length === 3 && (
              <FlexContainer
                width={'auto'}
                htmlAttr={{ className: 'cardLayout' }}
                flex={'1'}
                minWidth={SCSSVars.minW}
              ></FlexContainer>
            )}
          </Layout>
        </div>
        {cards && cards.length > 12 && (
          <Pagination
            onPageChange={onPageChange}
            currentPage={currPage}
            totalCount={updateProps.cards.length}
            pageSize={12}
          />
        )}
      </div>
      {children}
    </SimpleCardGroup>
  )
}
