'use client'
import { useEffect, useRef, useState } from 'react'
import { RouteToUrl } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import Richtext from '../../../ContentBlocks/Richtext'
import { BackgroundImage } from '../../../Multimedia/Images'
import SimpleCard from '../../SimpleCard'
import styles from './index.module.scss'
import { CardFeaturedI } from './interface'

export default function CardFeatured(props: CardFeaturedI) {
  // const [dimensions, setDimensions] = useState({
  //     width: window.innerWidth,
  //     height: window.innerHeight

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CardFeaturedI = {
    // ...SimpleCardFeaturedD,
    ...props,
  }

  const [isClicked, setIsClicked] = useState<boolean>(false)
  const cardRef = useRef<HTMLDivElement>(null)
  const { size } = useWindowSize()

  const orientation =
    size === 'small' && updatedProps.orientation !== 'portrait-detailed'
      ? 'portrait'
      : updatedProps.orientation

  let cardStyle
  let overlayStyle

  if (orientation === 'portrait') {
    cardStyle = styles.cardPortrait
    overlayStyle = styles.overlayPortrait
  } else if (orientation === 'portrait-detailed') {
    cardStyle = styles.cardPortraitDetailed
    overlayStyle = styles.overlayPortraitDetailed
  } else {
    cardStyle = styles.cardLandscape
    overlayStyle = styles.overlayLandscape
  }

  const displayHeadingAs =
    orientation === 'portrait' ||
      orientation === 'portrait-detailed' ||
      size === 'small'
      ? 'h4'
      : 'h3'

  const headingStyle =
    orientation === ('portrait' || 'portrait-detailed')
      ? `cs2 ${styles.heading}`
      : `cs2 ${styles.headingLandscape}`

  const subheadingStyle = `cs2 ${styles.subheading}`

  //handles richText
  const renderRichText = (event) => {
    event.preventDefault()
    setIsClicked(true)
  }

  //handles card click
  const RouteHandler = () => {
    orientation === 'portrait-detailed'
      ? setIsClicked(true)
      : RouteToUrl(updatedProps.cta?.href, updatedProps.cta?.target)
  }

  useEffect(() => {
    const closeMenusOnOutsideClick = (event: MouseEvent) => {
      const target = event.target as Node

      // Check if the click is outside the card component
      if (cardRef.current && !cardRef.current.contains(target)) {
        setIsClicked(false)
      }
    }

    // Add event listener on component mount
    document.body.addEventListener('click', closeMenusOnOutsideClick)

    // Remove event listener on component unmount
    return () => {
      document.body.removeEventListener('click', closeMenusOnOutsideClick)
    }
  }, [isClicked])

  const cardContent = (
    <>
      <ContextualInformation
        {...updatedProps.contextualInformation}
        heading={{
          ...updatedProps.contextualInformation.heading,
          as: `${displayHeadingAs}`,
          htmlAttr: {
            ...updatedProps.contextualInformation.heading?.htmlAttr,
            className: headingStyle,
          },
        }}
        subHeading={{
          ...updatedProps.contextualInformation.subHeading,
          htmlAttr: {
            ...updatedProps.contextualInformation.subHeading?.htmlAttr,
            className: subheadingStyle,
          },
        }}
        showButtons={false}
      />

      <SimpleButtonWIcon
        {...updatedProps.cta}
        locale={props?.locale}
        isFormButton={orientation === 'portrait-detailed' ? true : false}
        href={`${orientation === 'portrait-detailed' ? 'javascript:void(0);' : updatedProps.cta?.href}`}
        htmlAttr={{
          className: styles.button,
          onClick: (event) =>
            orientation === 'portrait-detailed'
              ? renderRichText(event)
              : undefined,
        }}
      />
    </>
  )
  const portraitDetailed = (
    <div
      className={`${styles.richContent} ${isClicked && styles.richOverlayContainer}`}
    >
      <Richtext {...updatedProps.richText} />
    </div>
  )

  //     getCardFeaturedSEObj(updatedProps)

  return (
    <SimpleCard
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        className: `${updatedProps?.htmlAttr?.className} ${cardStyle} `,
        onClick: RouteHandler,
        ref: cardRef,
      }}
    >
      {updatedProps.children}
      {orientation === 'portrait-detailed' && portraitDetailed}
      <div className={`${styles.imageContainer}`}>
        {updatedProps?.image?.src && (
          <BackgroundImage
            {...updatedProps?.image}
            alt={updatedProps.image?.alt}
            src={updatedProps.image?.src}
            htmlAttr={{
              ...updatedProps.image?.htmlAttr,
              className: styles.image,
            }}
          />
        )}
        <div className={overlayStyle}>
          <div className={styles.cardContent}>{cardContent}</div>
        </div>
      </div>
    </SimpleCard>
  )
}
