//Default SCSS variables are prepended in next.config.js

.cardComparisonRoot {
  display: flex;
  flex-direction: column;
  max-width: 420px;
  width: 100%;
  min-width: $minWidth;
  min-height: 649px;
  padding: 50px;
  border-radius: 4px;
  gap: 30px;
  text-wrap: wrap;
  //background-color: $ca1;

  .checklist {
    display: flex;
    flex: 1;
    flex-direction: column;
    position: relative;
    width: 100%;
    border: none;
    background-color: transparent;
    overflow: auto;
    gap: 24px;

    .checklistItem {
      display: flex;
      flex-direction: row;
      gap: 24px;
      margin: 0;
      padding: 0;
      justify-content: flex-start;
      align-items: center;
    }
  }

  @media screen and (max-width: $smScreenSize) {
    //width: 300px;
    //width: $minWidth;
    padding: 30px;
    min-height: auto;
  }
}

.excerpt {
  //color: $cn3;
  line-height: $fs6;
  font-size: $fs4;
  margin: 0;
}
