'use client'
import { RouteToUrl } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import List from '../../../ContentBlocks/List'
import SimpleSpan from '../../../ContentBlocks/Texts/Spans/SimpleSpan'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import SysIcon from '../../../Multimedia/Icons/SysIcon'
import { SimpleImage } from '../../../Multimedia/Images'
import SimpleCardComparison from '../SimpleCardComparison'
import style from './index.module.scss'
import { CardComparisonIconI } from './interface'

export default function CardComparisonIcon(props: CardComparisonIconI) {
    /**
     * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
     * This way we don't need to keep providing the minimum defaults everytime a component is added
     * and plus we don't end up mutating the originally provided props.
     * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
     */
    const updatedProps: CardComparisonIconI = {
        // ...CardComparisonIconD,
        ...props
    }
    let iconColor = `${!updatedProps.isLightMode ? 'cs1' : 'cp2'}`
    let excerptColor = updatedProps.isLightMode ? 'cp1' : 'cs2'
    let bgColor = updatedProps.isLightMode ? style.bp6 : style.bp1
    const subHeading = updatedProps.isLightMode ? style.cp2 : style.cs1
    // Determine the height and width based on the size prop
    let logoHeight = '40px'
    let logoWidth = '40px'

    if (updatedProps.size === 'small') {
        logoHeight = '40px'
        logoWidth = '40px'
    } else if (updatedProps.size === 'medium') {
        logoHeight = '60px'
        logoWidth = '60px'
    } else if (updatedProps.size === 'large') {
        logoHeight = '80px'
        logoWidth = '80px'
    }

    const { size } = useWindowSize()

    // const isPortrait = updatedProps.orientation === 'portrait' ? true : false

    const Icon = updatedProps.image?.src ? (
        <SimpleImage
            {...updatedProps.image}
            htmlAttr={{ className: style.image }}
            height={logoHeight}
            width={logoWidth}
        />
    ) : null
    /**
     * Checklist is a component that renders a list of checklist items.
     */
    // checklist logic jsx here
    const checklist = (
        // list of checklist items
        <List htmlAttr={{ className: style.checklist }}>
            {/*mapping through the checklist items*/}
            {updatedProps.span?.map((span, index) => (
                // each checklist item
                <div className={style.checklistItem} key={index}>
                    {/*checklist item icon*/}
                    <div>
                        <SysIcon
                            icon={'FaCircleCheck'}
                            size={'sm'}
                            iconColour={iconColor}
                            isAsIs={true}
                        />
                    </div>
                    {/*checklist item text*/}
                    <SimpleSpan
                        {...span}
                        colour={`${updatedProps.isLightMode ? 'cn2' : 'cs2'}`}
                        fontSize="fs4"
                        textContent={span.textContent}
                    />
                </div>
            ))}
        </List>
    )
    const handleRoute = () => {
        RouteToUrl(updatedProps.button?.href)
    }

    return (
        updatedProps.orientation === 'portrait' ?
            <SimpleCardComparison
                {...updatedProps}
                htmlAttr={{
                    onClick: handleRoute,
                    className: `${style.CardComparisonIconRoot} ${bgColor} shadow shadow3 `,
                    style: props.htmlAttr?.style
                }}
            >

                {Icon}
                <ContextualInformation
                    {...updatedProps.contextualInformation}
                    showButtons={false}
                    heading={{
                        ...updatedProps.contextualInformation?.heading,
                        as: 'h3',
                        htmlAttr: {
                            style: {
                                margin: '20px 0 20px 0'
                            }
                        }
                    }}
                    subHeading={{
                        ...updatedProps.contextualInformation?.subHeading,
                        fontSize: 'fs4',
                        htmlAttr: { className: subHeading }
                    }}
                    excerpt={{
                        ...updatedProps.contextualInformation?.excerpt,
                        htmlAttr: { className: `${style.excerpt} ${excerptColor}` }
                    }}
                    htmlAttr={{}}
                    isLightMode={updatedProps.isLightMode}
                />
                {checklist}
                <div className={style.buttonContainer}>
                    <SimpleButtonWIcon
                        {...updatedProps.button}
                        locale={props?.locale}
                        isLightMode={updatedProps.isLightMode}
                    />
                </div>
            </SimpleCardComparison> :
            <SimpleCardComparison
                {...updatedProps}
                htmlAttr={{
                    onClick: handleRoute,
                    className: `${style.CardComparisonLdRoot} ${bgColor} shadow shadow3 `,
                    style: props.htmlAttr?.style
                }}
            >

                {Icon}

                <div className={style.CardComparisonIcon}>
                    <div className={style.cardComparisonContext}>
                        <ContextualInformation
                            {...updatedProps.contextualInformation}
                            showButtons={false}
                            heading={{
                                ...updatedProps.contextualInformation?.heading,
                                as: 'h3',
                                htmlAttr: {
                                    style: {
                                        margin: '20px 0 20px 0'
                                    }
                                }
                            }}
                            subHeading={{
                                ...updatedProps.contextualInformation?.subHeading,
                                fontSize: 'fs4',
                                htmlAttr: { className: subHeading }
                            }}
                            excerpt={{
                                ...updatedProps.contextualInformation?.excerpt,
                                htmlAttr: { className: `${style.excerpt} ${excerptColor}` }
                            }}
                            htmlAttr={{}}
                            isLightMode={updatedProps.isLightMode}
                        />
                        <div className={style.smChecklist}>
                            {checklist}
                        </div>
                        <div className={style.buttonContainer}>
                            <SimpleButtonWIcon
                                {...updatedProps.button}
                                locale={props?.locale}
                                isLightMode={updatedProps.isLightMode}
                            />
                        </div>
                    </div>
                    <div className={style.dtChecklist}>
                        {checklist}
                    </div>
                </div>
            </SimpleCardComparison>
    )
}