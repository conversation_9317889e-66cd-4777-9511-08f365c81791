//Default SCSS variables are prepended in next.config.js

.CardComparisonIconRoot {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: $minWidth;
  min-height: 649px;
  height: 100%;
  padding: 50px;
  border-radius: 4px;
  gap: 30px;
  text-wrap: wrap;

  .CardComparisonIcon {
    display: flex;
    }

  .image {
    display: flex;
    flex-shrink: 0;
    align-self: end;
    position: absolute !important;
    }

  h6 {
    margin-bottom: $s4;
    }

  .checklist {
    display: flex;
    flex: 1;
    flex-direction: column;
    position: relative;
    width: 100%;
    border: none;
    background-color: transparent;
    overflow: auto;
    gap: 24px;

    .checklistItem {
      display: flex;
      flex-direction: row;
      gap: 24px;
      margin: 0;
      padding: 0;
      justify-content: flex-start;
      align-items: center;
      }
    }

  @media screen and (max-width: $smScreenSize) {
    //width: 300px;
    //width: $minWidth;
    padding: 30px;
    min-height: auto;
    }
  }


// Card comparison landscape style

.CardComparisonLdRoot {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: $minWidth;
  height: 100%;
  padding: 50px;
  border-radius: 4px;
  gap: 30px;
  text-wrap: wrap;

  .CardComparisonIcon {
    display: flex;
    justify-content: space-between;
    gap: 30px;

    @media screen and (max-width: $smScreenSize) {
      flex-direction: column;
      }

    .cardComparisonContext {
      width: 45%;
      display: flex;
      flex-direction: column;
      gap: 30px;

      @media screen and (max-width: $smScreenSize) {
        width: 100%;
        }
      }
    }

  .image {
    display: flex;
    flex-shrink: 0;
    align-self: end;
    position: absolute !important;
    }

  h6 {
    margin-bottom: $s4;
    }

  .buttonContainer {
    margin-top: $s3;

    @media screen and (max-width: $smScreenSize) {
      margin-top: 0;
      }
    }

  .smChecklist {
    display: none;

    @media screen and (max-width: $smScreenSize) {
      display: flex !important;
      }
    }

  .dtChecklist {
    display: flex;
    width: 45%;

    @media screen and (max-width: $smScreenSize) {
      display: none !important;
      width: 100%;
      }
    }

  .checklist {
    display: flex;
    flex: 1;
    flex-direction: column;
    position: relative;
    width: 100%;
    margin-top: 90px;
    border: none;
    background-color: transparent;
    overflow: auto;
    gap: 24px;

    @media screen and (max-width: $smScreenSize) {
      margin-top: 0;
      }

    .checklistItem {
      display: flex;
      flex-direction: row;
      gap: 24px;
      margin: 0;
      padding: 0;
      justify-content: flex-start;
      align-items: center;
      }
    }

  @media screen and (max-width: $smScreenSize) {
    //width: 300px;
    //width: $minWidth;
    padding: 30px;
    min-height: auto;
    }
  }

.excerpt {
  //color: $cn3;
  line-height: $fs6;
  font-size: $fs4;
  margin: 0;
  }

.bp1 {
  background-color: $cp1 !important;
  }

.bp6 {
  background-color: $cp6 !important;
  }

.cp2 {
  color: $cp2 !important;
  }

.cs1 {
  color: $cs1 !important;
  }