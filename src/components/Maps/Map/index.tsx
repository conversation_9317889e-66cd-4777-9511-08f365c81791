'use client'

import {
  GoogleMap,
  InfoWindowF,
  MarkerF,
  useLoadScript,
} from '@react-google-maps/api'
import React, { useEffect, useMemo, useState } from 'react'
import { RichtextI } from '../../ContentBlocks/Richtext/interface'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import Kernel from '../../Kernel'
import MapTooltipCard from '../MapTooltipCard'
import { MapD, mapStyle } from './defaults'
import styles from './index.module.scss'
import { MapI } from './interface'

export default function Map(props: MapI): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: MapI = {
    ...MapD,
    ...props,
    htmlAttr: {
      className: props.htmlAttr?.className,
      stye: props.htmlAttr?.style,
    },
  }
  updatedProps.locationName = props.locationName || `${props.city}, ${props.country}`

  const [locations, setLocations] = useState<MapI['locations']>(
    updatedProps.locations
  )
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [isMapTooltip, setIsMapTooltip] = useState<boolean>(props.isTooltipOpen)
  const [tooltipData, setTooltipData] = useState<any>(null)
  const [center, setCenter] = useState<any>(updatedProps?.mapCenter)
  const [zoom, setZoom] = useState<number>(updatedProps?.zoom)

  useEffect(() => {
    // Update locations when props.locations change

    if (map && updatedProps.allCitiesAndCountries?.length > 0 && !Boolean(props.country) && !Boolean((props.city))) {
      setLocations(updatedProps.allCitiesAndCountries)
      const bounds = new google.maps.LatLngBounds()
      updatedProps.allCitiesAndCountries?.forEach(({ position }) => {
        bounds.extend(new google.maps.LatLng(position.lat, position.lng))
      })
      map.fitBounds(bounds)
      return
    }

    if (props.country && props.city && map) {
      const cityWiseMarker = updatedProps?.allCitiesAndCountries?.filter((item) => item.country === props.country && item.city === props.city) || []
      setLocations(cityWiseMarker)
      const bounds = new google.maps.LatLngBounds()
      cityWiseMarker.forEach(({ position }) => {
        bounds.extend(new google.maps.LatLng(position.lat, position.lng))
      })
      map.fitBounds(bounds)
      map.setZoom(10)
      return
    }

    if (props.country && map) {
      const countryWiseMarker = updatedProps?.allCitiesAndCountries?.filter((item) => item.country === props.country) || []
      setLocations(countryWiseMarker)
      const bounds = new google.maps.LatLngBounds()
      countryWiseMarker.forEach(({ position }) => {
        bounds.extend(new google.maps.LatLng(position.lat, position.lng))
      })
      map.fitBounds(bounds)
      return
    }

    if (updatedProps.allCitiesAndCountries?.length > 0) {
      setLocations(updatedProps.allCitiesAndCountries)
      return
    }

    if (updatedProps.locations?.length > 0) {
      setLocations(updatedProps.locations)
      // return
    }

    /*      // Re-fit the map bounds when the map is available
        if(map && updatedProps.locations && updatedProps.locations.length > 1) {
            const bounds = new google.maps.LatLngBounds()
            updatedProps.locations?.forEach(({position}) => {
                bounds.extend(new google.maps.LatLng(position.lat, position.lng))
            })
            map.fitBounds(bounds)
        } */
    if (map && updatedProps.locations && updatedProps.locations.length === 1) {
      // If there is only one location, set the center and zoom
      const { position } = updatedProps.locations[0]
      map.setCenter(new google.maps.LatLng(position.lat, position.lng))
      map.setZoom(10) // Adjust the zoom level as needed
    } else if (
      map &&
      updatedProps.locations &&
      updatedProps.locations.length > 1
    ) {
      // If there are multiple locations, fit the bounds
      const bounds = new google.maps.LatLngBounds()
      updatedProps.locations.forEach(({ position }) => {
        bounds.extend(new google.maps.LatLng(position.lat, position.lng))
      })
      map.fitBounds(bounds)
    }
  }, [updatedProps.locations, map, props.country, props.city, updatedProps.allCitiesAndCountries])

  // Callback function to set the map instance
  const handleMapLoad = (map: google.maps.Map) => {
    setMap(map)
  }


  useEffect(() => {
    setIsMapTooltip(props.isTooltipOpen)
  }, [props.isTooltipOpen, props.toolTipData])


  // State to track the active marker
  const [activeMarker, setActiveMarker] = useState<number | null>(
    updatedProps?.activeLocationIndex
  )

  // List of libraries to load from Google Maps API
  const libraries = useMemo(() => ['places'], [])
  // Memoized map center
  const mapCenter = useMemo(() => updatedProps.mapCenter, [])
  // Memoized map options
  const mapOptions = useMemo<google.maps.MapOptions>(
    () => ({
      //This property disables any UI control buttons from the Maps
      disableDefaultUI: true,
      mapTypeControlOptions: {
        mapTypeIds: ['roadmap', 'satellite', 'hybrid', 'terrain', 'styled_map'],
      },
      styles: mapStyle,
    }),

    []
  )

  // Load Google Maps API
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY as string,
    libraries: libraries as any,
  })

  // If the API is not loaded yet, show a loading message
  if (!isLoaded) {
    return <p>Loading...</p>
  }

  // Handler for marker click
  const handleMarkerClick = (markerId: number) => {
    setActiveMarker(markerId)
  }
  const handleCardOpen = (richContent: RichtextI) => {
    if (map) {
      console.log('rr', richContent)
      setTooltipData(richContent)
      setIsMapTooltip(true)
    }
  }
  const handleTooltipClose = () => {
    setTooltipData(null)
    setIsMapTooltip(false)
  }
  // Handler for closing the info window
  const handleInfoWindowClose = () => {
    setActiveMarker(null)
  }
  const iconSize = new google.maps.Size(30, 30)
  const anchorPoint = new google.maps.Point(
    iconSize.width / 2,
    iconSize.height / 2
  )
  console.log("locations", locations)
  //Returning the Map enclosed withint Kernel
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: updatedProps?.isAFSMap
          ? `${styles.afsMap} bleedf`
          : styles.rootContainer,
      }}
    >
      <GoogleMap
        options={mapOptions}
        zoom={zoom}
        center={center}
        mapTypeId={google.maps.MapTypeId.ROADMAP}
        mapContainerStyle={{ width: '100%', height: '100%' }}
        onLoad={handleMapLoad}
      >
        {locations?.map(
          ({ position, city, country, address, phone, email, link, richContent }, i) => {
            return (
              <MarkerF
                key={i}
                // icon={{
                //   url: locationPin, // url
                //   scaledSize: iconSize, // scaled size
                //   origin: new google.maps.Point(0, 0), // origin
                //   anchor: anchorPoint, // anchor
                // }}
                /*  icon={{
                           url: '/@Core/geo-alt-fill.svg',
                           scaledSize: new google.maps.Size(30, 30),
                         }}  */
                position={position}
                // title={city}
                onClick={() => {
                  if (updatedProps?.isAFSMap) {
                    // handleCardOpen(richContent)
                    props.setSelectedCountry(country)
                    props.setSelectedCity(city)
                  } else {
                    handleMarkerClick(i)
                  }
                }}
              >
                {activeMarker === i &&
                  (updatedProps?.isAFSMap ? (
                    <></>
                  ) : (
                    <InfoWindowF
                      key={i}
                      position={position}
                      onCloseClick={handleInfoWindowClose}
                      options={{
                        maxWidth: 250, // Set a maximum width
                        pixelOffset: new google.maps.Size(0, 0), // Adjust the vertical position
                      }}
                    >
                      {/*    infowindow custom html and css */}
                      <div className={styles.mapContent} id='mapContent'>
                        <SimpleHeading
                          as='h4'
                          htmlAttr={{ className: styles.firstHeading }}
                          textContent={city}
                        />
                        <div id='bodyContent' className={styles.bodyContent}>
                          <p className={styles.address}>
                            <span>Address:</span>
                            {address}
                          </p>
                          {phone && (
                            <p className={styles.phone}>
                              <span>Phone:</span>
                              {phone}
                            </p>
                          )}
                          <p
                            className={styles.link}
                            onClick={() =>
                              updatedProps.onViewMoreClick(city, i)
                            }
                          >
                            View more info
                          </p>
                        </div>
                      </div>
                    </InfoWindowF>
                  ))}
              </MarkerF>
            )
          }
        )}
      </GoogleMap>
      {isMapTooltip && (
        <MapTooltipCard
          richContent={props.toolTipData || tooltipData}
          handleClose={handleTooltipClose}
        />
      )}
    </Kernel>
  )
}
