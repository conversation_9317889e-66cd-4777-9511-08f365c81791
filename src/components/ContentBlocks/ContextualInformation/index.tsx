import CTAGroup from '../../CTAs/CTAGroup'
import SimpleCTAs from '../../CTAs/SimpleCTAs'
import Kernel from '../../Kernel'
import Richtext from '../Richtext'
import SimpleHeading from '../Texts/Headings/SimpleHeading'
import { ContextualInformationD } from './defaults'
import styles from './index.module.scss'
import { ContextualInformationI } from './interface'

export default function ContextualInformation(props: ContextualInformationI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: ContextualInformationI = {
    ...ContextualInformationD,
    ...props,
  }

  let subheading = <></>
  let heading = <></>
  let richtext = <></>
  let className = `${updatedProps.htmlAttr?.className ?? ''}`

  if (!updatedProps.isLightMode) {
    className += ` ${styles.hasDarkBg}`
  }

  if (updatedProps.subHeading?.textContent) {
    subheading = (
      <SimpleHeading
        {...updatedProps.subHeading}
        as={'h6'}
        htmlAttr={{
          ...updatedProps.subHeading?.htmlAttr,
          className: ` ${styles.text} ${updatedProps.subHeading ? styles.margin : ''
            } subheading ${updatedProps.subHeading.htmlAttr?.className}`,
        }}
      />
    )
  }
  // Add this block for richtext
  if (
    updatedProps.excerpt?.data &&
    updatedProps.excerpt?.data?.content?.length > 0
    // && updatedProps.excerpt?.data?.content?.at(0)?.content?.at(0)?.text
  ) {
    richtext = (
      <Richtext
        {...updatedProps.excerpt}
        htmlAttr={{
          className: `${styles.excerpt} ${styles.text} ${updatedProps.excerpt?.htmlAttr?.className}`,
        }}
      />
    )
  }
  if (updatedProps?.heading?.textContent) {
    heading = (
      <SimpleHeading
        {...updatedProps.heading}
        as={updatedProps?.heading?.as ?? 'h1'}
        htmlAttr={{
          ...updatedProps.heading?.htmlAttr,
          className: `${styles.heading} ${styles.text} ${updatedProps.heading?.htmlAttr?.className}`,
        }}
      />
    )
  } else {
    heading = <></>
  }

  if (updatedProps.cta) {
    heading = <SimpleCTAs {...updatedProps.cta}
      htmlAttr={{
        'aria-label': updatedProps.heading?.textContent
      }}
    >
      {heading}
    </SimpleCTAs>
  }

  return (
    <Kernel
      {...updatedProps}
      as={'div'}
      htmlAttr={{
        className: className,
        style: updatedProps.htmlAttr?.style,
      }}
    >
      {subheading}

      {heading}

      {richtext}
      {updatedProps.showButtons === true ? (
        <CTAGroup
          {...updatedProps?.buttons}
          isLightMode={updatedProps.isLightMode}
          htmlAttr={{ className: styles.buttons }}
        />
      ) : undefined}

    </Kernel>
  )
}
