import { isBrowser } from '../../../../globals/utils'
import styles from '../index.module.scss'

function RichtextSimpleLink(props: any) {
  const slug = props?.node?.attrs?.slug
  let href = '#' // default href

  if (slug && !slug?.includes('#')) {
    href = slug
  } else {
    href = props?.node?.attrs?.href || '#'
  }

  if (isBrowser() && !props?.sectionId && href !== '#') {
    const urlParams = new URLSearchParams(window.location.search)
    const lang = urlParams.get('lang')
    if (lang) {
      href = href + '?lang=' + lang
    }
  }

  return (
    <a
      href={href}
      className={`${styles[props?.node?.attrs?.class] ?? ''} templateLink`}
      target={props?.node?.attrs?.target ?? '_blank'}
      rel='noreferrer'
    >
      {props.children}
    </a>
  )
}

export default RichtextSimpleLink
