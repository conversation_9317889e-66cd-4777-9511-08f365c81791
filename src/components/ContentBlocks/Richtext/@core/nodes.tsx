import {
  TipTapMarkHandlers,
  TipTapNodeHandlers,
} from '@wokaylabs/tiptap-react-render'
import Link from 'next/link'
import { HEADINGS } from '../../../../globals/types'
import { mapDataToComponent } from '../../../../lib/propsMapping/index.mapping'
import SimpleHeading from '../../Texts/Headings/SimpleHeading'
import SimpleText from '../../Texts/SimpleText'
import styles from '../index.module.scss'
import RichtextSimpleLink from './RichtextSimpleLink'

export const CalculateReactComponent = (props) => {
  const componentProps = props.node.attrs.sys[props.node.attrs.sys.typeName]
  const Comp = mapDataToComponent(componentProps)
  const CompMain = {
    ...Comp,
    props: {
      ...Comp?.props,
      htmlAttr: {
        ...Comp?.props?.htmlAttr,
        className:
          props.node.type === 'reactBlockComponent'
            ? 'block'
            : props?.node?.attrs?.sys?.typeName === 'componentLogo'
              ? 'inlineBlock'
              : 'inline',
      },
    },
  }

  return props.node.type === 'reactBlockComponent' ? (
    <div className='py20'> {CompMain}</div>
  ) : (
    <>{CompMain}</>
  )
}

const levelToHeading = {
  '1': 'h1',
  '2': 'h2',
  '3': 'h3',
  '4': 'h4',
  '5': 'h5',
  '6': 'h6',
}

const StyledText = (props) => {
  const htmlStyles: any = {}
  let classes = ''

  if (props && props?.node && props?.node?.marks) {
    props.node.marks.forEach((item) => {
      switch (item.type) {
        case 'textStyle':
          htmlStyles['color'] = item?.attrs?.color
          break
        case 'highlight':
          htmlStyles['background'] = item?.attrs?.color
          break
        case 'altusText':
          classes = classes + ' ' + item?.attrs?.class
          break
        case 'italic':
          htmlStyles['fontStyle'] = 'italic'
          break
        case 'underline':
          htmlStyles['textDecoration'] = 'underline'
          break
        // case 'bold':
        //   htmlStyles['fontWeight'] = 'bold'
        // break
        default:
          break
      }
    })
  }
  const ariaProps = {}
  if (Boolean(props?.node?.attrs?.['aria-hidden'])) {
    ariaProps['aria-hidden'] = true
  }

  return (
    <span {...ariaProps} style={htmlStyles} className={classes}>
      {props?.node?.text}
    </span>
  )
}

const StyledHeadingText = (props) => {
  if (props && props?.node && props?.node?.content) {
    const CompHeadings = props.node.content.map((headingItem) => {
      const htmlStyles: any = {}
      let classes = ''
      let link = null
      headingItem?.marks?.forEach((item) => {
        switch (item.type) {
          case 'textStyle':
            htmlStyles['color'] = item?.attrs?.color
            break
          case 'highlight':
            htmlStyles['background'] = item?.attrs?.color
            break
          case 'altusText':
            classes = classes + ' ' + item?.attrs?.class
            break
          case 'link':
            link = item?.attrs
            classes = classes + ' ' + styles[item?.attrs?.class]
            break
          case 'italic':
            htmlStyles['fontStyle'] = 'italic'
            break
          case 'underline':
            htmlStyles['textDecoration'] = 'underline'
            break
          case 'bold':
            htmlStyles['fontWeight'] = 'bold'
          default:
            break
        }
      })
      return link ? (
        <Link href={link?.href ?? '/'} target={link?.target} rel={link?.rel}>
          <span className={classes} style={htmlStyles}>
            {headingItem?.text}
          </span>
        </Link>
      ) : (
        <span className={classes} style={htmlStyles}>
          {headingItem?.text}
        </span>
      )
    })
    return (
      <SimpleHeading
        textContent={CompHeadings}
        textAlign={props?.node?.attrs?.textAlign}
        as={
          levelToHeading.hasOwnProperty(props.node.attrs.level)
            ? (levelToHeading[props.node.attrs.level] as HEADINGS)
            : 'h6'
        }
      />
    )
  } else {
    return <></>
  }

  // if (props && props?.node && props?.node?.content) {
  //     props.node.content?.at(0)?.marks?.forEach((item) => {
  //         switch (item.type) {
  //             case 'textStyle':
  //                 htmlStyles['color'] = item?.attrs?.color
  //                 break
  //             case 'highlight':
  //                 htmlStyles['background'] = item?.attrs?.color
  //                 break
  //             case 'altusText':
  //                 classes = classes + ' ' + item?.attrs?.class
  //                 break
  //             case 'link':
  //                 link = item?.attrs
  //                 classes = classes + ' ' + styles[item?.attrs?.class]
  //                 break
  //             case 'italic':
  //                 htmlStyles['fontStyle'] = 'italic'
  //                 break
  //             case 'underline':
  //                 htmlStyles['textDecoration'] = 'underline'
  //                 break
  //             case 'bold':
  //                 htmlStyles['fontWeight'] = 'bold'
  //             default:
  //                 break
  //         }
  //     })
  // }
}

export const markHandlers: TipTapMarkHandlers = {
  link: RichtextSimpleLink,
  bold: (props) => <strong>{props.children}</strong>,
  italic: (props) => <i>{props.children}</i>,
  underline: (props) => <u>{props.children}</u>,
  strike: (props) => <s>{props.children}</s>,
  code: (props) => <code>{props.children}</code>,
}

export const nodeHandlers: TipTapNodeHandlers = {
  doc: (props) => <>{props.children}</>,
  text: (props) => <StyledText {...props} />,
  paragraph: (props) => {
    const ariaProps = {}
    if (Boolean(props?.node?.attrs?.['aria-hidden'])) {
      ariaProps['aria-hidden'] = true
    }
    return (
      <SimpleText
        htmlAttr={{
          ...ariaProps,
          // role: 'paragraph',
          // 'aria-label': 'Descriptive content',
        }}
        textAlign={props?.node?.attrs?.textAlign}
        as={'p'}
      >
        {props.children}
      </SimpleText>
    )
  },
  // heading: (props) => (
  //     <>
  //         <SimpleHeading
  //             {...SimpleHeadingD}
  //             as={levelToHeading.hasOwnProperty(props.node.attrs.level) ? levelToHeading[props.node.attrs.level] as HEADINGS : 'h6'}
  //             textContent={props?.node?.content?.at(0)?.text}
  //             textAlign={props?.node?.attrs?.textAlign}
  //         />
  //     </>
  // ),
  heading: (props) => <StyledHeadingText {...props} />,
  hardBreak: () => <br />,
  bulletList: (props) => <ul>{props.children}</ul>,
  orderedList: (props) => <ol>{props.children}</ol>,
  listItem: (props) => <li>{props.children}</li>,
  codeBlock: (props) => (
    <pre>
      <code>{props.children}</code>
    </pre>
  ),
  horizontalRule: () => <hr />,
  blockquote: (props) => <blockquote>{props.children}</blockquote>,
  // Table
  table: (props) => <table>{props.children}</table>,
  tableRow: (props) => <tr>{props.children}</tr>,
  tableHeader: (props) => <th>{props.children}</th>,
  tableCell: (props) => <td>{props.children}</td>,
  reactBlockComponent: (props) => <CalculateReactComponent {...props} />,
  reactInlineComponent: (props) => <CalculateReactComponent {...props} />,
  altusDiv: (props) => (
    <div className={props?.node?.attrs?.class ?? ''}>{props.children}</div>
  ),
  // textStyle: (props) => <StyledText {...props} />,
  image: (props) => {
    return <img src={props?.node?.attrs?.src} alt={props?.node?.attrs?.alt} />
  },
}
