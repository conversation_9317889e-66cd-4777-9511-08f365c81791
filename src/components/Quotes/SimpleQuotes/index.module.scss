//Default SCSS variables are prepended in next.config.js

.quotesContainer {
  min-width: $minWidth;
  max-width: 762px;
  width: 100%;
  border-left: 2px solid $cp2;
  padding-left: 28px;

  .paraMain {
    /*  font-size: $fs6;
     font-family: $fSansRegItalic;
     font-weight: 500;
     line-height: 30px; */
    color: $cn2;
  }

  .authorMain {
    margin-top: 16px;
    background: transparent;

    .avatar {
      display: none;
    }
  }

  .avatarGrid {
    grid-template-columns: 1fr;
    gap: 0;

    & > div {
      margin-left: 0;
    }
  }

  .avatarDisplay {
    display: none;
  }
}
