import CardPeopleSmall from '../../../components/Cards/CardPeople/CardPeopleSmall'
import Kernel from '../../../components/Kernel'
import Richtext from '../../ContentBlocks/Richtext'

import Style from './index.module.scss'
import { SimpleQuotesI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function SimpleQuotes(props: SimpleQuotesI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: SimpleQuotesI = {
    // ...SimpleQuotesD,
    isPresent: true,
    isVisible: true,
    // isEnabled:true,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: `${Style.quotesContainer}`,
    },
  }

  return (
    <Kernel {...updatedProps} isFullXBleed>
      <Richtext
        {...updatedProps.para}
        htmlAttr={{ className: `${Style.paraMain} quote` }}
      />

      {updatedProps.isAuthor && (
        <CardPeopleSmall
          {...updatedProps.author}
          htmlAttr={{
            ...updatedProps?.author?.htmlAttr,
            className: `${Style.authorMain} ${
              !updatedProps.isAvatar ? Style.avatarGrid : ''
            } ${updatedProps.author?.htmlAttr?.className}`,
          }}
          person={{
            ...updatedProps?.author?.person,
            avatar: {
              ...updatedProps?.author?.person.avatar,

              htmlAttr: {
                ...updatedProps.author?.person.avatar.htmlAttr,
                className: `${!updatedProps.isAvatar ? Style.avatar : ''} ${
                  updatedProps.author?.person.avatar.htmlAttr?.className
                }`,
              },
            },
          }}
        />
      )}
    </Kernel>
  )
}
