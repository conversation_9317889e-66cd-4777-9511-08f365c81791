import { BGCOLOURS, BGGRADIENTS } from '../../../globals/types'
import { SimpleBarI } from '../SimpleBar/interface'

/**
 * SimpleBar interface enlists all the props (and their types) that SimpleBar can have.
 */
export interface ProgressBarI extends SimpleBarI {
  //enlist components specific props over here...
  progressWidth?: string
  progressColour?: BGCOLOURS | BGGRADIENTS
  transition?: boolean
  duration?: number
}
