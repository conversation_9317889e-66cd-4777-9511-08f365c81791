import { color } from 'echarts';
import React, { useEffect } from 'react';

interface CustomSidebarProps {
    isOpen: boolean;
    onCancel: () => void;
    children: React.ReactNode;
    width?: string; // Optional width prop
}

const CustomSidebar: React.FC<CustomSidebarProps> = ({ isOpen, onCancel, children, width = '300px' }) => {
    // Lock the body scroll when the sidebar is open
    // useEffect(() => {
    //     if (isOpen) {
    //         document.body.style.overflow = 'hidden';
    //     } else {
    //         document.body.style.overflow = 'auto';
    //     }
    //     return () => {
    //         document.body.style.overflow = 'auto'; // Reset scroll when the sidebar is closed
    //     };
    // }, [isOpen]);

    if (!isOpen) return null; // Do not render the sidebar if it's closed

    return (
        <div style={styles.overlay}>
            <div style={{ ...styles.sidebar, width }}>
                <div style={styles.sidebarHeader}>
                    <button onClick={onCancel} style={styles.closeButton}>X</button>
                </div>
                <div style={styles.sidebarContent}>
                    {children}
                </div>
            </div>
        </div>
    );
};

// Custom styles
const styles = {
    overlay: {
        position: 'fixed' as 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
        display: 'flex',
        justifyContent: 'flex-start', // Align sidebar to the left
        zIndex: 1000,
    },
    sidebar: {
        backgroundColor: '#fff',
        height: '100%',
        maxHeight: '100vh',
        overflowY: 'auto' as 'auto',
        position: 'relative' as 'relative',
        boxShadow: '2px 0 5px rgba(0,0,0,0.5)',
    },
    sidebarHeader: {
        display: 'flex',
        justifyContent: 'flex-end',
        padding: '10px',
    },
    sidebarContent: {
        padding: '20px',
    },
    closeButton: {
        background: 'none',
        border: 'none',
        fontSize: '18px',
        cursor: 'pointer',
        color: '#333',
    },
};

export default CustomSidebar;