'use client'
import React, { useState } from 'react'
import { UseSearchBoxProps, useSearchBox } from 'react-instantsearch'
import {
  setRePopulateReducer,
  setSearchState,
} from '../../../redux/slices/searchSlice'
import { useAppDispatch } from '../../../redux/store'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import SimpleControl from '../SimpleControl'
import { SearchInputD } from './defaults'
import styles from './index.module.scss'
import { SearchInputI } from './interface'

const queryHook = (query, search) => {
  search(query)
}

export default function SearchInput(props: SearchInputI & UseSearchBoxProps) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: SearchInputI = {
    ...SearchInputD,
    ...props,
  }

  const rootClass = `${
    updatedProps.isLightMode ? styles.lightRoot : styles.darkRoot
  }`
  const colorClass = `${updatedProps.isLightMode ? 'cn3' : 'cs2'}`

  const { query, refine } = useSearchBox({
    queryHook,
  })
  const [input, setInput] = useState<string>(query)
  const dispatch = useAppDispatch()

  let currentDomain = window.location.hostname

  if (currentDomain.startsWith('www.')) {
    currentDomain = currentDomain.slice(4)
  }

  // Function to handle input value change
  const inputChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
    setTimeout(() => {
      refine(e.target.value)
      if (e.target.value.length > 0) {
        dispatch(setRePopulateReducer(true))
      } else {
        dispatch(setRePopulateReducer(false))
      }
    }, 1000)
    updatedProps.htmlAttr?.onChange!(e.target.value)
  }

  // Function to clear the input text
  const clearInputHandler = () => {
    dispatch(setSearchState(false))
    setInput('')
    updatedProps.closeIcon.htmlAttr?.onClick()
  }

  return (
    <SimpleControl
      {...updatedProps}
      htmlAttr={{ ...updatedProps.htmlAttr, className: rootClass }}
    >
      {/* Display an Search icon */}
      <GenericIcon
        icon='Search'
        size={'sm'}
        htmlAttr={{ className: styles.SearchIcon }}
      />

      <input
        type='text'
        className={`${styles.formInput} ${colorClass}`}
        placeholder={`Search ${currentDomain}`}
        value={input}
        onChange={inputChangeHandler}
        required
        autoFocus
      />
      {input && (
        <GenericIcon
          {...updatedProps.closeIcon}
          icon='Close'
          size={'md'}
          htmlAttr={{
            className: styles.deleteIconContainer,
            onClick: clearInputHandler,
          }}
        />
      )}
    </SimpleControl>
  )
}
