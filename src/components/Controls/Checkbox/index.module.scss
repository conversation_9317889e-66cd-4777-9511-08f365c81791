//Default SCSS variables are prepended in next.config.js

.checkboxRoot {
  display: flex;
  min-width: 190px;
  width: 100%;
  align-items: center;
  cursor: pointer;
  gap: 8px;

  .icon {
    color: $cn4;
  }

  .inValidIcon {
    @extend .icon;
    color: $ca12;
  }

  &:hover {
    background-color: $cn7;

    .label {
      color: $cp2;
      // font-weight: 700;
    }
  }

  &.checked {
    .icon {
      color: $cp2;
    }
  }

  @media screen and (max-width: 733px) {
    gap: 4px;
  }
}
