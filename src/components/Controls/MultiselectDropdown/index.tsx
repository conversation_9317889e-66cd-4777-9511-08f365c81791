'use client'
import React from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { setFilters } from '../../../redux/slices/filterSlice/filtersSlice'
import List from '../../ContentBlocks/List'
import styles from '../../ContentBlocks/List/index.module.scss'
import DropdownTab from '../../Tabs/DropdownTab'
import Checkbox from '../Checkbox'
import SimpleControl from '../SimpleControl'
import { MultiselectDropdownD } from './defaults'
import { MultiselectDropdownI } from './interface'

export default function Dropdown(
  props: MultiselectDropdownI
): React.ReactElement {
  const updatedProps: MultiselectDropdownI = {
    ...MultiselectDropdownD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
    },
  }
  const filters = useSelector((state: any) => state?.filters)
  const dispatch = useDispatch()
  const filterType = props?.filterType
  const internalFilterField = props?.internalFilterField
  const checkedItems =
    filterType && internalFilterField && filters[filterType]
      ? filters[filterType][internalFilterField]
      : []

  // Function to handle checkbox change
  const handleCheckboxChange = (option: any, isChecked: any) => {
    dispatch(
      setFilters({
        filterType: props.filterType,
        field: props.internalFilterField,
        value: option.id,
      })
    )
  }

  const checkboxGroup = updatedProps?.option?.map((option, i) => (
    <li className={styles.option} key={i}>
      <Checkbox
        {...option}
        isInvalid={updatedProps.isInvalid}
        label={option.label}
        isChecked={checkedItems?.includes(option.label.id as string)}
        onChange={(isChecked: any) => {
          handleCheckboxChange(option.label, isChecked)
        }}
      />
    </li>
  ))

  return (
    <SimpleControl {...updatedProps} isAsIs>
      <DropdownTab
        {...updatedProps.dropDownTab}
        isInvalid={updatedProps.isInvalid}
      >
        <List
          isInvalid={updatedProps.isInvalid}
          htmlAttr={{
            style: { display: 'none' },
            onClick: (e) => {
              e.stopPropagation()
            },
          }}
        >
          {checkboxGroup}
        </List>
      </DropdownTab>
    </SimpleControl>
  )
}
