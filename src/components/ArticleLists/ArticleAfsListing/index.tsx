'use client'

import React, { useEffect } from 'react'
import { handleElementScrollIntoView } from '../../../globals/utils'
import { sortBy } from '../../../systems/AFS/lib/sorting'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import Dropdown from '../../Controls/Dropdown'
import Pagination from '../../Navs/Pagination'
import ArticleRelease from '../ArticleRelease'
import styles from './index.module.scss'

export default function ArticleReleaseAfsInsightsListing(props) {
  const updateProps = {
    ...props,
  }

  const [sortValue, setSortValue] = React.useState<string>('newest')
  const [cards, setCards] = React.useState(updateProps?.cards)

  const [currPage, setCurrPage] = React.useState(1)

  const [isPageChanged, setIsPageChanged] = React.useState(false)

  const onPageChange = (page: number) => {
    setCurrPage(page)
    setIsPageChanged(true)
  }

  // update sort value
  const updateSortValue = (value) => {
    setSortValue(value)
  }

  // handle scroll into view on page change
  useEffect(() => {
    if (!isPageChanged) return
    handleElementScrollIntoView(`.${styles.EventContainer}`, 60, 305)
  }, [currPage])

  // Effect to perform sorting when sort value changes
  useEffect(() => {
    const newCards = sortBy(props?.cards, sortValue.toLowerCase())
    setCards(newCards)
  }, [sortValue])

  // sorting for every new props
  useEffect(() => {
    const cards = sortBy(updateProps?.cards, sortValue.toLowerCase())
    setCards(cards)
  }, [updateProps.cards])

  const cardComponents =
    cards &&
    cards
      .slice(currPage * 12 - 12, currPage * 12)
      ?.map((card, index) => (
        <ArticleRelease key={index} {...card} locale={updateProps.locale} />
      ))
  return (
    <div className={styles.EventContainer}>
      <div className={styles.simpleFlex}>
        <SimpleHeading
          {...updateProps.results}
          as='h6'
          displayAs='h6'
          isLightMode={updateProps.isLightMode}
        />
        <div className={styles.dropDown}>
          <Dropdown
            {...updateProps.Sort}
            dropDownTab={{
              ...updateProps.Sort?.dropDownTab,
              fixedWidth: false,
              htmlAttr: { className: styles.dropDownTab },
            }}
            isLightMode={updateProps.isLightMode}
            onChange={updateSortValue}
            selected={sortValue}
          />
        </div>
      </div>
      <div className={styles.cardContent}>{cardComponents}</div>
      {cards && cards.length > 12 && (
        <Pagination
          onPageChange={onPageChange}
          currentPage={currPage}
          totalCount={updateProps.cards.length}
          pageSize={12}
        />
      )}
    </div>
  )
}
