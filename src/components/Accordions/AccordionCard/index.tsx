'use client'
import { useCallback, useEffect, useState } from 'react'
import PlayerRouter from '../../../lib/componentsRouter/PlayerRouter'
import { SimpleImage } from '../../Multimedia/Images'
import SimpleAccordion from '../SimpleAccordion'
import AccordionItems from './@Core/AccordionItems'
import { AccordionCardD } from './defaults'
import styles from './index.module.scss'
import { AccordionCardI } from './interface'

export default function AccordionCard(props: AccordionCardI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: AccordionCardI = {
    ...AccordionCardD,
    ...props,
    contextualInfo: {
      ...props.contextualInfo,
      htmlAttr: { className: styles.contexualInfo },
      heading: {
        ...props.contextualInfo?.heading,
        as: 'h2',
        htmlAttr: {
          ...props.contextualInfo?.heading?.htmlAttr,
          className: `${props.contextualInfo?.heading?.htmlAttr?.className} ${styles.containerHeading} `,
        },
        textAlign: props.headingAlignment,
      },
      subHeading: {
        ...props.contextualInfo?.subHeading,
        htmlAttr: {
          ...props.contextualInfo?.subHeading?.htmlAttr,
          className: `${props.contextualInfo?.subHeading?.htmlAttr?.className} subheading `,
        },
        colour: 'cp2',
      },
    },
  }
  const [currentIndex, setCurrentIndex] = useState<number | null>(0)
  useEffect(() => {}, [currentIndex, updatedProps])

  const updateCurrentIndex = useCallback(
    (index: number) => {
      setCurrentIndex(index)
    },
    [currentIndex]
  )

  return (
    <SimpleAccordion {...updatedProps}>
      <div className={styles.AccordionRoot}>
        <div className={styles.AccordionItems}>
          <AccordionItems
            accordionsMain={updatedProps.accordionsMain}
            currentIndex={currentIndex}
            setCurrentIndex={updateCurrentIndex}
            isCloseable={false}
            isAutoClose={true}
          />
        </div>
        {updatedProps?.accordionsMain[currentIndex ? currentIndex : 0]
          ?.accordionContent?.media.src ? (
          <div className={styles.media}>
            {/* with the help of current Index you can get current media  */}

            <SimpleImage
              {...updatedProps?.accordionsMain[currentIndex ? currentIndex : 0]
                ?.accordionContent?.media}
              htmlAttr={{ className: styles.img }}
            />
          </div>
        ) : (
          <div className={styles.media}>
            {/* with the help of current Index you can get current media  */}
            <div className={styles.videoplayer}>
              <PlayerRouter
                {...updatedProps?.accordionsMain[
                  currentIndex ? currentIndex : 0
                ]?.accordionContent?.video}
              />
            </div>
          </div>
        )}
      </div>
    </SimpleAccordion>
  )
}
