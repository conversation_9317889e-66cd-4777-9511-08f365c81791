'use client'
import { l } from '../../../../../globals/utils'
import PlayerRouter from '../../../../../lib/componentsRouter/PlayerRouter'
import Richtext from '../../../../ContentBlocks/Richtext'
import SimpleButtonWIcon from '../../../../CTAs/SimpleButtonWIcon'
import Kernel from '../../../../Kernel'
import { SimpleImage } from '../../../../Multimedia/Images'
import styles from './index.module.scss'
import { AccordionContentI } from './interface'

export default function AccordionContent(props: AccordionContentI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: AccordionContentI = {
    //...AccordionContentD,
    ...props,
  }
  l('mmmmm', updatedProps.cta)
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{ className: styles.AccordionContentRoot }}
    >
      <Richtext
        {...updatedProps.text}
        htmlAttr={{ className: styles.paragraph }}
      />
      {/* <SimpleParagraph {...updatedProps.text} /> */}
      {updatedProps.cta?.textContent && (
        <SimpleButtonWIcon {...updatedProps.cta} />
      )}
      {updatedProps?.media?.src ? (
        <div className={styles.media}>
          <SimpleImage
            {...updatedProps.media}
            htmlAttr={{ className: styles.img }}
            width='100%'
            height='100%'
          />
        </div>
      ) : (
        <div className={styles.media}>
          <PlayerRouter
            {...updatedProps.video}
            isLightMode={updatedProps.isLightMode}
          />
        </div>
      )}
    </Kernel>
  )
}
