//Default SCSS variables are prepended in next.config.js

.AccordionContentRoot {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 50px;
  margin-bottom: 32px;
  justify-content: center;

  .paragraph {
    color: $cn2;
    font-size: $h6FontSize;
    line-height: 24px;
    /* 150% */
  }

  .media {
    width: 100%;
    height: 150px;
    border-radius: 4px;
    background: $cn7;
    display: none;
    align-items: center;

    .img {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      object-fit: cover !important;
    }

    @media screen and (max-width: $smScreenSize) {
      display: flex;
      height: 350px;
      background: none;
    }
  }
}
