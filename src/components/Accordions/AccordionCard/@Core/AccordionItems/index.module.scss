//Default SCSS variables are prepended in next.config.js

.AccordionRoot {
  width: 100%;

  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 45px;
  margin-top: 70px;

  .media {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background: $cn7;
    display: flex;
    align-items: center;

    @media screen and (max-width: $smScreenSize) {
      display: none;
    }
  }

  .AccordionItems {
    width: 100%;
  }

  @media screen and (max-width: $smScreenSize) {
    min-width: $minWidth;
    width: 100%;
    grid-template-columns: 1fr;
    margin-top: 30px;
  }
}

.accordionContainer {
  border-bottom: 1px solid $cn5;
  cursor: pointer;

  .accordionHeader {
    padding: 20px 0;
    gap: 10px;
    // use this for style accordion tab header
  }

  .accordionHeader {
    .heading {
      color: $cp1;

      //font-size: $h3FontSize;
      @media screen and (max-width: $mScreenSize) {
        // font-size: $sH3FontSize;
        // line-height: 32px;
      }

      @media screen and (max-width: $smScreenSize) {
        //font-size: $sH5FontSize;
        // line-height: 40px;
        /* 125% */
      }
    }

    &:hover {
      .heading {
        color: $cp2;
      }

      color: $cp2;
    }
  }

  .inActiveHeader {
    color: $cn3 !important;
  }

  .iconHide {
    display: none;
  }
}

.rightImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;

  img {
    width: 100%;
    object-fit: contain;
    border-radius: 4px;
  }
}
