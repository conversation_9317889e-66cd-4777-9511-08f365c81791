'use client'
import { useEffect } from 'react'
import { TheSEOBlock } from '../../../../../globals/utils'
import Kernel from '../../../../Kernel'
import SimpleAccordionItem from '../../../SimpleAccordion/@Core/SImpleAccordionItem'
import { getSimpleAccordionSEObj } from '../../../SimpleAccordion/seo'
import AccordionContent from '../AccordionCardContent'
import { AccordionItemsD } from './defaults'
import styles from './index.module.scss'
import { AccordionItemsI } from './interface'

/**
 *This component is only for use of AccordionDynamic
 */

export default function AccordionItems(props: AccordionItemsI) {
  const updatedProps: AccordionItemsI = {
    ...AccordionItemsD,
    ...props,
  }

  useEffect(() => {
    // just to update values
  }, [updatedProps])
  return (
    <Kernel {...updatedProps} isFullXBleed>
      {updatedProps.accordionsMain?.map((accordion, i) => {
        return (
          <SimpleAccordionItem
            key={i}
            htmlAttr={{
              className: styles.accordionContainer,
            }}
            acrTab={{
              ...accordion.accordionItem?.acrTab,
              htmlAttr: {
                ...accordion.accordionItem?.acrTab?.htmlAttr,
                className: styles.accordionHeader,
              },
              heading: {
                ...accordion.accordionItem?.acrTab?.heading,
                as: 'h3',
                displayAs: 'h4',
                htmlAttr: {
                  ...accordion.accordionItem?.acrTab?.heading?.htmlAttr,
                  className: styles.heading,
                },
              },
            }}
            acrTabC={{
              ...accordion.accordionItem?.acrTabC,
              htmlAttr: {
                ...accordion.accordionItem?.acrTabC?.htmlAttr,
                className: styles.accordionTabContent,
              },
            }}
            isOpen={updatedProps.currentIndex === i}
            index={i}
            isAutoClose={updatedProps.isAutoClose}
            isCloseable={updatedProps.isCloseable}
            currentIndex={updatedProps.currentIndex}
            setCurrentIndex={updatedProps.setCurrentIndex}
          >
            <AccordionContent {...accordion.accordionContent} />
          </SimpleAccordionItem>
        )
      })}
      <TheSEOBlock seoObj={getSimpleAccordionSEObj(updatedProps)} />
    </Kernel>
  )
}
