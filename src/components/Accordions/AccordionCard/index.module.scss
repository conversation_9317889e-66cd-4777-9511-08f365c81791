//Default SCSS variables are prepended in next.config.js

.AccordionRoot {
  width: 100%;

  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 45px;
  margin-top: 60px;

  // .video{
  //   min-height: 107px;
  //   height: 100%;
  //   width: 33%;
  //   border-radius: 4px;
  //   border: 10px solid transparent;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  // }
  .media {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background: $cn7;
    display: flex;
    align-items: center;

    .img {
      object-fit: cover;
      width: 100%;
      height: 362px;
      border-radius: 4px;
    }

    .videoplayer {
      object-fit: cover;
      width: 100%;
      max-height: 500px;
      border-radius: 4px;
    }

    @media screen and (max-width: $smScreenSize) {
      display: none;
    }
  }

  .AccordionItems {
    width: 100%;
  }

  @media screen and (max-width: $smScreenSize) {
    width: 100%;
    grid-template-columns: 1fr;
    margin-top: 30px;
    /*  .containerSubHeading {
      font-size: $sH5FontSize;
      font-style: normal;
        font-family: $fSansBld;;
      line-height: 24px; /* 133.333%
      } */
  }
}

.contexualInfo {
  width: 75%;

  @media (max-width: $smScreenSize) {
    width: 100%;
  }
}
