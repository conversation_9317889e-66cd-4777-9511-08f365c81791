'use client'
import ContextualInformation from '../../ContentBlocks/ContextualInformation'
import Kernel from '../../Kernel'
import { SimpleAccordionD } from './defaults'
import style from './index.module.scss'
import { SimpleAccordionI } from './interface'

export default function SimpleAccordion(props: SimpleAccordionI) {
  let className = `${props.isLightMode ? style.light : style.dark} ${
    props.htmlAttr?.className ?? ''
  } bold`
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: SimpleAccordionI = {
    ...SimpleAccordionD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: className,
      style: props.htmlAttr?.style,
    },
  }

  return (
    <Kernel {...updatedProps}>
      <ContextualInformation {...updatedProps?.contextualInfo} />
      {updatedProps.children}
    </Kernel>
  )
}
