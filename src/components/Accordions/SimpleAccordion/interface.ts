import { ContextualInformationI } from '../../ContentBlocks/ContextualInformation/interface'
import { KernelI } from '../../Kernel'

/**
 * SimpleAvatar interface enlists all the props (and their types) that SimpleAvatar can have.
 */
export interface SimpleAccordionI extends KernelI {
  contextualInfo?: ContextualInformationI
  headingAlignment?: string
  //enlist components specific props over here...
}
