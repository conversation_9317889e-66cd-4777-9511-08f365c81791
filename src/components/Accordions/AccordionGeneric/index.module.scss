//Default SCSS variables are prepended in next.config.js

.accordionGenericContextInfo {
  max-width: 719px;
}

.AccordionRoot {
  max-width: 719px;
  margin-top: 60px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
  // margin-top: 20px;

  .AccordionItems {
    width: 100%;
  }

  @media screen and (max-width: $smScreenSize) {
    min-width: $minWidth;
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    margin-top: 30px;
  }
}

.accordionContainer {
  // use this for style accordion main container
  border-bottom: 1px solid;
  border-color: $cn5;
  min-width: $minWidth;
  width: 100%;

  .accordionTab {
    min-width: $minWidth;
    width: 100%;
    min-height: 100px;
    height: 100%;
    display: flex;
    gap: 30px;
    align-items: center;
    padding: 0;

    &:hover {
      .accordionTabHeading {
        color: $cp2;
      }

      color: $cp2;
    }
  }

  .contextualHeadingStyle {
    color: $cp1;
  }

  @media screen and (max-width: $smScreenSize) {
    min-width: $minWidth;
    width: 100%;

    .accordionTabHeading {
      //font-size: $fs4;
      //font-family: $fSansBld;
      //line-height: 24px;
    }
  }
}
