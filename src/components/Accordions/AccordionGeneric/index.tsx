'use client'

import { useEffect, useState } from 'react'
import { TheSEOBlock } from '../../../globals/utils'
import SimpleAccordion from '../SimpleAccordion'
import SimpleAccordionItem from '../SimpleAccordion/@Core/SImpleAccordionItem'
import { getSimpleAccordionSEObj } from '../SimpleAccordion/seo'
import AccordionContent from './@Core'
import styles from './index.module.scss'
import { AccordionGenericI } from './interface'

interface AccordionItemStateI {
  [key: number]: boolean
}

export default function AccordionGeneric(props: AccordionGenericI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: AccordionGenericI = {
    // ...AccordionGenericD,
    ...props,
    isAutoClose: true,
  }

  const [currentIndex, setCurrentIndex] = useState<number>(0)
  const [isOpen, setIsOpen] = useState<AccordionItemStateI>({})

  useEffect(() => {
    if (updatedProps?.isOpen === true) {
      setIsOpen({
        0: true,
      })
    }
  }, [])

  return (
    <SimpleAccordion
      {...updatedProps}
      contextualInfo={{
        ...updatedProps.contextualInfo,
        heading: {
          ...updatedProps.contextualInfo?.heading,
          as: 'h2',
          htmlAttr: {
            className: styles.contextualHeadingStyle,
          },
          textAlign: updatedProps.headingAlignment,
        },
        subHeading: {
          ...updatedProps.contextualInfo?.subHeading,
          colour: 'cp2',
        },
        htmlAttr: { className: styles.accordionGenericContextInfo },
      }}
    >
      <div className={`${styles.AccordionRoot} xxxx`}>
        <div className={styles.AccordionItems}>
          {updatedProps.accordionsMain?.map((accordion, idx) => {
            return (
              <SimpleAccordionItem
                key={idx}
                htmlAttr={{
                  ...accordion?.accordionItem?.htmlAttr,
                  className: styles.accordionContainer,
                }}
                acrTab={{
                  ...accordion.accordionItem?.acrTab,
                  htmlAttr: {
                    ...accordion.accordionItem?.acrTab?.htmlAttr,
                    className: styles.accordionTab,
                  },
                  heading: {
                    ...accordion.accordionItem?.acrTab?.heading,
                    as: 'h3',
                    displayAs: 'h4',
                    htmlAttr: { className: styles.accordionTabHeading },
                  },
                }}
                acrTabC={{
                  ...accordion.accordionItem?.acrTabC,
                  htmlAttr: {
                    ...accordion.accordionItem?.acrTabC?.htmlAttr,
                    className: styles.accordionTabContent,
                  },
                }}
                index={idx}
                isOpen={isOpen[idx] || false}
                isAutoClose={updatedProps?.isAutoClose}
                isCloseable={true}
                currentIndex={currentIndex}
                setCurrentIndex={() => {
                  setCurrentIndex(idx)
                  setIsOpen({
                    ...isOpen,
                    [idx]: !isOpen[idx],
                  })
                }}
              >
                {accordion?.accordionContent !== undefined ? (
                  <AccordionContent {...accordion?.accordionContent} />
                ) : undefined}
              </SimpleAccordionItem>
            )
          })}
          <TheSEOBlock seoObj={getSimpleAccordionSEObj(updatedProps)} />
        </div>
      </div>
    </SimpleAccordion>
  )
}
