import Kernel from '../../../components/Kernel'
import LinkRouter, {
  LinkRouterI,
} from '../../../lib/componentsRouter/LinkRouter'
import { CTAGroupD } from './defaults'
import style from './index.module.scss'
import { CTAGroupI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function CTAGroup(props: CTAGroupI) {
  const updatedProps: CTAGroupI = {
    ...CTAGroupD,
    ...props,
  }

  return (
    <Kernel {...updatedProps} isAsIs>
      <div
        className={`${style.group} ${updatedProps.htmlAttr?.className ?? ''}`}
      >
        {updatedProps?.links?.map((e: any) => (
          <LinkRouter
            {...(e as LinkRouterI)}
            isLightMode={updatedProps.isLightMode ?? e.isLightMode}
          />
        ))}

        {<div style={{ border: "2px solid green", padding: 10 }}>
          +Add
        </div>}
      </div>
    </Kern<PERSON>>
  )
}
