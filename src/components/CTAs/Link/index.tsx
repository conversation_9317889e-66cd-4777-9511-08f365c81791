import SimpleCTAs from '../SimpleCTAs'
import { LinkI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function Link(props: LinkI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: LinkI = {
    // ...LinkD,
    ...props,
  }

  let children = updatedProps.children

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Family components, return Family Simpletons only.
   */
  return (
    <SimpleCTAs {...updatedProps} isButton={false}>
      {updatedProps.children}
      {updatedProps.textContent}
    </SimpleCTAs>
  )
}
