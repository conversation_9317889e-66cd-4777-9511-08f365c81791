//importing the default styles from the global styles
//Default SCSS variables are prepended in next.config.js

.linkRootLight {
  //color of the link
  color: $cp2;
  padding-inline: 2px;
  text-wrap: nowrap;
  //on hover
  &:hover {
    //color of the link on hover
    color: $cs2;
    background-color: $cp2;
    text-decoration: none;
    transition:
      color 0.25s,
      background-color 0.25s;
  }
}

.linkRootDark {
  text-wrap: nowrap;
  color: $cs1;
  padding-inline: 2px;
  //on hover
  &:hover {
    //color of the link on hover
    color: $cn2;
    background-color: $cs6;
    text-decoration: none;
    transition:
      color 0.25s,
      background-color 0.25s;
  }
}
