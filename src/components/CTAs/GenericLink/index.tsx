import Link from 'next/link'
import Kernel from '../../Kernel'
import {GenericLinkD} from './defaults'
import style from './index.module.scss'
import {GenericLinkI} from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function GenericLink(props: GenericLinkI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: GenericLinkI = {
    ...GenericLinkD,
    ...props,
  }

  let children = updatedProps.children
  let linkColor = `${!updatedProps.isLightMode ? style.linkRootDark : style.linkRootLight} ${updatedProps.htmlAttr?.className}`

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Family components, return Family Simpletons only.
   */
  return (
    <Kernel {...updatedProps} as={'span'}>
      <Link className={linkColor} href={updatedProps.href}>
        {updatedProps.textContent}{' '}
      </Link>
    </Kernel>
  )
}