'use client'
import { useState } from 'react'
import GenericIcon from '../../../components/Multimedia/Icons/SysIcon'
import SimpleCTAs from '../SimpleCTAs'
import { SimpleIconButtonD } from './defaults'
import style from './index.module.scss'
import { SimpleIconButtonI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function SimpleIconButton(props: SimpleIconButtonI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: SimpleIconButtonI = {
    ...SimpleIconButtonD,
    ...props,
  }

  // State to track hover state
  const [isHovered, setIsHovered] = useState(false)

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  let iconColor // Declare a variable to store the icon color

  // Check the 'variant' prop to determine the icon color
  if (updatedProps.variant === 'primary') {
    // If the variant is 'primary' and the background is dark, set the icon color to 'cn2'
    if (!updatedProps.isLightMode) {
      iconColor = 'cn2'
    }
  } else if (updatedProps.variant === 'secondary') {
    // If the variant is 'secondary', set the default icon color to 'cp1'
    iconColor = 'cp1'
    // If the component is being hovered over, change the icon color to 'cs2'
    if (isHovered) {
      iconColor = 'cs2'
    }
    // If the background is dark, set the icon color to 'cn2'
    if (!updatedProps.isLightMode) {
      iconColor = 'cn2'
    }
  } else if (updatedProps.variant === 'tertiary') {
    // If the variant is 'tertiary', set the default icon color to 'cp2'
    iconColor = 'cp2'
    // If the component is being hovered over, change the icon color to 'cp3'
    if (isHovered) {
      iconColor = 'cp3'
    }
    // If the background is dark, set the icon color to 'cs1'
    if (!updatedProps.isLightMode) {
      iconColor = 'cs1'

      // If the component is being hovered over, change the icon color to 'cn8'
      if (isHovered) {
        iconColor = 'cn8'
      }
    }
  } else if (updatedProps.variant === 'tertiary2') {
    // If the variant is 'tertiary2', set the default icon color to 'cp1'
    iconColor = 'cp1'
    // If the component is being hovered over, change the icon color to 'cp2'
    if (isHovered) {
      iconColor = 'cp2'
    }
    // If the background is dark, set the icon color to 'cn8'
    if (!updatedProps.isLightMode) {
      iconColor = 'cn8'
      // If the component is being hovered over, change the icon color to 'cs1'
      if (isHovered) {
        iconColor = 'cs1'
      }
    }
  }

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Family components, return Family Simpletons only.
   */
  return (
    <SimpleCTAs
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        className: `${style.iconButton} ${updatedProps.htmlAttr?.className}`,
        /*   style: { borderRadius: '50%', height: '48px', width: '48px' } */
      }}
    >
      {/*using the icon prop to render the icon  */}
      <GenericIcon
        as={'span'}
        icon={updatedProps.icon}
        size={updatedProps.size}
        iconColour={`${iconColor} ${updatedProps.iconColour}`}
      />
    </SimpleCTAs>
  )
}
