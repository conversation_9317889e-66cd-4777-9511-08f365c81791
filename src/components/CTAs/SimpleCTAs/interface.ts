import { ICONS } from '../../../globals/types'
import { KernelI } from '../../Kernel'
import { IconI } from '../../Multimedia/Icons/SysIcon/interface'

export interface SimpleCTAsI extends KernelI, IconI {
  href?: string
  icon?: ICONS
  isIconPrefixed?: boolean
  type?: 'SimpleButtonWIcon' | 'SimpleButton' | 'SimpleIconButton'
  /**
   * if isChevron is true then it shows the icon else shows cheveronToArrow
   */
  isChevron2Arrow?: boolean
  isButton?: boolean
  isFormButton?: boolean
  textContent?: string
  isLightMode?: boolean
  variant?: 'primary' | 'secondary' | 'tertiary' | 'tertiary2' | 'tertiary3'
  target?: '_blank' | '_self' | '_parent' | '_top'
  actionContent?: unknown
  disableLinkColor?: boolean
  /**
   *@todo ...enlist props that are unique to SimpleCTAs
   */
}
