'use client'
import { useStatsigClient } from '@statsig/react-bindings'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import {
  BUISNESS_DOMAINS,
  cl,
  generateAriaLabelFromSlug,
  getQueryParam<PERSON>son,
  isBrows<PERSON>,
} from '../../../globals/utils'
import { setPopupData, setShow } from '../../../redux/slices/popupSlice'
import { useAppDispatch } from '../../../redux/store'
import { SimpleCTAsD } from './defaults'
import style from './index.module.scss'
import { SimpleCTAsI } from './interface'
import { getPreviewProps } from '../../../utils/previewUtils'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function SimpleCTAs(props: SimpleCTAsI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const dispatch = useAppDispatch()
  // console.log(props?.locale)
  const [updatedUrl, setUpdatedUrl] = useState<string>('')
  let UpdaterdUrlforLink = props.href ?? ''
  const { client } = useStatsigClient()

  useEffect(() => {
    if (props.href) {
      setUpdatedUrl(props.href)
    }
  }, [props])

  const checkIsLinkValid = (href: string) => {
    if (!href) return false
    if (href.charAt(0) === '#') return false
    if (href.startsWith('/#')) return false
    return true
  }

  const updatedProps: SimpleCTAsI = {
    ...SimpleCTAsD,
    ...props,
    htmlAttr: {
      ...props?.htmlAttr,
      onMouseEnter: () => {
        if (props?.htmlAttr?.onMouseEnter) {
          props.htmlAttr.onMouseEnter()
        }
        cl(
          'Event Identifier after Merge ::: \n\n\n\n Hover \n\n\n\n :::',
          props,
          client
        )
        if (props?.isExperimentation && props?.experimentEvents === 'Hover') {
          // cl(
          //   'first Statsig from Kernal Hover : Filter : FIrestKernal : Identifier : CTA : Hover',
          //   props, client
          // )
          client.logEvent(
            `${props?.textContent}`,
            `next14_hover_${props?.experimentEvents}`,
            {
              href: props?.href,
              variant: props?.variant,

              // item_name: 'diet_coke_48_pack',
              // ...data,
            }
          )
        }
      },

      onClick: (e) => {
        cl(
          'Event Identifier after Merge : ::: \n\n\n\n Click \n\n\n\n :::',
          props,
          client
        )
        if (props?.htmlAttr.onClick) {
          props?.htmlAttr?.onClick(
            checkIsLinkValid(props?.href)
              ? { ...updatedProps, href: updatedUrl }
              : e
          )
        } // cl(props?.isExperimentation, props?.experimentEvents, props, SimpleCTAsD, 'Identifier : CTA :Click')

        if (props?.isExperimentation && props?.experimentEvents === 'Click') {
          // cl(
          //   props?.isExperimentation,
          //   props?.experimentEvents,
          //   'Identifier : CTA :Click'
          // )
          client.logEvent(
            `${props?.textContent}`,
            `next14_click_${props?.experimentEvents}`,
            {
              href: props?.href,
              variant: props?.variant,

              // item_name: 'diet_coke_48_pack',
              // ...data,
            }
          )
        }
        // cl('Click of Button CTA ButtonFilter')

        // handleClickStatsig({ name: props?.actionContent?.__typename })

        if (props?.actionContent?.__typename === 'ComponentPopup') {
          dispatch(setPopupData(props?.actionContent.componentPopup))
          dispatch(setShow(true))
          document.body.style.overflow = 'hidden'
          e.preventDefault()
        } else if (props?.actionContent?.__typename === 'ComponentForm') {
          document.body.style.overflow = 'hidden'
          dispatch(
            setPopupData({
              __typename: 'ComponentPopup',
              childrenCollection: {
                items: [props?.actionContent],
              },
            })
          )
          dispatch(setShow(true))
          e.preventDefault()
        }
      },
      rel: props.target === '_blank' ? 'noopener' : '',
    },
  }

  let children = updatedProps.children

  let classname = updatedProps.htmlAttr?.className

  let linkColor = `${!updatedProps.disableLinkColor &&
    (!updatedProps.isLightMode ? style.linkRootDark : style.linkRootLight)
    } ${updatedProps.htmlAttr?.className}`

  /*   let linkColor = `${updatedProps.theme === 'DARK' ? style.linkRootDark : style.linkRootLight}`
                    let linkButtonColor = `${updatedProps.theme === 'DARK' ? style.linkButtonDarkRoot : style.ctaRoot}` */

  // Determine the CSS class based on the 'variant' prop
  switch (updatedProps.variant) {
    case 'primary':
      classname += ` ${style.primary}`
      break
    case 'secondary':
      classname += ` ${style.secondary}`
      break
    case 'tertiary':
      classname += ` ${style.tertiary}`
      break
    case 'tertiary2':
      classname += ` ${style.tertiary2}`
      break
    case 'tertiary3':
      classname += ` ${style.tertiary3}`
      break
    default:
      break
  }
  // updating css classes for dark theme
  if (!updatedProps.isLightMode) {
    switch (updatedProps.variant) {
      case 'primary':
        classname += ` ${style['dark-mode-primary']}`
        break
      case 'secondary':
        classname += ` ${style['dark-mode-secondary']}`
        break
      case 'tertiary':
        classname += ` ${style['dark-mode-tertiary']}`
        break
      case 'tertiary2':
        classname += ` ${style['dark-mode-tertiary2']}`
        break
      case 'tertiary3':
        classname += ` ${style['dark-mode-tertiary3']}`
        break
      default:
        // Handle default case if necessary
        break
    }
  }
  // Check if the button is disabled and update HTML attributes accordingly
  if (updatedProps.isEnabled == false) {
    classname = style.disabled
    if (updatedProps.htmlAttr) {
      updatedProps.htmlAttr.disabled = true
    }
  }

  if (updatedUrl) {
    if (
      // Exclude external links like https and http.
      !updatedUrl?.split(':')?.[0]?.toLowerCase()?.startsWith('http') &&
      updatedUrl?.charAt(0) !== '/' &&
      updatedUrl?.charAt(0) !== '#' &&
      !updatedUrl.startsWith('mailto:') &&
      !updatedUrl.startsWith('tel:')
    ) {
      setUpdatedUrl('/' + updatedUrl)
    }

    if (isBrowser()) {
      const searchParams = getQueryParamJson()

      if (searchParams?.lang && !updatedUrl.includes('lang')) {
        const url = new URL(updatedUrl, window.location.origin)
        url.searchParams.set('lang', searchParams['lang'])
        setUpdatedUrl(url.toString())
      }
    }
  }
  if (UpdaterdUrlforLink) {
    if (
      // Exclude external links like https and http.
      !UpdaterdUrlforLink?.split(':')?.[0]?.toLowerCase()?.startsWith('http') &&
      UpdaterdUrlforLink?.charAt(0) !== '/' &&
      UpdaterdUrlforLink?.charAt(0) !== '#' &&
      !UpdaterdUrlforLink?.startsWith('mailto:') &&
      !UpdaterdUrlforLink?.startsWith('tel:')
    ) {
      UpdaterdUrlforLink = '/' + UpdaterdUrlforLink
    }
    // console.log(props)
    if (
      props?.locale &&
      BUISNESS_DOMAINS['altus'] === process.env.NEXT_PUBLIC_DOMAIN &&
      !UpdaterdUrlforLink?.split(':')?.[0]?.toLowerCase()?.startsWith('http') &&
      !UpdaterdUrlforLink?.startsWith('mailto:') &&
      !UpdaterdUrlforLink?.startsWith('tel:')
    )
      UpdaterdUrlforLink = UpdaterdUrlforLink + localeMap[props?.locale]
    // console.log(getLocalizationSlugByLocale)
    // if (isBrowser()) {
    //   const searchParams = getQueryParamJson()

    //   if (searchParams?.lang && !UpdaterdUrlforLink.includes('lang')) {
    //     const url = new URL(UpdaterdUrlforLink, window.location.origin)
    //     url.searchParams.set('lang', searchParams['lang'])
    //     UpdaterdUrlforLink = url.toString()
    //   }
    // }
  }
  //
  const ariaLabel =
    updatedProps.htmlAttr?.['aria-label'] ??
    generateAriaLabelFromSlug(updatedUrl, updatedProps.textContent)

  const component = updatedProps.isFormButton ? (
    <button
      {...updatedProps.htmlAttr}
      {...getPreviewProps(props)}
      // if the isButton prop is true, the link is rendered as a link button, else as a link
      className={` ${updatedProps.isButton ? classname : linkColor
        } simpleCta simpleBtn`}
      aria-label={updatedProps.textContent}
    // role={'button'}
    >
      {/* root button component structure goes here...(@Button wrapper) */}

      {children}
    </button>
  ) : (
    <Link
      {...updatedProps.htmlAttr}
      {...getPreviewProps(props)}
      aria-label={ariaLabel}
      // if the isButton prop is true, the link is rendered as a link button, else as a link
      className={` ${updatedProps.isButton ? classname : linkColor} simpleCta`}
      href={UpdaterdUrlforLink}
      target={updatedProps.target}
    >
      {children}
    </Link>
  )

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Simpletons, return Kernel only.
   */
  return component
}

export const localeMap: {
  [key: string]: string
} = {
  'en-CA': '',
  'fr-CA': '?lang=fr',
  'de-DE': '?lang=de',
  es: '?lang=es',
  it: '?lang=it',
  nl: '?lang=nl',
}
