//Add SimpleButton specific styles only...

//importing the default styles from the global styles
//Default SCSS variables are prepended in next.config.js

//local variables
$buttonHeight: 40px;
$smallButtonHeight: 36px;
$buttonPadding: 11px 20px;
$smallButtonPadding: 11px 15px;
$buttonBorderRadius: 50px;

.linkRootLight {
  //color of the link
  color: $co1;
  // min-width: 110px;
  text-decoration: none;

  //on hover
  &:hover {
    //color of the link on hover
    color: $cp2;
    text-decoration: underline;
  }
}

.linkRootDark {
  color: $cs1;
  //min-width: 110px;
  text-decoration: none;

  //on hover
  &:hover {
    //color of the link on hover
    // color: $cs4;
    text-decoration: underline;
  }
}

// Button container styles for simple button (basic button structure)
.buttonRoot {
  width: max-content;
  //width of the button
  min-width: 110px;
  //height of the button
  height: $buttonHeight;
  //padding of the button (top and bottom padding is 0, left and right padding is 36px)
  padding: $buttonPadding;

  //border radius of the button
  border-radius: $buttonBorderRadius;
  //background color of the button
  background-color: $cp2;
  // color of the items inside the button (text, icons, etc.)
  color: white;
  // font size of the items inside the button (text, icons, etc.)
  font-size: $fs4;
  font-family: $fSansBld;
  //cursor of the button (pointer to indicate that the button is clickable)
  cursor: pointer;
  //removing the default button outline styles
  border: none;
  //text transform of the text is capitalize to ensure capitalization of the text
  // text-transform: capitalize;
  //gap is the space between the icon and the text
  gap: 8px;
  text-wrap: nowrap;
  text-decoration: none;

  //hover styles for the button
  &:hover {
    transition: 0.25s ease-in-out;
    //background color of the button
    background-color: $cp2;
  }

  @media screen and (max-width: 733px) {
    //padding of the button (top and bottom padding is 0, left and right padding is 22px)
    padding: $smallButtonPadding;
    //height of the button is 36px
    height: $smallButtonHeight;
    //reduce the gap between the icon and the text to 4px
    gap: 4px;
  }
}

.primary {
  @extend .buttonRoot;
  background-color: $cp2;
  //display of the button (inline-flex to center the items inside the button)
  display: inline-flex;
  //justify content of the button (center the items inside the button)
  justify-content: center;
  //align items of the button (center the items inside the button)
  align-items: center;
  text-wrap: wrap;
  // height: auto;

  // used 460px custom size because mobile screen size starts from 733px and we don't need padding on that screen size.
  @media (max-width: 460px) {
    // width: auto;
  }

  &:hover {
    //background color of the button
    background-color: $cp3;
  }
}

// Dark mode styles for primary variant
.dark-mode-primary {
  background-color: $cs1;
  color: $cn2;

  &:hover {
    background-color: $cs4;
  }
}

.secondary {
  @extend .buttonRoot;
  background-color: $cn8;
  color: $cn2;
  //display of the button (inline-flex to center the items inside the button)
  display: inline-flex;
  //justify content of the button (center the items inside the button)
  justify-content: center;
  //align items of the button (center the items inside the button)
  align-items: center;

  &:hover {
    //background color of the button

    background-color: $cp3;
    color: $cn8;
  }
}

// Dark mode styles for secondary variant
.dark-mode-secondary {
  &:hover {
    background-color: $cs4;
    color: $cn2;
  }
}

.tertiary {
  @extend .buttonRoot;
  background-color: transparent;
  color: $cp2;
  height: auto;
  //display of the button (inline-flex to center the items inside the button)
  display: inline-flex;
  //justify content of the button (center the items inside the button)
  justify-content: center;
  //align items of the button (center the items inside the button)
  align-items: center;
  padding: 0;

  &:hover {
    // color: $cp3;
    //background color of the button
    background-color: transparent;
  }
}

// Dark mode styles for tertiary variant
.dark-mode-tertiary {
  color: $cs1;

  &:hover {
    // color: $cn8;
  }
}

.tertiary2 {
  @extend .buttonRoot;
  height: auto;
  //display of the button (inline-flex to center the items inside the button)
  display: inline-flex;
  //justify content of the button (center the items inside the button)
  justify-content: center;
  //align items of the button (center the items inside the button)
  align-items: center;
  background-color: transparent;
  color: $cp1;

  padding: 0;

  &:hover {
    color: $cp2;
    //background color of the button
    background-color: transparent;
  }
}

// Dark mode styles for tertiary2 variant
.dark-mode-tertiary2 {
  color: $cn8;

  &:hover {
    color: $cs1;
  }
}

.tertiary3 {
  @extend .buttonRoot;
  height: auto;
  //display of the button (inline-flex to center the items inside the button)
  display: inline-flex;
  //justify content of the button (center the items inside the button)
  justify-content: center;
  //align items of the button (center the items inside the button)
  align-items: center;
  background-color: transparent;
  //color: $cp2;
  color: $cs2;

  padding: 0;

  &:hover {
    color: $cs2;
    //color: $cp2;
    //background color of the button
    background-color: transparent;
  }
}

// Dark mode styles for tertiary2 variant
.dark-mode-tertiary3 {
  color: $cs2;
  /*   color: $cs1;

  &:hover {
    color: $cs1;
    }
 */
}

.disabled {
  @extend .buttonRoot;
  background-color: $cn6;
  color: $cn3;

  &:hover {
    //background color of the button
    background-color: $cn6;
  }
}
