import { TipTapBaseNode } from '@wokaylabs/tiptap-react-render/dist/esm/interfaces'
import { ImageProps } from 'next/image'
import { CSSProperties } from 'react'
import { SIZES } from '../../../../globals/types'
import { KernelI } from '../../../Kernel/interface'

/**
 * SimpleImage interface enlists all the props (and their types) that SimpleImage can have.
 */

type ImagePropsWithoutChildren = Omit<
  ImageProps,
  'children' | 'width' | 'height'
>

export interface SimpleImageI extends KernelI, ImagePropsWithoutChildren {
  isRounded?: boolean
  curveSize?: SIZES
  objectFit?: CSSProperties['objectFit']
  width?: CSSProperties['width']
  height?: CSSProperties['height']
  originalWidth?: number | string
  originalHeight?: number | string
  isZoomAble?: boolean
  title?: string
  description?: TipTapBaseNode
  zoomAspectRatio?: string
  objectPosition?: string
  isBrandLogo?: boolean
  //enlist components specific props over here...
}
