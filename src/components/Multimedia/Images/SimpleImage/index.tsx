'use client'
import Image from 'next/image'
import { useState } from 'react'
import { getCleanImageName } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import { setPopupData, setShow } from '../../../../redux/slices/popupSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import FlexContainer from '../../../Containers/FlexContainer'
import SimpleContainer from '../../../Containers/SimpleContainer'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../Kernel'
import { SimpleImageD } from './defaults'
import style from './index.module.scss'
import { SimpleImageI } from './interface'

export default function SimpleImage(props: SimpleImageI) {
  // console.log('🚀 ~ imageClickHandler ~ props:', props)

  const [loading, setLoading] = useState(true)
  const isShow = useAppSelector((state) => state.popup.isShow)

  let className = `${style.imageRoot} ${props.htmlAttr?.className}`

  if (props.isRounded) {
    className += ` rounded ${props.curveSize}`
  }

  const { size } = useWindowSize()
  const dispatch = useAppDispatch()

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: SimpleImageI = {
    ...SimpleImageD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: className,
      style: {
        width: `${props.width}`,
        height: `${props.height}`,
        ...props.htmlAttr?.style,
      },
    },
  }

  // useEffect(() => {
  //     if (isShow) {
  //         setShow(false)
  //     }
  // }, [isShow])

  function imageClickHandler() {
    if (updatedProps.isZoomAble) {
      dispatch(setShow(true))
      dispatch(setPopupData({ __typename: 'ComponentImage', ...updatedProps }))
    }
  }

  const content = updatedProps?.isZoomAble ? (
    <>
      {props?.title && (
        <FlexContainer
          htmlAttr={{ className: style.topContainer }}
          gap='20px'
          width={updatedProps?.htmlAttr?.style?.width}
          maxWidth={updatedProps?.htmlAttr?.style?.maxWidth}
        >
          <SimpleParagraph textContent={props?.title} />
        </FlexContainer>
      )}
      <Kernel
        {...updatedProps}
        htmlAttr={{
          ...updatedProps.htmlAttr,
          onClick: () => {
            imageClickHandler()
            document.body.style.overflow = 'hidden'
          },
          style: {
            ...updatedProps.htmlAttr.style,
            className: className,
            height: updatedProps.htmlAttr.style.aspectRatio
              ? null
              : updatedProps.htmlAttr?.style?.height,
            position: 'relative',
          },
          className: style.imageRootZoomable,
        }}
      >
        {size === 'large' && (
          <div className={style.clickToOpenMessage}>
            <div className={style.clickToOpenMessageContainer}>
              <div className={style.triangle}></div>
              <p>Click to enlarge</p>
            </div>
          </div>
        )}
        {loading && !updatedProps.isBrandLogo && (
          <div className='placeholder'>
            <div className='placeholderanim'></div>
          </div>
        )}
        <Image
          src={updatedProps.src}
          alt={
            Boolean(updatedProps?.alt)
              ? updatedProps.alt
              : getCleanImageName(updatedProps?.src)
          }
          aria-label={updatedProps.alt}
          fill
          style={{ objectFit: updatedProps.objectFit }}
          className={Boolean(props.htmlAttr?.className) ? '' : style.image}
          loader={updatedProps.loader}
          sizes={updatedProps.sizes}
          quality={updatedProps.quality}
          priority={updatedProps.priority}
          placeholder={updatedProps.placeholder}
          onLoadingComplete={() => setLoading(false)}
          onLoad={updatedProps.onLoad}
          onError={updatedProps.onError}
          blurDataURL={updatedProps.blurDataURL}
          objectPosition={updatedProps.objectPosition}
          loading='lazy'
        />
      </Kernel>
      <FlexContainer
        justifyContent='space-between'
        gap='20px'
        htmlAttr={{ className: style.bottomContainer }}
        width={updatedProps?.htmlAttr?.style?.width}
        maxWidth={updatedProps?.htmlAttr?.style?.maxWidth}
      >
        <SimpleContainer>
          {props?.description && <Richtext data={props?.description} />}
        </SimpleContainer>
      </FlexContainer>
      {/* show && <Popup
                handleShow={() => setShow(false)}
                isShow={show}
                htmlAttr={{style: {overflowY: 'auto', maxHeight: 'calc(90vh)', maxWidth: 'calc(90vw)'}}}
                width={(updatedProps?.originalWidth as number + 130) + 'px' as string}
            >
                <Kernel
                    {...updatedProps}
                    htmlAttr={{
                        ...updatedProps.htmlAttr,
                        style: {
                            ...updatedProps.htmlAttr.style,
                            position: 'relative',
                            maxWidth: '100%',
                            maxHeight: '90vh',
                            height: null,
                            aspectRatio: updatedProps?.zoomAspectRatio ?? '1 / 1'
                        }
                    }}
                >
                    <Image
                        src={updatedProps.src}
                        alt={updatedProps.alt}
                        fill
                        className={style.popupImage}
                        loader={updatedProps.loader}
                        sizes={updatedProps.sizes}
                        quality={updatedProps.quality}
                        priority={updatedProps.priority}
                        placeholder={updatedProps.placeholder}
                        onLoadingComplete={() => setLoading(false)}
                        onLoad={updatedProps.onLoad}
                        onError={updatedProps.onError}
                        loading="lazy"
                        blurDataURL={updatedProps.blurDataURL}
                        objectPosition={updatedProps.objectPosition}
                    />
                </Kernel>
            </Popup> */}
    </>
  ) : (
    <>
      <Kernel
        {...updatedProps}
        htmlAttr={{
          ...updatedProps.htmlAttr,
          style: {
            ...updatedProps.htmlAttr.style,
            height: updatedProps.htmlAttr.style.aspectRatio
              ? null
              : updatedProps.htmlAttr?.style?.height,
            position: 'relative',
          },
        }}
      >
        {loading && !updatedProps.isBrandLogo && (
          <div className='placeholder'>
            <div className='placeholderanim'></div>
          </div>
        )}
        <Image
          src={updatedProps.src}
          alt={
            Boolean(updatedProps?.alt)
              ? updatedProps.alt
              : getCleanImageName(updatedProps?.src)
          }
          aria-label={updatedProps.alt}
          fill
          style={{ objectFit: updatedProps.objectFit }}
          className={Boolean(props.htmlAttr?.className) ? '' : style.image}
          loader={updatedProps.loader}
          sizes={updatedProps.sizes}
          quality={updatedProps.quality}
          priority={updatedProps.priority}
          placeholder={updatedProps.placeholder}
          onLoadingComplete={() => setLoading(false)}
          onLoad={updatedProps.onLoad}
          onError={updatedProps.onError}
          loading='lazy'
          blurDataURL={updatedProps.blurDataURL}
          objectPosition={updatedProps.objectPosition}
        />
      </Kernel>
    </>
  )

  return content
}
