'use client'
import { get<PERSON>lean<PERSON>mageName, TheSEOBlock } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import SimpleCTAs from '../../../CTAs/SimpleCTAs'
import SimpleImage from '../SimpleImage'
import styles from './index.module.scss'
import { LogoI } from './interface'
import { getLogoSEObj } from './seo'

export default function Logo(props: LogoI) {
  // Predefine height and width based on orientation
  const { orientation } = props

  /*    const height = orientation === 'Logo Landscape' ? '100px' : '200px'
   const width = orientation === 'Logo Landscape' ? '200px' : '200px' */

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps wherever possible.
   */

  const updatedProps: LogoI = {
    // ...LogoD,
    ...props,
    htmlAttr: {
      ...(props.htmlAttr || {}),
      onDragStart: (e: React.DragEvent<HTMLImageElement>) => e.preventDefault(),
    },
  }
  const { size } = useWindowSize()

  let height
  let width
  const brandLogoH =
    size === 'small'
      ? Number(80 / 100) * updatedProps.height
      : updatedProps.height
  const brandLogoW =
    size === 'small'
      ? Number(80 / 100) * updatedProps.width
      : updatedProps.width

  if (orientation === 'Logo Landscape') {
    height = '100px'
    width = '200px'
  } else if (orientation === 'Logo Portrait') {
    height = '200px'
    width = '200px'
  } else {
    height = brandLogoH + 'px'
    width = brandLogoW + 'px'
  }

  return (
    <>
      {updatedProps.cta ? (
        <>
          <SimpleCTAs
            {...updatedProps.cta}
            htmlAttr={{
              ...updatedProps.htmlAttr,
              className: `${styles.cta} ${props.htmlAttr?.className}`,
            }}
            textContent={
              Boolean(updatedProps?.alt)
                ? updatedProps.alt
                : getCleanImageName(updatedProps?.src)
            }
          >
            <SimpleImage
              {...updatedProps}
              src={updatedProps.src}
              alt={updatedProps.alt}
              htmlAttr={updatedProps.htmlAttr}
              objectFit='contain'
              orientation={updatedProps.orientation}
              height={height}
              width={width}
            />
          </SimpleCTAs>
          <TheSEOBlock seoObj={getLogoSEObj(updatedProps)} />
        </>
      ) : (
        <>
          <SimpleImage
            {...updatedProps}
            src={updatedProps.src}
            alt={updatedProps.alt}
            htmlAttr={updatedProps.htmlAttr}
            objectFit='contain'
            orientation={updatedProps.orientation}
            height={height}
            width={width}
          />
          <TheSEOBlock seoObj={getLogoSEObj(updatedProps)} />
        </>
      )}
    </>
  )
}
