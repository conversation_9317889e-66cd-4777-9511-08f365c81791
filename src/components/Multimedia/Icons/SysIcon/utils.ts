import { AiOutlineInfoCircle } from 'react-icons/ai'
import {
    BsApple,
    BsArrowDown,
    BsArrowRight,
    BsBadgeCc,
    BsBoxArrowUpRight,
    BsBuilding,
    BsCalendar3,
    BsCardText,
    BsCheck,
    BsCheckLg,
    BsChevronBarUp,
    BsChevronDown,
    BsChevronLeft,
    BsChevronRight,
    BsChevronUp,
    BsClock,
    BsDownload,
    BsEnvelope,
    BsFacebook,
    BsFilePdf,
    BsFiletypeJpg,
    BsFiletypePng,
    BsFiletypeSvg,
    BsFillExclamationCircleFill,
    BsFilterCircle,
    BsFullscreen,
    BsFullscreenExit,
    BsGeoAlt,
    BsGlobeAmericas,
    BsLayoutTextSidebarReverse,
    BsLightbulb,
    BsLink,
    BsLinkedin,
    BsList,
    BsMic,
    BsPauseFill,
    BsPersonFill,
    BsPip,
    BsPipFill,
    BsPlayBtn,
    BsPlayCircle,
    BsSearch,
    BsSend,
    BsSendXFill,
    BsShare,
    BsSpotify,
    BsTag,
    BsTelephone,
    BsTrash2,
    BsTwitterX,
    BsUpload,
    BsVolumeDownFill,
    BsVolumeMute,
    BsVolumeUp,
    BsX,
    BsXLg,
    BsYoutube
} from 'react-icons/bs'
import { FaCircleCheck } from 'react-icons/fa6'
import { FiMic } from 'react-icons/fi'
import { HiPlay } from 'react-icons/hi2'
import { IoMdRadioButtonOff } from 'react-icons/io'
import { MdCheckBox, MdCheckBoxOutlineBlank } from 'react-icons/md'
import { RiCheckboxBlankFill, RiEarthLine, RiMapPinLine, RiRadioButtonFill, RiVolumeDownLine } from 'react-icons/ri'
import { RxSlash, RxTrackNext } from 'react-icons/rx'
import { TfiShareAlt } from 'react-icons/tfi'

export const TheIcons = {
    Tag: BsTag,
    SendXFill: BsSendXFill,
    IoMdRadioButtonOff: IoMdRadioButtonOff,
    BsLayoutTextSidebarReverse: BsLayoutTextSidebarReverse,
    RiCheckboxBlankLine: MdCheckBoxOutlineBlank,
    FaCircleCheck: FaCircleCheck,
    AiOutlineInfoCircle: AiOutlineInfoCircle,
    DownChevron: BsChevronDown,
    BsEnvelope: BsEnvelope,
    BsBoxArrowUpRight: BsBoxArrowUpRight,
    BsCardText: BsCardText,
    Close: BsX,
    Download: BsDownload,
    BsCheckLg: BsCheckLg,
    BsCheck: BsCheck,
    Apple: BsApple,
    Delete: BsTrash2,
    BsYoutube: BsYoutube,
    LeftChevron: BsChevronLeft,
    RightChevron: BsChevronRight,
    UpChevron: BsChevronUp,
    RightArrow: BsArrowRight,
    BarUp: BsChevronBarUp,
    Mics: BsMic,
    GeoAlt: BsGeoAlt,
    BsArrowDown: BsArrowDown,
    RiCheckboxBlankFill: RiCheckboxBlankFill,
    BsLinkedin: BsLinkedin,
    BsFillCheckSquareFill: MdCheckBox,
    RiRadioButtonFill: RiRadioButtonFill,
    Slash: RxSlash,
    Buildings: BsBuilding,
    Calendar: BsCalendar3,
    Clock: BsClock,
    Spotify: BsSpotify,
    CardText: BsCardText,
    MapPin: RiMapPinLine,
    Play: HiPlay,
    Pdf: BsFilePdf,
    Mic: FiMic,
    Upload: BsUpload,
    Bulb: BsLightbulb,
    Earth: RiEarthLine,
    Exclamation: BsFillExclamationCircleFill,
    CirclePlay: BsPlayCircle,
    Telephone: BsTelephone,
    Share: BsShare,
    LinkedIn: BsLinkedin,
    Facebook: BsFacebook,
    Twitter: BsTwitterX,
    Send: BsSend,
    Link: BsLink,
    Search: BsSearch,
    Profile: BsPersonFill,
    Hamburger: BsList,
    Linkedin: BsLinkedin,
    Email: BsSend,
    Filter: BsFilterCircle,
    VolumeDown: RiVolumeDownLine,
    VolumeUp: BsVolumeUp,
    VolumeMute: BsVolumeMute,
    VolumeFill: BsVolumeDownFill,
    Fullscreen: BsFullscreen,
    FullscreenExit: BsFullscreenExit,
    Pause: BsPauseFill,
    Skip: RxTrackNext,
    Caption: BsBadgeCc,
    Pip: BsPip,
    PipFill: BsPipFill,
    Expand: TfiShareAlt,
    BsPlayBtn: BsPlayBtn,
    BsXLarge: BsXLg,
    Globe: BsGlobeAmericas,
    Svg: BsFiletypeSvg,
    Png: BsFiletypePng,
    Jpg: BsFiletypeJpg,
}