'use client'
import React from 'react'
import { useDispatch } from 'react-redux'
import { removeTagReducer } from '../../../redux/slices/filterSlice/filtersSlice'
import SimpleParagraph from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import SimpleBubble from '../SimpleBubble'
import { TagD } from './defaults'
import styles from './index.module.scss'
import { TagI } from './interface'

export default function Tag(props: TagI): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: TagI = {
    ...TagD,
    ...props,
  }

  // const tagMap =
  const dispatch = useDispatch()
  const tagId = props.id
  let classname
  let Size = `${styles[props.size!]}`

  if (updatedProps.variant === 'primary') {
    classname = ' cn2 bn6'
  } else if (updatedProps.variant === 'secondary') {
    classname = 'cs2 bp2 '
  }

  const handleClose = (tagId: string) => {
    dispatch(
      removeTagReducer({
        filterType: props.filterType,
        id: tagId,
      })
    )
  }

  const icon = updatedProps.isIcon && (
    <div className={styles.closeIcon}>
      <GenericIcon
        icon='Close'
        size='sm'
        htmlAttr={{
          onClick: () => {
            handleClose(tagId)
          },
          role: 'button',
        }}
      />
    </div>
  )

  //Returning the Tag enclosed within SimpleBubble
  return (
    <SimpleBubble
      {...updatedProps}
      htmlAttr={{
        className: `${styles.tagRoot} ${classname} ${Size}`,
        role: 'button',
        style: updatedProps.htmlAttr?.style,
      }}
    >
      {/* Render a LabelText component with the text from updatedProps */}
      <SimpleParagraph {...updatedProps.text} />
      {icon}
    </SimpleBubble>
  )
}
