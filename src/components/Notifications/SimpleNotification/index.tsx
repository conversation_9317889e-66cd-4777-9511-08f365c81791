import React from 'react'
import Kernel from '../../Kernel'
import { SimpleNotificationD } from './defaults'
import style from './index.module.scss'
import { SimpleNotificationI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function SimpleNotification(props: SimpleNotificationI) {
  let as: SimpleNotificationI['as'] = props.as
  // @ts-ignore
  let htmlAttr: React.ComponentProps<typeof props.as> = {}

  if (props) {
    /**
     * @todo add custom logic if required otherwise remove this block
     */
  }

  /**
   * @todo Add any logic and evaluation code over here
   */

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: SimpleNotificationI = {
    ...SimpleNotificationD,
    ...props,
    as,
    htmlAttr: htmlAttr,
  }

  let children = updatedProps.children

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Simpletons, return Kernel only.
   */
  return (
    <Kernel {...updatedProps} htmlAttr={{ className: style.notification }}>
      {children}
    </Kernel>
  )
}
