'use client'
import { useRef, useState } from 'react'
import SimpleCarousel from '../../../Carousels/SimpleCarousel'
import BannerComp from '../BannerMedium'
import style from './index.module.scss'
import { BannerCarouselI } from './interface'

export default function BannerCarousel(props: BannerCarouselI) {
  const updatedProps: BannerCarouselI = {
    // ...BannerCarouselD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      style: props.htmlAttr?.style,
    },
  }
  const [closedBanners, setClosedBanners] = useState<string[]>([])

  // ... existing code ...

  const handleCloseBanner = (bannerId: string) => {
    setClosedBanners((prevClosedBanners) => [...prevClosedBanners, bannerId])
  }

  const items = (updatedProps?.carouselData ?? [])
    .filter((item) => !closedBanners.includes(item.id)) // Filter out closed banners
    .map((item, index) => (
      <BannerComp
        key={index}
        {...item}
        onClose={() => handleCloseBanner(item.id)}
      />
    ))
  const carouselRef = useRef<HTMLDivElement>(null)

  return (
    <SimpleCarousel
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: style.simpleCarousel,
      }}
      items={items}
      isPresent
      isVisible
      indicatorsPosition={'Invisible'}
      timer={5000}
      ref={carouselRef}
    ></SimpleCarousel>
  )
}
