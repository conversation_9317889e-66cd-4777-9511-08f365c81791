//Default SCSS variables are prepended in next.config.js

.bannerContainer {
  width: 100%;
  height: auto; // Default height

  padding-top: 17px;
  padding-bottom: 27px;

  color: $cn2;

  /* Set a default text color, which is black for other variants */
  .simpleHeading {
    /* Styles for the light mode (if you have any) */
    color: $cn2;
    /* Default text color for light mode */
    padding-bottom: 10px;
  }

  .simpleHeadingDark {
    /* Styles for the dark mode */
    color: $cs2;
    /* Text color for dark mode (white) */
    padding-bottom: 10px;
  }

  .noHeading {
    display: none;
  }

  .noheadingDark {
    display: none;
  }

  &.secondary {
    color: $cn8;
    /* Set the text color to white for the 'secondary' variant */
  }

  .link {
    text-decoration: none;
    color: $cp2;
    font-size: $fs3;

    &:hover {
      font-family: $fSansBld;
      color: $cp2;
    }

    &.secondary-link {
      color: $cs1;
    }
  }

  .textContainer {
    line-height: 24px;
  }
}

.textFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hidden {
  display: none;
}

.primary {
  background: #d8f782;
}

.secondary {
  background: $cp2;
}

@media screen and (max-width: $smScreenSize) {
  .bannerContainer {
    .heading {
      display: none;
    }

    .textContainer {
      padding-right: 20px;
    }
  }
}

.span {
  padding-right: 10px;
}
