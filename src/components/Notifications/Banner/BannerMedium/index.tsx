'use client'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import {
  getSessionStorageItem,
  isBannerVisible,
  setSessionStorageItem,
} from '../../../../globals/utils'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import LayoutContainer from '../../../Containers/LayoutContainer'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleSpan from '../../../ContentBlocks/Texts/Spans/SimpleSpan'
import GenericIcon from '../../../Multimedia/Icons/SysIcon'
import SimpleNotificationComp from '../../SimpleNotification'
import styles from './index.module.scss'
import { BannerCompI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function BannerComp(props: BannerCompI) {
  const [isBannerContainerVisible, setBannerContainerVisible] = useState(true)
  // @ts-ignore
  let htmlAttr: React.ComponentProps<typeof props.as> = {}

  if (props) {
    /**
     * @todo add custom logic if required otherwise remove this block
     */
  }

  /**
   * @todo Add any logic and evaluation code over here
   */

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  // Check if the banner is closed in sessionStorage
  const isBannerClosed = getSessionStorageItem(`banner_${props?.id}_closed`)

  const pathname = usePathname()

  useEffect(() => {
    setBannerContainerVisible(isBannerVisible(props, pathname))
  }, [props.isGlobal, props.SpecificPage, props.categoriesPage, pathname])

  let isLightMode = false
  if (props.variant === 'secondary') {
    isLightMode = false
  } else if (props.variant === 'primary') {
    isLightMode = true
  }

  const updatedProps: BannerCompI = {
    // ...BannerCompD,
    ...props,
    htmlAttr: htmlAttr,
    button: {
      ...props.button,
      isLightMode: isLightMode,
    },
  }

  let children = updatedProps.children

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Family components, return Family Simpletons only.
   */
  const headingClassName =
    props.variant === 'primary'
      ? styles.simpleHeading
      : styles.simpleHeadingDark

  const [isPresent, setIsPresent] = useState(!isBannerClosed)
  const HandleCloseClick = () => {
    setBannerContainerVisible(false)
    setSessionStorageItem(`banner_${props.id}_closed`, true)
    // props.onClose()
  }
  // Check if closedBanners is defined before using includes
  if (props.closedBanners && props.closedBanners.includes(props.id)) {
    return null // Don't render if the banner is closed
  }

  // Check if isPersistent is false before rendering the close button
  const renderCloseButton = () => {
    if (!props.isPersistent) {
      return (
        <GenericIcon
          icon='Close'
          size='md'
          htmlAttr={{
            onClick: HandleCloseClick,
          }}
        />
      )
    }
    return null // No close button if isPersistent is true
  }

  // l('Current Page URL:', window.location.href)
  return (
    <SimpleNotificationComp {...updatedProps} isFullXBleed>
      <div
        className={`${styles.bannerContainer} ${
          isBannerContainerVisible
            ? updatedProps.variant === 'primary'
              ? styles.primary
              : styles.secondary
            : styles.hidden // Add a 'hidden' class when not visible
        }`}
      >
        <LayoutContainer>
          <div className={styles.textFlex}>
            <div className={styles.textContainer}>
              <div className={styles.heading}>
                <SimpleHeading
                  {...updatedProps.heading}
                  as='h6'
                  displayAs='h6'
                  htmlAttr={{ className: headingClassName }}
                />
              </div>
              <SimpleSpan
                {...updatedProps.description}
                htmlAttr={{ className: styles.span }}
              />
              {updatedProps.Link && (
                <SimpleButtonWIcon
                  {...updatedProps.Link}
                  as='span'
                  htmlAttr={{
                    className: `${styles.link} ${
                      updatedProps.variant === 'secondary'
                        ? styles['secondary-link']
                        : ''
                    }`,
                  }}
                />
              )}
            </div>
            {renderCloseButton()} {/* Render close button conditionally */}
          </div>
        </LayoutContainer>
      </div>
      {children}
    </SimpleNotificationComp>
  )
}
