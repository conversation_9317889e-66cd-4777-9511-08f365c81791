import { SimpleHeadingI } from '../../../ContentBlocks/Texts/Headings/SimpleHeading/interface'
import { LinkI } from '../../../CTAs/Link/interface'
import { SimpleBannerI } from '../SimpleBanner/interface'

export interface BannerCompI extends SimpleBannerI {
  /**
   *@todo ...enlist props that are unique to SimpleNotification
   */
  heading?: SimpleHeadingI
  Link?: LinkI
  variant?: 'primary' | 'secondary'
  isPersistent?: boolean
  isGlobal?: boolean
  isEnabled?: boolean
  SpecificPage?: string[] // Array of specific page paths where the banner should be visible
  categoriesPage?: string[] // Array of page categories where the banner should be visible
  id: string
  onClose?: () => void // Callback function for closing the banner
  closedBanners?: string[] // List of closed banner IDs
}
