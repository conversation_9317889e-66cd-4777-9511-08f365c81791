'use client'
import { usePathname } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import {
  getSessionStorageItem,
  isBannerVisible,
  setSessionStorageItem,
} from '../../../../globals/utils'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import LayoutContainer from '../../../Containers/LayoutContainer'
import SimpleSpan from '../../../ContentBlocks/Texts/Spans/SimpleSpan'
import GenericIcon from '../../../Multimedia/Icons/SysIcon'
import SimpleBanner from '../SimpleBanner'
import styles from './index.module.scss'
import { BannerSmallI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function BannerSmall(props: BannerSmallI) {
  const [isBannerContainerVisible, setBannerContainerVisible] = useState(true)
  // @ts-ignore
  let htmlAttr: React.ComponentProps<typeof props.as> = {}

  if (props) {
    /**
     * @todo add custom logic if required otherwise remove this block
     */
  }

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const isBannerClosed = getSessionStorageItem(`banner_${props?.id}_closed`)
  const pathname = usePathname()
  useEffect(() => {
    setBannerContainerVisible(isBannerVisible(props, pathname))
  }, [props.isGlobal, props.SpecificPage, props.categoriesPage, pathname])

  let isLightMode = false
  if (props.variant === 'secondary') {
    isLightMode = false
  } else if (props.variant === 'primary') {
    isLightMode = true
  }
  const updatedProps: BannerSmallI = {
    // ...BannerSmallD,
    ...props,
    htmlAttr: htmlAttr,
    button: {
      ...props.button,
      isLightMode: isLightMode,
    },
  }

  let children = updatedProps.children
  // const isSecondaryVariant = updatedProps.variant === 'secondary'
  // const isPrimaryVariant = updatedProps.variant === 'primary'
  // // Update the SimpleButtonWIcon's theme prop based on the variant
  // if(isSecondaryVariant) {
  //     updatedProps.button.isLightMode = false
  // } else if(isPrimaryVariant) {
  //     updatedProps.button.isLightMode = true
  // }
  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Family components, return Family Simpletons only.
   */
  const [isPresent, setIsPresent] = useState(!isBannerClosed)
  const HandleCloseClick = () => {
    setIsPresent(false)
    setSessionStorageItem(`banner_${props.id}_closed`, true)
  }
  // Check if isPersistent is false before rendering the close button
  const renderCloseButton = () => {
    if (!props.isPersistent) {
      return (
        <GenericIcon
          icon='Close'
          size='md'
          htmlAttr={{
            onClick: HandleCloseClick,
          }}
        />
      )
    }
    return null // No close button if isPersistent is true
  }
  // l('Current Page URL:', window.location.href)
  return (
    <SimpleBanner {...updatedProps} isFullXBleed isPresent={isPresent}>
      <div
        className={`${styles.bannerContainer} ${
          isBannerContainerVisible
            ? updatedProps.variant === 'primary'
              ? styles.primary
              : styles.secondary
            : styles.hidden // Add a 'hidden' class when not visible
        }`}
      >
        <LayoutContainer>
          <div className={styles.textFlex}>
            <div className={styles.textContainer}>
              <SimpleSpan
                {...updatedProps.description}
                htmlAttr={{ className: styles.span }}
              />
              <SimpleButtonWIcon
                {...updatedProps.cta}
                as={'span'}
                htmlAttr={{
                  className: `${styles.link} ${
                    updatedProps.variant === 'secondary'
                      ? styles['secondary-link']
                      : ''
                  }`,
                }}
              />
            </div>
            {renderCloseButton()} {/* Render close button conditionally */}
          </div>
        </LayoutContainer>
      </div>
      {children}
    </SimpleBanner>
  )
}
