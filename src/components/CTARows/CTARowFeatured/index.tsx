import React from 'react'
import { BGCOLOURS } from '../../../globals/types'
import LayoutContainer from '../../Containers/LayoutContainer'
import ContextualInformation from '../../ContentBlocks/ContextualInformation'
import Kernel from '../../Kernel'
import { BackgroundImage } from '../../Multimedia/Images'
import { CTARowFeaturedD } from './defaults'
import styles from './index.module.scss'
import { CTARowFeaturedI } from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function CTARowFeatured(props: CTARowFeaturedI) {
  let htmlAttr: React.ComponentProps<typeof props.as>

  if (props) {
    /**
     * @todo add custom logic if required otherwise remove this block
     */
  }

  /**
   * @todo Add any logic and evaluation code over here
   */

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CTARowFeaturedI = {
    ...CTARowFeaturedD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
    },
  }
  const subheadingColor = updatedProps?.isLightMode
    ? styles.subheading
    : styles.subheadingDark
  const overlayClasses = updatedProps.isBlur
    ? ' bgTranslucent'
    : `${styles.overlay}`

  const BGColors: BGCOLOURS[] = [
    'bp1',
    'bp2',
    'bs1',
    'bs2',
    'bs3',
    'bs5',
    'bn1',
    'bn3',
    'bn4',
    'bn5',
  ]

  // Split the class name string and check each part
  const classList = updatedProps?.htmlAttr?.className?.split(' ')

  // Find if any part of the class name matches a BGCOLOURS
  const BGCOLOUR = classList?.find((bg: BGCOLOURS) => BGColors.includes(bg))

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Family components, return Family Simpletons only.
   */
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{ className: styles.ctarowContainer }}
      isFullXBleed
    >
      <BackgroundImage {...updatedProps.backgroundImage} />
      <LayoutContainer>
        <div
          className={`${styles.content} ${BGCOLOUR ? BGCOLOUR : overlayClasses}`}
        >
          <ContextualInformation
            {...updatedProps.contextualInformation}
            isLightMode={updatedProps?.isLightMode}
            subHeading={{
              ...updatedProps.contextualInformation?.subHeading,
              htmlAttr: { className: subheadingColor },
            }}
            heading={{
              ...updatedProps.contextualInformation?.heading,
              as: 'h2',
              htmlAttr: { className: styles.heading },
            }}
            excerpt={{
              ...updatedProps.contextualInformation?.excerpt,
              as: 'p',
            }}
            showButtons={true}
            buttons={{
              ...updatedProps.contextualInformation?.buttons,
            }}
          />
        </div>
      </LayoutContainer>
    </Kernel>
  )
}
