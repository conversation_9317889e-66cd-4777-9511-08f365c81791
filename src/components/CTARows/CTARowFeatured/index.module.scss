//Default SCSS variables are prepended in next.config.js

.ctarowContainer {
  position: relative;
}

.content {
  max-width: 750px;
  min-width: $minWidth;
  padding: 50px;
  margin: 100px 0;
  border-radius: 4px;

  @media screen and (max-width: $mScreenSize) {
    margin: 50px 0;
  }

  @media screen and (max-width: $smScreenSize) {
    padding: 50px 20px;
  }
}

.heading {
  margin-top: 0;
}

.subheading {
  color: $cp2 !important;
}

.subheadingDark {
  color: $cs1 !important;
}

.overlay {
  background: linear-gradient(
    45deg,
    rgba(10, 44, 55, 0.8) 0%,
    rgba(20, 37, 69, 0.16) 120%
  );
}

@media screen and (max-width: $mScreenSize) {
}
