.rootContainer {
  display: flex;
  flex-direction: row;
  color: $cn2;
  font-family: $fSansReg;
  gap: 50px;

  .linkIon {
    padding-top: 23px;
  }

  .calender {
    // max-width: 150px;
    color: $cn2;
    text-decoration: none;
    font-family: $fSansReg;
    padding: 30px 0;

    @media screen and (max-width: $smScreenSize) {
      padding: 0;
    }
  }

  .clock {
    // max-width: 150px;
    color: $cn2;
    text-decoration: none;
    text-transform: lowercase;
    font-family: $fSansReg;
    padding: 30px 0;

    @media screen and (max-width: $smScreenSize) {
      padding: 0;
    }
  }

  .download {
    color: $cn2;
    text-decoration: none;
    font-family: $fSansReg;
    padding: 30px 0;

    &:hover {
      color: $cp2;
    }

    @media screen and (max-width: $smScreenSize) {
      padding: 0;
    }
  }

  .downloadcontainer {
    cursor: pointer;
  }

  .shareContainer {
    position: relative;

    .shareBox {
      background-color: $cn8;
      width: 197px;
      max-width: 197px;
      padding: 30px 35px;
      box-shadow: 0px 10px 100px 1px rgba(10, 44, 55, 0.1);
      border-radius: 4px;
      position: absolute;
      display: none;
      flex-direction: column;
      z-index: 1;
      top: 75px;
      left: -25px;

      @media (max-width: $smScreenSize) {
        left: -20px;
        top: 60px;
      }

      .shareBoxIcons {
        max-width: 150px;
        color: $cn2;
        padding-top: 15px;
        display: flex;
        text-decoration: none;
        align-items: center;
        font-family: $fSansReg;
        justify-content: space-between;

        &>div {
          gap: 20px;
        }

        &:hover {
          color: $cp2;
        }
      }
    }

    &:hover {
      .shareBox {
        display: flex;
      }
    }
  }

  .darkShareBox {
    display: none;
    background-color: $cp1;
    width: 197px;
    max-width: 197px;
    padding: 31px 25px;
    box-shadow: 0px 10px 100px 1px rgba(10, 44, 55, 0.1);
    border-radius: 4px;
    flex-direction: column;
    z-index: 1;
    top: 75px;
    left: -25px;
    /* Ensure it's above other content */

    .shareBoxIcons {
      max-width: 150px;
      color: $cn2;
      padding-top: 15px;
      justify-content: space-between;

      &>div {
        gap: 20px;
      }

      &:hover {
        color: $cp2;
      }
    }
  }

  .shareButton {
    // max-width: 150px;
    color: $cn2;
    font-family: $fSansReg;
    display: flex;
    justify-content: flex-start;
    text-decoration: none;
    padding: 30px 0;

    @media screen and (max-width: $smScreenSize) {
      padding: 0;
    }
  }

  .linkedinIcon,
  .facebookIcon,
  .twitterIcon {
    margin-left: -8px;
    gap: 12px;
  }

  .linkedinIcon .facebookIcon .twitterIcon .emailIcon .linkIcon {
    justify-content: flex-start;
    text-decoration: none;
  }

  .emailIcon {
    span {
      gap: 20px;
    }
  }

  .shareThisOn {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    // gap: 23px;
  }

  .sendThisBy {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    max-width: 400px;
  }

  .closeContainer {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .sendThisByHeading {
    padding-top: 23px;
  }

  .closeButton {
    display: none;
  }

  .darkThemeMainBar {
    max-width: 150px;
    color: $cs2;
    text-decoration: none;
    text-transform: lowercase;
  }

  .darkThemeshareBox {
    color: $cs2;
    max-width: 150px;
    padding-top: 23px;

    &:hover {
      color: $cs1;
    }

    text-decoration: none;
  }

  .darkHeading {
    color: $cs2;
  }

  .containerOne {
    display: flex;
    gap: 50px;
  }

  .containerTwo {
    display: flex;
    gap: 50px;

    // @media screen and (max-width: $smScreenSize) {
    //   gap: 80px;
    //   }
  }

  @media screen and (max-width: $smScreenSize) {
    flex-wrap: wrap;
    row-gap: 30px;
  }
}

.calendercontainer {
  display: flex;
  gap: 12px;
  align-items: center;
  text-wrap: nowrap;
}

.clockcontainer {
  display: flex;
  gap: 12px;
  align-items: center;
  text-wrap: nowrap;
}

.downloadcontainer {
  display: flex;
  gap: 12px;
  align-items: center;
  text-wrap: nowrap;
  cursor: pointer;

  &:hover {
    color: $cp2;
  }
}

.linkcontainer {
  display: flex;
  align-items: center;
  text-wrap: nowrap;
  align-items: center;
  gap: 20px;

  &:hover {
    color: $cp2;
  }
}

.successToast {
  position: fixed;
  bottom: 16px;
  /* Adjust this value to set the distance from the bottom */
  left: 50%;
  transform: translateX(-50%);
  transition: all 2s ease-in-out;
  z-index: 1000;

  /* Adjust the z-index as needed to ensure it appears above other elements */
  // width: 100%;
  @media screen and (max-width: $smScreenSize) {
    width: 100%;
  }
}

.dvToolbar {
  width: fit-content;
  background-color: transparent !important;
}

.dvShareDiv {
  padding: 0 0 20px 20px !important;

  .dvShareP {
    padding: 0 !important;
  }
}

.dvShareBox {
  top: 40px !important;
  left: -90px !important;
}