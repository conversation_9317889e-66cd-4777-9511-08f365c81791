import { SimpleButtonWIconI } from '../../CTAs/SimpleButtonWIcon/interface'
import { IconI } from '../../Multimedia/Icons/SysIcon/interface'
import { SimpleSocialShareI } from '../SimpleSocialShare/interface'

export interface <PERSON>lbarI extends SimpleSocialShareI {
  /**
   *@todo ...enlist props that are unique to SimpleSocialShare
   */
  calender?: SimpleButtonWIconI
  clock?: SimpleButtonWIconI
  download?: SimpleButtonWIconI
  share?: SimpleButtonWIconI
  linkedin?: SimpleButtonWIconI
  facebook?: SimpleButtonWIconI
  twitter?: SimpleButtonWIconI
  email?: SimpleButtonWIconI
  link?: SimpleButtonWIconI
  onThisPage?: SimpleButtonWIconI
  close?: IconI
  title?: string
  bookMarkId?: string
  hidCalender?: boolean
  hideClock?: boolean
  isDvToolBar?:boolean
}
