'use client'
import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { usePathname } from 'next/navigation'
import React, { useState } from 'react'
import {
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton,
} from 'react-share'
import { isBrowser, l, useTranslation } from '../../../globals/utils'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleParagraph from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import SimpleSpan from '../../ContentBlocks/Texts/Spans/SimpleSpan'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import SuccessToast from '../../Notifications/Toasts/SuccessToast'
import SimpleSocialShare from '../SimpleSocialShare'
import { ToolbarD } from './defaults'
import styles from './index.module.scss'
import { ToolbarI } from './interface'

export default function Toolbar(props: ToolbarI) {
  let htmlAttr: React.ComponentProps<typeof props.as>
  const [isShareBoxVisible, setShareBoxVisible] = useState(false)
  const [isFullXBleed, setIsFullBleed] = useState(false)
  const [isSuccessToastVisible, setSuccessToastVisible] = useState(false)

  const successToastContent = {
    data: {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          attrs: {
            textAlign: 'left',
          },
          content: [
            {
              type: 'text',
              text: 'Your link has been copied!',
            },
          ],
        },
      ],
    },
  }

  if (props) {
    // Add custom logic if required
  }
  const copyToClipBoard = (txt: string) => {
    if (isBrowser()) {
      navigator.clipboard
        .writeText(txt)
        .then(() => {
          setSuccessToastVisible(true)
        })
        .catch((error) => {
          l('Error', error)
        })
    }
  }

  const updatedProps: ToolbarI = {
    ...ToolbarD,
    ...props,
    htmlAttr: htmlAttr,
  }
  const headingClassName = updatedProps.isLightMode ? 'cn2' : 'cs2'
  const domain = isBrowser() ? window.location.origin : ''
  const pathname = usePathname()
  let children = updatedProps.children
  const bookmarkId = props?.bookMarkId ? `#${props?.bookMarkId}` : ''
  const fullURL = domain + `${pathname}${bookmarkId}`
  const mailVariable = fullURL?.includes('insights')
    ? 'insight article'
    : 'press release'

  // Date Formate
  const formatISODateToLongFormat = (isoDate) => {
    const dateObject = new Date(isoDate)
    const options = { year: 'numeric', month: 'long', day: 'numeric' }
    const formattedDate = dateObject.toLocaleDateString('en-US', options)
    return formattedDate
  }

  // Read Time Function
  function getObject(objects) {
    const array = []

    function getNestedObjectText(theObject) {
      if (theObject instanceof Array) {
        for (let i = 0; i < theObject.length; i++) {
          getNestedObjectText(theObject[i])
        }
      } else if (theObject instanceof Object) {
        for (const prop in theObject) {
          if (Object.prototype.hasOwnProperty.call(theObject, prop)) {
            if (prop === 'type' && theObject[prop] === 'text') {
              array.push(theObject.text)
            } else if (
              theObject[prop] instanceof Object ||
              theObject[prop] instanceof Array
            ) {
              getNestedObjectText(theObject[prop])
            }
          }
        }
      }
    }

    getNestedObjectText(objects)
    return array
  }

  const introTextArray = getObject({ ...updatedProps.clock })

  function calculateReadingTime(wordsPerMinute, wordCount) {
    const minutes = wordCount / wordsPerMinute
    return Math.ceil(minutes)
  }

  const wordsPerMinute = 250

  // Calculate the total word count from the introTextArray
  const totalWordCount = introTextArray.join(' ').split(/\s+/).length
  // Calculate the reading time
  const readingTime = calculateReadingTime(wordsPerMinute, totalWordCount)
  const handleDownloadPDF = () => {
    const pdfUrl = updatedProps.download
    const pdfTitle = updatedProps.title

    // Fetch the PDF file
    fetch(pdfUrl)
      .then((response) => response.blob())
      .then((blob) => {
        // Create a Blob from the fetched data
        const blobObject = new Blob([blob], { type: 'application/pdf' })

        // Create a temporary link element
        const link = document.createElement('a')

        // Set the href attribute with a Blob URL
        link.href = URL.createObjectURL(blobObject)

        // Set the download attribute with the desired filename (using pdfTitle)
        link.download = `${pdfTitle}.pdf`

        // Append the link to the document body
        document.body.appendChild(link)

        // Trigger a click on the link to start the download
        link.click()

        // Remove the link from the document body
        document.body.removeChild(link)
      })
      .catch((error) => {
        console.error('Error fetching the PDF:', error)
      })
  }

  return (
    <SimpleSocialShare
      {...updatedProps}
      isFullXBleed={isFullXBleed}
      htmlAttr={{ className: props?.isDvToolBar ? `${styles.dvToolbar} ${styles.rootContainer}` : styles.rootContainer }}
    >
      {(!props?.hideClock || !props?.hidCalender) ?
        <div className={styles.containerOne}>
          {/* Display "calender," "clock,"**/}
          {!props?.hidCalender && <div className={styles.calendercontainer}>
            <GenericIcon
              icon={'Calendar'}
              isInteractive={false}
            // htmlAttr={{className: styles.hoverNormal}}
            />
            <SimpleParagraph
              {...updatedProps.calender}
              htmlAttr={{
                className: `${!updatedProps.isLightMode
                  ? styles.darkThemeMainBar
                  : styles.calender
                  }`,
              }}
              isLightMode={updatedProps.isLightMode}
              textContent={moment(updatedProps.calender)
                .locale(props?.locale)
                .format('MMMM D, YYYY')}
            />
          </div>}
          {!props?.hideClock && <div className={styles.clockcontainer}>
            <GenericIcon
              icon={'Clock'}
              isInteractive={false}
            // htmlAttr={{className: styles.hoverNormal}}
            />
            <SimpleParagraph
              {...updatedProps.clock}
              htmlAttr={{
                className: `${!updatedProps.isLightMode
                  ? styles.darkThemeMainBar
                  : styles.clock
                  }`,
              }}
              isLightMode={updatedProps.isLightMode}
              textContent={`${readingTime} ${useTranslation(
                'minRead',
                props.locale
              )}`}
            />
          </div>}
        </div> : <></>}
      <div className={styles.containerTwo}>
        {updatedProps.download ? (
          <div className={styles.downloadcontainer} onClick={handleDownloadPDF}>
            <GenericIcon
              icon={'Download'}
              isInteractive={true}
            // htmlAttr={{className: styles.hoverNormal}}
            />
            <SimpleParagraph
              {...updatedProps.download}
              htmlAttr={{
                className: `${!updatedProps.isLightMode
                  ? styles.darkThemeMainBar
                  : styles.download
                  }`,
              }}
              isLightMode={updatedProps.isLightMode}
              textContent={useTranslation('downloadPdf', props.locale)}
            />
          </div>
        ) : (
          ''
        )}

        <div className={styles.shareContainer}>
          <div className={`${styles.calendercontainer} pointer ${props?.isDvToolBar ? styles.dvShareDiv : ''}
`}>
            <GenericIcon
              icon={'Share'}
              isInteractive={false}
            // htmlAttr={{className: styles.hoverNormal}}
            />
            <SimpleParagraph
              {...updatedProps.share}
              htmlAttr={{
                className: `${!updatedProps.isLightMode
                  ? styles.darkThemeMainBar
                  : styles.calender
                  }  ${props?.isDvToolBar ? styles.dvShareP : ''}`,
              }}
              isLightMode={updatedProps.isLightMode}
              textContent={useTranslation('share', props.locale)}
            />
          </div>
          <div
            className={`${!updatedProps.isLightMode
              ? styles.darkShareBox
              : styles.shareBox
              } ${props?.isDvToolBar ? styles.dvShareBox : ''}`}
          >
            <SimpleHeading
              textContent={useTranslation('shareThisOn', props.locale)}
              as={'h6'}
              // htmlAttr={{ style: { paddingBottom: '23px' } }}
              colour={headingClassName}
            />

            <div className={styles.shareThisOn}>
              <LinkedinShareButton
                url={fullURL ?? ''}
                title='share to LinkedIn'
              >
                {/*  <SimpleButtonWIcon */}
                {/*   {...updatedProps.linkedin} */}
                {/*   isIconPrefixed */}
                {/*   htmlAttr={{ */}
                {/*     className: `${styles.linkedinIcon} ${!updatedProps.isLightMode */}
                {/*       ? styles.darkThemeshareBox */}
                {/*       : styles.shareBoxIcons */}
                {/*       } `, */}
                {/*   }} */}
                {/*   isLightMode={updatedProps.isLightMode} */}
                {/* />  */}
                <span
                  className={`${styles.linkedinIcon} ${!updatedProps.isLightMode
                    ? styles.darkThemeshareBox
                    : styles.shareBoxIcons
                    } `}
                >
                  {/*using the icon prop to render the icon  */}

                  <GenericIcon
                    icon={updatedProps.linkedin?.icon}
                    size='sm'
                    as={'span'}
                    // iconColour={iconColor}
                    htmlAttr={{ className: styles.iconContainer }}
                  />
                  <SimpleSpan
                    textContent={updatedProps.linkedin?.textContent}
                  />
                </span>
              </LinkedinShareButton>
              <FacebookShareButton
                url={fullURL ?? ''}
                title='share to Facebook'
              >
                <span
                  className={`${!updatedProps.isLightMode
                    ? styles.darkThemeshareBox
                    : styles.shareBoxIcons
                    } ${styles.facebookIcon} `}
                >
                  {/*using the icon prop to render the icon  */}

                  <GenericIcon
                    icon={updatedProps.facebook?.icon}
                    size='sm'
                    as={'span'}
                    // iconColour={iconColor}
                    htmlAttr={{ className: styles.iconContainer }}
                  />
                  <SimpleSpan
                    textContent={updatedProps.facebook?.textContent}
                  />
                </span>
                {/* <SimpleButtonWIcon */}
                {/*   {...updatedProps.facebook} */}
                {/*   isIconPrefixed */}
                {/*   htmlAttr={{ */}
                {/*     className: `${ */}
                {/*       !updatedProps.isLightMode */}
                {/*         ? styles.darkThemeshareBox */}
                {/*         : styles.shareBoxIcons */}
                {/*     } ${styles.facebookIcon}`, */}
                {/*   }} */}
                {/*   isLightMode={updatedProps.isLightMode} */}
                {/* /> */}
              </FacebookShareButton>
              <TwitterShareButton url={fullURL ?? ''} title='share to Twitter'>
                <span
                  className={`${!updatedProps.isLightMode
                    ? styles.darkThemeshareBox
                    : styles.shareBoxIcons
                    } ${styles.twitterIcon} `}
                >
                  {/*using the icon prop to render the icon  */}

                  <GenericIcon
                    icon={updatedProps.twitter?.icon}
                    size='sm'
                    as={'span'}
                    // iconColour={iconColor}
                    htmlAttr={{ className: styles.iconContainer }}
                  />
                  <SimpleSpan textContent={updatedProps.twitter?.textContent} />
                </span>
                {/* <SimpleButtonWIcon */}
                {/*   {...updatedProps.twitter} */}
                {/*   isIconPrefixed */}
                {/*   htmlAttr={{ */}
                {/*     className: `${ */}
                {/*       !updatedProps.isLightMode */}
                {/*         ? styles.darkThemeshareBox */}
                {/*         : styles.shareBoxIcons */}
                {/*     } ${styles.twitterIcon}`, */}
                {/*   }} */}
                {/*   isLightMode={updatedProps.isLightMode} */}
                {/* /> */}
              </TwitterShareButton>
            </div>
            <SimpleHeading
              textContent={useTranslation('sendThisBy', props.locale)}
              as={'h6'}
              htmlAttr={{ className: styles.sendThisByHeading }}
              isLightMode={updatedProps.isLightMode}
              colour={headingClassName}
            />
            <div className={styles.sendThisBy}>
              <SimpleButtonWIcon
                {...updatedProps.email}
                isIconPrefixed
                htmlAttr={{
                  className: `${!updatedProps.isLightMode
                    ? styles.darkThemeshareBox
                    : styles.shareBoxIcons
                    } ${styles.emailIcon}`,
                }}
                isLightMode={updatedProps.isLightMode}
                href={encodeURI(
                  `mailto:?subject=Sharing a recent ${mailVariable} from Altus Group &body=Hi, I found this recent ${mailVariable} from Altus Group that I thought you might find interesting. ${fullURL ?? ''
                  }`
                )}
              />
              {/* <SimpleButtonWIcon
                {...updatedProps.link}
                htmlAttr={{
                  className: `${
                    !updatedProps.isLightMode
                      ? styles.darkThemeshareBox
                      : styles.shareBoxIcons
                  } ${styles.linkIcon}`,
                  onClick: () => {
                    // Call the copyToClipboard function with the URL
                    copyToClipBoard(fullURL ?? '')
                  },
                }}
                isLightMode={updatedProps.isLightMode}
              /> */}
              <div
                onClick={() => {
                  copyToClipBoard(fullURL ?? '')
                  setSuccessToastVisible(true)
                }}
                className={styles.linkcontainer}
              >
                <GenericIcon
                  icon={'Link'}
                  isInteractive={true}
                  as={'span'}
                  htmlAttr={{ className: styles.linkIon }}
                />
                <SimpleParagraph
                  {...updatedProps.link}
                  htmlAttr={{
                    className: `${!updatedProps.isLightMode
                      ? styles.darkThemeshareBox
                      : styles.shareBoxIcons
                      } ${styles.linkIcon}`,
                  }}
                  isLightMode={updatedProps.isLightMode}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      {isSuccessToastVisible && (
        <div className={styles.successToast}>
          <SuccessToast
            simpleParagraph={successToastContent}
            autoHide
            setToastState={setSuccessToastVisible}
            timer={2000}
          />
        </div>
      )}
    </SimpleSocialShare>
  )
}