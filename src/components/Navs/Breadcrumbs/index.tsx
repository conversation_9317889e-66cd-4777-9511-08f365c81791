import React from 'react'
import {
  makeTitle,
  TheSE<PERSON><PERSON>lock,
  useTranslation
} from '../../../globals/utils'
import { useWindowSize } from '../../../hooks/useWindowSize'
import Link from '../../CTAs/Link'
import Kernel from '../../Kernel'
import { IconI } from '../../Multimedia/Icons/SysIcon/interface'
import { BreadCrumbsD } from './defaults'
import style from './index.module.scss'
import { BreadCrumbI } from './interface'
import { getBreadCrumbSEObj } from './seo'

export default function BreadCrumb(props: BreadCrumbI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: BreadCrumbI = {
    ...BreadCrumbsD,
    ...props,
  }

  let className = style.link
  if (!updatedProps.isLightMode) {
    className += ` ${style.link_dark}`
  }
  let slash = style.slash
  if (!updatedProps.isLightMode) {
    className += ` ${style.slash_dark}`
  }
  let variant = props.variant ?? BreadCrumbsD.variant
  let icon: IconI = { ...BreadCrumbsD.iconProps, ...props.iconProps }
  const { size } = useWindowSize()

  if (!updatedProps.isLightMode) {
    icon.iconColour = 'cs2'
  }

  //break for mobile device
  // if (size === 'small') {
  //     variant = 'short'
  // }

  const paths = updatedProps.fullUrl ?? "preview-editor"

  const pathSegments = paths.split('/').filter((p) => p)

  const locale = updatedProps?.locale || 'en-CA'

  /**
   * Generates default links for a breadcrumb navigation based on the provided variant and properties.
   * @returns BreadcrumbLink An array of breadcrumb link objects.
   */
  const generateDefaultLinks = (): BreadCrumbI[] => {
    const lastSegment =
      pathSegments[variant === 'short' ? 0 : pathSegments.length - 2]
    const title = props?.endText || makeTitle(lastSegment, locale)
    // Handle 'short' variant
    if (variant === 'short') {
      return [
        {
          text: `${useTranslation('backTo', locale)} ${title}`,
          href: `/${lastSegment}`,
        },
      ]
    }

    // When endText is not provided
    if (!props.endText) {
      if (props.isCustomBreadcrumb) {
        return props.breadCrumblinks.map((link) => ({
          text:
            size === 'small'
              ? `${useTranslation('backTo', locale)} ${title}`
              : link.textContent,
          href: link.href?.startsWith('http') ? link.href : link.href,
        }))
      } else {
        // When No Endtext and No isCustomBreadcrumb
        return pathSegments.map((segment, index) => ({
          text:
            size === 'small'
              ? `${useTranslation('backTo', locale)} ${title}`
              : makeTitle(segment, locale),
          href: `/${pathSegments.slice(0, index + 1).join('/')}`,
        }))
      }
    }

    // When endText is provided and isCustomBreadcrumb is false
    if (!props.isCustomBreadcrumb) {
      return [
        {
          text:
            size === 'small'
              ? `${useTranslation('backTo', locale)} ${title}`
              : lastSegment,
          href: `/${lastSegment}`,
        },
        { text: props.endText, href: '#' },
      ]
    }

    // Handling custom breadcrumb with endText
    const longLinks = props.breadCrumblinks.map((link) => ({
      text: link.textContent,
      href: link.href,
    }))
    longLinks.push({ text: props.endText, href: '#' })

    return longLinks
  }

  const links = generateDefaultLinks()

  let itemClasses = `${className} ${style.active}`
  const liClass = `${props.isLightMode ? style.slash : style.slash_dark}`
  let innerComponent

  // Generate innerComponent based on the variant
  innerComponent = links.map((link, index) => (
    <React.Fragment key={index}>
      {index > 0 && <span className={`${liClass}`}> / </span>}{' '}
      <Link
        {...link}
        isExperimentation={props?.isExperimentation}
        experimentEvents={props?.experimentEvents}
        textContent={`${link.text}`}
        disableLinkColor
        htmlAttr={{ className: itemClasses }}
      />
    </React.Fragment>
  ))

  //@ts-ignore
  return (
    <Kernel {...updatedProps} htmlAttr={{ ...updatedProps.htmlAttr }} isAsIs>
      <div>{innerComponent}</div>

      <TheSEOBlock seoObj={getBreadCrumbSEObj(updatedProps)} />
    </Kernel>
  )
}
