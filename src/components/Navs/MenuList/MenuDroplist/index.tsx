'use client'
import { useState } from 'react'
import { RouteToUrl } from '../../../../globals/utils'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import Checkbox from '../../../Controls/Checkbox'
import SimpleMenu from '../SimpleMenu'
import styles from './index.module.scss'
import { MenuDroplistI } from './interface'

// Import Checkbox component, assuming the implementation is available

export default function MenuDroplist(props: MenuDroplistI) {
  const [activeItem, setActiveItem] = useState(0) // Set the first item as active by default

  const handleItemClick = (index) => {
    setActiveItem(index)
    RouteToUrl(props?.links[index]?.href)
  }

  const updatedProps: MenuDroplistI = {
    //...MenuDroplistD,
    ...props,
    htmlAttr: {
      className: props.htmlAttr?.className,
    },
  }

  return (
    <SimpleMenu {...updatedProps}>
      <SimpleHeading
        {...updatedProps.heading}
        as='h6'
        colour='cp1'
        fontFamily='fSansBld'
        htmlAttr={{ className: styles.heading }}
      />
      <ul className={styles.ul}>
        {updatedProps.links?.map((linkProps, i) => (
          <li
            key={i}
            className={`${styles.li} ${i === activeItem ? styles.active : ''}`}
            onClick={() => handleItemClick(i)}
          >
            <Checkbox
              label={linkProps.label} // Use 'label' property for Checkbox component
              isChecked={i === activeItem}
            />
          </li>
        ))}
      </ul>
    </SimpleMenu>
  )
}
