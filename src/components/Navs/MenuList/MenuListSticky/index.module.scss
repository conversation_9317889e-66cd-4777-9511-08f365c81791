//Default SCSS variables are prepended in next.config.js

.root {
  position: sticky;
  top: 30px;
}

.heading {
  padding: 0 5px;
  margin-left: 29px;
  color: $cp1;
  position: relative;
}

.heading_dark {
  @extend .heading;
  color: $cs2;
}

.ul {
  padding: 0;
  margin: 10px 0;
  width: 100%;

  .li {
    // margin: 10px 0;
    padding: 10px 15px;
    transition: 1s;
    text-wrap: wrap;
    display: flex;
    cursor: pointer;
    gap: 1rem;
    text-decoration: none;
    font-size: $fs4;
    color: $cn2;

    .sticky {
      border-left: 3px solid $cn4;
      //  height: 19px;
    }

    /*   .link {
      display: flex;
      flex-direction: column;
      text-decoration: none;
      font-size: $fs4;
      text-wrap: wrap;
      color: $cn2; // Default link color
    } */

    &:hover {
      background: $cn5;
      transition: 500ms;
      color: $cp1;

      .sticky {
        border-left-color: $cp2;
      }

      /*  .link {
        color: $cp1;

      } */
    }

    /*  .link_dark {
      @extend .link;
      color: $cs2;
    } */

    &:hover {
      .sticky_dark {
        @extend .sticky;
        border-left-color: $cs1;
      }

      /*   .link_dark {
        @extend .link;
        color: $cs2;
      } */
    }

    &.active {
      .sticky {
        border-left-color: $cp2;
      }

      /*  .link {
        color: $cn2;
      } */
    }

    &.active {
      .sticky_dark {
        @extend .sticky;
        border-left-color: $cs1;
      }

      /*    .link_dark {
        @extend .link;
        color: white;

      } */
    }
  }

  .li_dark {
    @extend .li;
    cursor: pointer;
    color: $cs2;

    &:hover {
      background-color: $cp5;
      color: $cs2;
    }
  }
}
