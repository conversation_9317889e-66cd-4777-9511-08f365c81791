'use client'
import { useSelector } from 'react-redux'
import { setRecentSearches } from '../../../../globals/utils'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Link from '../../../CTAs/Link'
import SimpleMenu from '../SimpleMenu'
import styles from './index.module.scss'
import { MenuListMegamenuI } from './interface'

export default function MenuListMegamenu(props: MenuListMegamenuI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: MenuListMegamenuI = {
    // ...MenuListMegamenuD,
    ...props,
    htmlAttr: {
      className: props.htmlAttr?.className,
    },
  }

  const { isSearchActive } = useSelector((state: any) => state?.search)

  const handleListOnClick = (linkProps) => {
    //RouteToUrl(linkProps?.href)
    if (!isSearchActive) return

    const inputValue = {
      objectId: linkProps.objectId,
      textContent: linkProps.textContent,
      href: linkProps.href,
    }

    setRecentSearches(inputValue)
  }

  let className = styles.link
  if (!props.isLightMode) {
    className += ` ${styles.link_dark}`
  }
  let heading = `${styles.heading} ${updatedProps.heading?.htmlAttr?.className}`
  if (!props.isLightMode) {
    heading += ` ${styles.heading_dark}`
  }
  const liClass = `${updatedProps.isLightMode ? styles.li : styles.DarkLi}`

  return (
    <SimpleMenu {...updatedProps}>
      <SimpleParagraph
        {...updatedProps.heading}
        fontFamily='fSansReg'
        htmlAttr={{ className: `${heading} fs2 ` }}
        textContent={updatedProps?.heading?.textContent}
      />
      <div className={styles.ul}>
        {updatedProps.links?.map((linkProps, i) => (
          <Link
            key={i}
            {...linkProps}
            htmlAttr={{ className: liClass, onClick: handleListOnClick }}
          />
        ))}
      </div>
    </SimpleMenu>
  )
}
