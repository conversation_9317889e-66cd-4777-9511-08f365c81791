//Default SCSS variables are prepended in next.config.js

.heading {
  padding: 0 15px;
  width: 100%;
  color: $cp2;

  // font-size: $fs3;
  @media screen and (max-width: $smScreenSize) {
    padding: 0;
  }
}

.heading_dark {
  @extend .heading;
  color: $cs1;
}

.ul {
  padding: 0;
  margin: 10px 0;
  display: flex;
  flex-direction: column;

  .li {
    text-wrap: wrap;
    padding: 10px 15px;
    transition: 1s;
    cursor: pointer;
    text-decoration: none;
    color: $cn2;
    font-size: $fs3;

    /*   .link {
      text-decoration: none;
      color: $cn2;
      font-size: $fs3;
    } */

    &:hover {
      background: $cn5;
      transition: 500ms;
      color: $cn2;

      /*   .link {
        color: $cn2;
      } */
    }

    @media screen and (max-width: $smScreenSize) {
      &:first-child {
        padding: 20px 0 10px 0;
      }
      padding: 10px 0;
    }
  }

  .DarkLi {
    text-wrap: wrap;
    padding: 10px 15px;
    transition: 1s;
    cursor: pointer;
    text-decoration: none;
    font-size: $fs3;
    color: $cs2;
    /* .link_dark {
      text-decoration: none;
      font-size: $fs4;
      color: $cs2;
    } */

    &:hover {
      background: $cp5;
      transition: 500ms;
      color: $cs2;
      /*   .link_dark {
        color: $cs2;
        background: none;
      } */
    }

    @media screen and (max-width: $smScreenSize) {
      padding: 10px 0;
    }
  }
}
