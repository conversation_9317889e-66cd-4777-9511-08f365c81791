'use client'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import Link from '../../../CTAs/Link'
import SimpleMenu from '../SimpleMenu'
import styles from './index.module.scss'
import { MenuListGenericI } from './interface'

export default function MenuListGeneric(props: MenuListGenericI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: MenuListGenericI = {
    // ...MenuListGenericD,
    ...props,
    htmlAttr: {
      ...props?.htmlAttr,
      className: `${props.htmlAttr?.className} ${props?.isSticky && styles.root}`,
    },
  }
  let className = styles.link
  if (!props.isLightMode) {
    className += ` ${styles.link_dark} fs4`
  }
  let heading = styles.heading
  if (!props.isLightMode) {
    heading += ` ${styles.heading_dark}`
  }
  const liClass = `${updatedProps.isLightMode ? styles.li : styles.DarkLi}`

  /*    const handleItemClick = (index: number) => {
        RouteToUrl(props?.links[index]?.href)
    } */

  return (
    <SimpleMenu {...updatedProps}>
      <SimpleHeading
        {...updatedProps.heading}
        as='h6'
        colour='cp1'
        fontFamily='fSansBld'
        htmlAttr={{
          className: `${heading} fs4 ${updatedProps?.heading?.htmlAttr?.className}`,
        }}
      />
      <div className={styles.ul}>
        {updatedProps.links?.map((linkProps, i) => (
          <Link
            key={i}
            {...linkProps}
            htmlAttr={{
              className: `${liClass} fs4 ${linkProps?.htmlAttr?.className}`,
            }}
          />
        ))}
      </div>
    </SimpleMenu>
  )
}
