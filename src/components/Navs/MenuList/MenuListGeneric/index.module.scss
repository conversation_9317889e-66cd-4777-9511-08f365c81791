//Default SCSS variables are prepended in next.config.js
.root {
  position: sticky;
  top: 30px;
}

.heading {
  padding: 0 10px;
  width: 100%;
  color: $cn2;
}

.heading_dark {
  @extend .heading;
  color: $cs2;
}

.ul {
  list-style: none;
  padding: 0;
  margin: 10px 0;
  display: flex;
  flex-direction: column;

  .li {
    text-wrap: wrap;
    transition: 1s;
    padding: 10px 10px;
    cursor: pointer;
    text-decoration: none;
    color: $cn2;
    /*
    .link {
      text-decoration: none;
      color: $cn2;
      // font-size: $fs4;
    } */

    &:hover {
      background: $cn5;
      transition: 500ms;
      color: $cp1;
      /*   .link {
        color: $cp1;
      } */
    }
  }

  .DarkLi {
    text-wrap: wrap;
    padding: 10px 10px;
    transition: 1s;
    cursor: pointer;
    text-decoration: none;
    color: $cs2;
    /*  .link_dark {
      text-decoration: none;
      color: $cs2;

    } */

    &:hover {
      background: $cp5;
      color: $cs2;
      /*  .link_dark {
        color: $cs2;
        background: none;
      } */
    }
  }
}
