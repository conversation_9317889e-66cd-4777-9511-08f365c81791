'use client'

import React, { useState } from 'react'
import Kernel from '../../Kernel'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import styles from './index.module.scss'
import { PaginationI } from './interface'

export default function Pagination(props: PaginationI): React.ReactElement {
  const updatedProps: PaginationI = {
    // ...PaginationD,
    isPresent: true,
    isVisible: true,
    ...props,
  }

  const totalPages = Math.ceil(updatedProps.totalCount / updatedProps.pageSize)

  const [currentPage, setCurrentPage] = useState(updatedProps.currentPage)

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
      updatedProps.onPageChange(currentPage + 1)
    }
  }

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
      updatedProps.onPageChange(currentPage - 1)
    }
  }

  const calculatePageRange = () => {
    let startPage, endPage
    if (totalPages <= 5) {
      // Total pages less than 5, show all pages
      startPage = 1
      endPage = totalPages
    } else {
      // More than 5 total pages
      if (currentPage <= 3) {
        // Near the start
        startPage = 1
        endPage = 5
      } else if (currentPage + 2 >= totalPages) {
        // Near the end
        startPage = totalPages - 4
        endPage = totalPages
      } else {
        // Somewhere in the middle
        startPage = currentPage - 2
        endPage = currentPage + 2
      }
    }
    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    )
  }

  const renderPageNumber = (pageNumber) => (
    <button
      key={pageNumber}
      onClick={() => {
        setCurrentPage(pageNumber)
        updatedProps.onPageChange(pageNumber)
      }}
      className={`${styles.pageNumber} ${
        currentPage === pageNumber ? styles.active : ''
      }`}
    >
      {pageNumber}
    </button>
  )

  const previousButtonStyle =
    currentPage === 1 ? styles.disabledButton : styles.enabledButton
  const nextButtonStyle =
    currentPage === totalPages ? styles.disabledButton : styles.enabledButton

  const previousIconStyle =
    currentPage === 1 ? styles.disabledIcon : styles.enabledIcon
  const nextIconStyle =
    currentPage === totalPages ? styles.disabledIcon : styles.enabledIcon

  return (
    <Kernel {...updatedProps}>
      <div className={styles.paginationContainer}>
        <button
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
          className={previousButtonStyle}
        >
          <GenericIcon
            htmlAttr={{ className: previousIconStyle }}
            icon='LeftChevron'
            size='md'
          />
        </button>
        {calculatePageRange().map((pageNumber) => renderPageNumber(pageNumber))}
        <button
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
          className={nextButtonStyle}
        >
          <GenericIcon
            icon='RightChevron'
            size='md'
            htmlAttr={{ className: nextIconStyle }}
          />
        </button>
      </div>
    </Kernel>
  )
}
