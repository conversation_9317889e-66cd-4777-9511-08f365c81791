'use client'
import { useState } from 'react'
import { RouteToUrl } from '../../../../../../globals/utils'
import SimpleAccordionItem from '../../../../../Accordions/SimpleAccordion/@Core/SImpleAccordionItem'
import CTAGroup from '../../../../../CTAs/CTAGroup'
import SimpleCTAs from '../../../../../CTAs/SimpleCTAs'
import LayoutContainer from '../../../../../Containers/LayoutContainer'
import { SimpleHeadingI } from '../../../../../ContentBlocks/Texts/Headings/SimpleHeading/interface'
import Kernel from '../../../../../Kernel'
import GenericIcon from '../../../../../Multimedia/Icons/SysIcon'
import Logo from '../../../../../Multimedia/Images/GenericLogo'
import MenuListMegamenu from '../../../../MenuList/MenuListMegamenu'
import { MenuListMegamenuI } from '../../../../MenuList/MenuListMegamenu/interface'
import { NavigationHamburgerD } from './defaults'
import styles from './index.module.scss'
import { NavigationHamburgerI } from './interface'

export default function NavigationHamburger(props: NavigationHamburgerI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationHamburgerI = {
    ...NavigationHamburgerD,
    ...props,
  }
  const [currentIndex, setCurrentIndex] = useState<number | null>(null)
  const colorClass = updatedProps?.isTranslucent
    ? 'cp1'
    : updatedProps.isLightMode
      ? 'cp1'
      : 'cs2'

  const buttons = updatedProps?.ButtonGroup?.links?.map((link, ind) => {
    const template = ind === 0 ? 'primary' : 'tertiary2'
    return {
      ...link,
      template: template,
    }
  })

  const background = `${updatedProps.isLightMode ? 'bs2' : 'bp1'}`

  const [isHamburger, setIsHamburger] = useState<boolean>(true)

  const menuList = updatedProps?.isTranslucent
    ? styles.menuListLight
    : updatedProps?.isLightMode
      ? styles.menuListLight
      : styles.menuListDark
  const AccordionHeading = updatedProps?.isTranslucent
    ? styles.darkHeading
    : updatedProps.isLightMode
      ? styles.darkHeading
      : styles.lightHeading
  const menuHeadingColor = updatedProps?.isLightMode ? styles.cn2 : styles.cs1
  const OpenAccordionHandler = () => {
    setIsHamburger(false)
  }

  const CloseAccordionHandler = () => {
    setIsHamburger(true)
  }

  //logo
  const logo = updatedProps.isLightMode ? (
    <Logo {...updatedProps.lightLogo} />
  ) : (
    <Logo {...updatedProps.darkLogo} />
  )

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationHamburgerRoot}
        }  ${updatedProps.htmlAttr?.className}`,
      }}
    >
      <LayoutContainer
        htmlAttr={{
          className: `${updatedProps?.isTranslucent ? 'transparent' : background} ${isHamburger ? styles.showHamburger : styles.hideHamburger}`,
        }}
      >
        <nav className={`${styles.menu}`}>
          {logo}
          <GenericIcon
            {...updatedProps.hamburger!}
            htmlAttr={{
              className: updatedProps.isLightMode ? 'cp1' : 'cs2',
              onClick: OpenAccordionHandler,
            }}
          />
        </nav>
      </LayoutContainer>

      <div
        className={`${styles.contentBox} ${updatedProps?.isTranslucent ? 'bs2' : background} ${isHamburger ? styles.hideContentBox : styles.showContentBox}`}
      >
        <div className={`${styles.upperContainer}`}>
          {updatedProps?.isTranslucent ? (
            <Logo {...updatedProps.lightLogo} />
          ) : (
            logo
          )}
          <GenericIcon
            {...updatedProps.closeIcon!}
            htmlAttr={{
              className: `${colorClass} ${styles.closeIcon}`,
              onClick: CloseAccordionHandler,
            }}
          />
        </div>

        {updatedProps.accordionsMain?.map((accordion, i) => {
          const href =
            (accordion?.acrTab?.heading?.internalLink?.slug
              ? '/' + accordion?.acrTab?.heading?.internalLink.slug
              : accordion?.acrTab?.heading?.externalLink) ||
            accordion?.acrTab?.heading?.sectionId ||
            accordion?.acrTab?.heading?.asset?.url
          const target = accordion.acrTab?.heading?.openInNewTab
            ? '_blank'
            : '_self'
          const isChevron = updatedProps?.menuList[i] !== undefined

          //return accordion item
          return (
            <>
              <SimpleAccordionItem
                key={i}
                htmlAttr={{
                  className: `${styles.accordionContainer} `,
                }}
                acrTab={{
                  ...accordion.acrTab,
                  htmlAttr: {
                    ...accordion.acrTab?.htmlAttr,
                    className: `${styles.accordionHeader}`,
                  },
                  heading:
                    href !== undefined ? (
                      <SimpleCTAs
                        href={href}
                        target={target}
                        htmlAttr={{
                          ...accordion.acrTab?.heading?.htmlAttr,
                          className: `${currentIndex === i ? (updatedProps?.isTranslucent ? 'cn3' : updatedProps.isLightMode ? 'cn3' : 'cn4') : AccordionHeading}
                                                 ${currentIndex === i ? styles.ActiveHeading : styles.heading}`,
                        }}
                      >
                        {accordion.acrTab?.heading?.text}
                      </SimpleCTAs>
                    ) : (
                      {
                        ...(accordion.acrTab?.heading as SimpleHeadingI),
                        htmlAttr: {
                          ...accordion.acrTab?.heading?.htmlAttr,
                          className: `${currentIndex === i ? (updatedProps?.isTranslucent ? 'cn3' : updatedProps.isLightMode ? 'cn3' : 'cn4') : AccordionHeading}
                                                     ${currentIndex === i ? styles.ActiveHeading : styles.heading}`,
                        },
                      }
                    ),
                  icon: {
                    ...accordion.acrTab?.icon,
                    htmlAttr: {
                      ...accordion.acrTab?.icon?.htmlAttr,
                      className: `${currentIndex === i ? (updatedProps?.isTranslucent ? 'cn3' : updatedProps.isLightMode ? 'cn3' : 'cn4') : colorClass} ${isChevron === false && styles.hideIcon}`,
                    },
                  },
                }}
                acrTabC={{
                  ...accordion.acrTabC,
                  htmlAttr: {
                    ...accordion.acrTabC?.htmlAttr,
                    className: ` ${colorClass}  ${isChevron === false && styles.hideIcon}`,
                  },
                }}
                isOpen={currentIndex === i}
                index={i}
                isAutoClose={updatedProps.isAutoClose}
                isCloseable={updatedProps.isCloseable}
                currentIndex={currentIndex as number}
                setCurrentIndex={() => {
                  setCurrentIndex((pre) => (pre !== i ? i : null))
                  href ? RouteToUrl(href, target) : undefined
                }}
                isLightMode={updatedProps.isLightMode}
              >
                {currentIndex && updatedProps?.menuList[currentIndex] ? (
                  updatedProps?.menuList[currentIndex].map(
                    (menu: MenuListMegamenuI) => (
                      <MenuListMegamenu
                        {...menu}
                        isLightMode={
                          updatedProps?.isTranslucent
                            ? true
                            : updatedProps?.isLightMode
                        }
                        heading={{
                          ...menu?.heading,
                          htmlAttr: {
                            className:
                              menu?.heading &&
                              `${styles.menuHeading} ${menuHeadingColor}`,
                          },
                        }}
                        htmlAttr={{ className: menuList }}
                      />
                    )
                  )
                ) : (
                  <></>
                )}
              </SimpleAccordionItem>
            </>
          )
        })}
        {updatedProps.ButtonGroup && (
          <CTAGroup
            {...updatedProps.ButtonGroup}
            isLightMode={true}
            links={buttons}
            htmlAttr={{ className: styles.buttons }}
          />
        )}
      </div>
    </Kernel>
  )
}
