import { SimpleAccordionItemI } from '../../../../../Accordions/SimpleAccordion/@Core/SImpleAccordionItem/interface'
import { CTAGroupI } from '../../../../../CTAs/CTAGroup/interface'
import { KernelI } from '../../../../../Kernel'
import { IconI } from '../../../../../Multimedia/Icons/SysIcon/interface'
import { LogoI } from '../../../../../Multimedia/Images/GenericLogo/interface'
import { MenuListMegamenuI } from '../../../../MenuList/MenuListMegamenu/interface'

export interface NavigationHamburgerI extends KernelI {
  lightLogo?: LogoI
  darkLogo?: LogoI
  isTranslucent: boolean
  hamburger?: IconI
  menuList: MenuListMegamenuI[]
  accordionsMain?: SimpleAccordionItemI[]
  Index?: number

  isAutoClose?: boolean

  isCloseable?: boolean

  logo: LogoI

  closeIcon: IconI
  ButtonGroup: CTAGroupI
  //...enlist other
}
