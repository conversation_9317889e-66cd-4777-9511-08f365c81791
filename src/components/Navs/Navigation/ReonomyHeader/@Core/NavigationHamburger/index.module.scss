//Default SCSS variables are prepended in next.config.js
.NavigationHamburgerRoot {
  width: 100%;

  .menu {
    height: 100px;
    overflow: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.heading {
  font-family: $fSansReg;
  font-size: $fs4;
  // font-weight: 400;
  cursor: pointer;

  &:hover {
    font-family: $fSansBld;
    // font-weight: 700;
  }
}

.ctas {
  min-width: auto;
}

.closeIcon {
  height: 30px;
  width: 30px;
}

.hideIcon {
  display: none;
}

.paddingBottom {
  padding-bottom: 40px;
}

.contentBox {
  border-radius: 5px;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 100px 100px 0px;
  // min-height: 80vh;
  padding: 0 25px 50px 25px;
  margin: 15px 20px;
  width: calc(100% - 40px);
  position: absolute;
  top: 0;
}

.accordionContainer {
  cursor: pointer;

  .accordionHeader {
    padding: 20px 0;
    gap: 10px;
    // use this for style accordion tab header
  }

  .accordionHeader {
    .heading {
      font-family: $fSansBld;
      text-decoration: none;
      font-size: $fs4;
    }

    .ActiveHeading {
      text-decoration: none;
      font-family: $fSansBld;
      font-size: $fs4;
    }

    .darkHeading {
      color: $cp1;
      text-decoration: none;
      font-size: $fs4;
    }

    .lightHeading {
      color: $cs2;
      text-decoration: none;
      font-size: $fs4;
    }
  }
}

.upperContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  min-width: $minWidth;
}

.buttons {
  margin-top: 50px;
}

.hideContentBox {
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease,
    transform 0.3s ease;
}

.showContentBox {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.hideHamburger {
  opacity: 0;
  visibility: hidden;
}

.showHamburger {
  opacity: 1;
  visibility: visible;
}

.menuListLight {
  margin-bottom: 10px;

  a {
    text-align: left !important;

    &:hover {
      color: $cp2 !important;
      background: transparent !important;
    }
  }

  div {
    margin: 0;
  }
}

.menuListDark {
  margin-bottom: 10px;

  a {
    text-align: left !important;

    &:hover {
      color: $cs1 !important;
      background: transparent !important;
    }
  }
}

.menuHeading {
  text-transform: uppercase;
  font-size: $fs2;
  font-family: $fSansBld;
  margin-top: 20px;
}

.cn2 {
  color: $cn2;
}

.cs1 {
  color: $cs1;
}
