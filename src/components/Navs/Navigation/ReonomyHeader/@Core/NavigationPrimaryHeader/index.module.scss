//Default SCSS variables are prepended in next.config.js
.NavigationPrimaryRoot {
  width: 100%;
  z-index: 4;
  position: relative;

  .menu {
    height: 100px;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5px;

    .left {
      display: flex;
      align-items: center;
      gap: 60px;

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;

        .borderBottom {
          border-bottom: 20px solid $cs2;
        }

        .borderBottomDark {
          border-bottom: 20px solid $cp1;
        }

        .triangle {
          width: 0;
          height: 0;
          border-right: 10px solid transparent;
          border-left: 10px solid transparent;
          transform: translateX(-50%);
          position: absolute;
          left: calc(50% - 16px);
          top: -15px;
        }

        :global .templateSingleDropdown {
          opacity: 0;
          visibility: hidden;
          position: absolute;
          top: 95px;
          left: 50%;
          min-width: max-content;
          box-shadow: rgba(17, 12, 46, 0.15) 0px 100px 100px 0px;
          transform: translate(-50%, -10px);
          transition:
            opacity 0.3s ease,
            visibility 0.3s ease,
            transform 0.3s ease;
          //padding-top: 20px;
          border-radius: 4px;

          & ul {
            flex-direction: column;
            padding-bottom: 20px;
          }
        }

        li {
          float: left;
          position: relative;

          &:hover :global .templateSingleDropdown {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, 0);
          }

          a {
            text-decoration: none;
            text-align: center;

            p {
              padding-right: 32px;
            }
          }

          p {
            padding-right: 32px;
          }

          &:hover {
            .hoverState {
              color: $cn3;
            }

            .darkHoverState {
              color: $cn4;
            }
          }
        }
      }

      @media screen and (max-width: 1080px) {
        gap: 40px;

        ul {
          .triangle {
            left: calc(50% - 10px);
          }

          li {
            a {
              p {
                padding-right: 20px;
              }
            }

            p {
              padding-right: 20px;
            }
          }
        }
      }

      @media screen and (max-width: 900px) {
        ul {
          .triangle {
            left: calc(50% - 5px);
          }

          li {
            a {
              p {
                padding-right: 10px;
              }
            }

            p {
              padding-right: 10px;
            }
          }
        }
      }

      @media screen and (max-width: 810px) {
        gap: 10px;
      }
    }

    .buttons {
      a {
        font-size: clamp(0.9375rem, 0.5625rem + 0.4688vw, 1.125rem);
      }

      @media screen and (max-width: 1080px) {
        a {
          min-width: fit-content;
        }
      }

      @media screen and (max-width: 900px) {
        gap: 10px;

        a {
          font-size: clamp(0.75rem, 0.2013rem + 1.1976vw, 0.875rem);
          height: 30px;
        }
      }

      @media screen and (max-width: 810px) {
        gap: 5px;

        a {
          svg {
            width: 6px;
          }
        }
      }
    }
  }
}

.heading {
  font-family: $fSansBld;
  //font-size: $fs4;
  font-size: clamp(0.9375rem, 0.5625rem + 0.4688vw, 1.125rem);
  padding: 40px 0;
  cursor: pointer;

  @media screen and (max-width: 900px) {
    font-size: clamp(0.75rem, 0.2013rem + 1.1976vw, 0.875rem);
  }
}

.navBar {
  position: absolute;
  width: 100%;
}

.menuListLight {
  a {
    text-align: left !important;

    &:hover {
      color: $cp2 !important;
      background: transparent !important;
    }
  }
}

.menuListDark {
  a {
    text-align: left !important;

    &:hover {
      color: $cs1 !important;
      background: transparent !important;
    }
  }
}

.menuListDiv {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 800px;
  margin: 10px;

  @media screen and (max-width: $mScreenSize) {
    max-width: 500px;
  }

  .menuHeading {
    margin-top: 20px;
    text-transform: uppercase;
    font-size: $fs2;
    font-family: $fSansBld;
  }
}

.cn2 {
  color: $cn2;
}

.cs1 {
  color: $cs1;
}
