'use client'
import CTAGroup from '../../../../../CTAs/CTAGroup'
import SimpleCTAs from '../../../../../CTAs/SimpleCTAs'
import LayoutContainer from '../../../../../Containers/LayoutContainer'
import SimpleParagraph from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../../../Kernel'
import Logo from '../../../../../Multimedia/Images/GenericLogo'
import MenuListMegamenu from '../../../../MenuList/MenuListMegamenu'
import { MenuListMegamenuI } from '../../../../MenuList/MenuListMegamenu/interface'
import { NavigationPrimaryHeaderD } from './defaults'
import styles from './index.module.scss'
import { NavigationPrimaryHeaderI } from './interface'

export default function NavigationPrimaryHeader(
  props: NavigationPrimaryHeaderI
) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationPrimaryHeaderI = {
    ...NavigationPrimaryHeaderD,
    ...props,
  }
  const tertiary = updatedProps?.isTranslucent
    ? updatedProps?.isLightBgImage
      ? 'tertiary2'
      : 'tertiary3'
    : updatedProps?.isLightMode
      ? 'tertiary2'
      : 'tertiary3'
  const buttons = updatedProps?.ButtonGroup?.links?.map((link, ind) => {
    const template = ind === 0 ? 'primary' : `${tertiary}`
    return {
      ...link,
      template: template,
    }
  })

  const colorClass = updatedProps.isLightMode ? 'cp1' : 'cs2'
  const background = updatedProps.isLightMode ? 'bs2' : 'bp1'
  const menuHeadingColor = updatedProps?.isLightMode ? styles.cn2 : styles.cs1

  let hoverColor = updatedProps.isLightMode
    ? styles.hoverState
    : styles.darkHoverState

  const borderBottom = updatedProps?.isTranslucent
    ? styles.borderBottom
    : updatedProps?.isLightMode
      ? styles.borderBottom
      : styles.borderBottomDark
  const menuList = updatedProps?.isTranslucent
    ? styles.menuListLight
    : updatedProps?.isLightMode
      ? styles.menuListLight
      : styles.menuListDark

  //SingleDropdown menu
  const getSingleDropdownSubmenu = (updatedProps, i) => {
    return (
      updatedProps.menuList[i] && (
        <div
          className={`templateSingleDropdown ${updatedProps?.isTranslucent ? 'bs2' : background}`}
        >
          <div className={`${styles.triangle} ${borderBottom}`}></div>
          <div className={styles.menuListDiv}>
            {' '}
            {updatedProps?.menuList[i].map((menu: MenuListMegamenuI) => (
              <MenuListMegamenu
                {...menu}
                isLightMode={
                  updatedProps?.isTranslucent ? true : updatedProps?.isLightMode
                }
                heading={{
                  ...menu?.heading,
                  htmlAttr: {
                    className:
                      menu?.heading &&
                      `${styles.menuHeading} ${menuHeadingColor}`,
                  },
                }}
                htmlAttr={{ className: menuList }}
              />
            ))}
          </div>
        </div>
      )
    )
  }

  //logo
  const logo = updatedProps.isLightMode ? (
    <Logo {...updatedProps.lightLogo} />
  ) : (
    <Logo {...updatedProps.darkLogo} />
  )

  return (
    <>
      <Kernel
        {...updatedProps}
        htmlAttr={{
          className: `${styles.NavigationPrimaryRoot} ${updatedProps?.isTranslucent ? 'transparent' : background} ${updatedProps.htmlAttr?.className} header`,
        }}
      >
        <LayoutContainer>
          <nav className={`${styles.menu}`}>
            <div className={styles.left}>
              {logo}
              <ul>
                {updatedProps?.links?.map((data, i) => {
                  return (
                    <li key={i}>
                      {data.cta?.href ? (
                        <SimpleCTAs
                          {...data.cta!}
                          htmlAttr={{
                            ...data?.cta?.htmlAttr,
                            className: `${styles.ctas} `,
                          }}
                        >
                          <SimpleParagraph
                            {...data.text}
                            htmlAttr={{
                              className: `${hoverColor} ${
                                colorClass
                              } ${styles.heading}`,
                            }}
                          />
                        </SimpleCTAs>
                      ) : (
                        <SimpleParagraph
                          {...data.text}
                          htmlAttr={{
                            className: `${hoverColor} ${
                              colorClass
                            } ${styles.heading}`,
                          }}
                        />
                      )}
                      {getSingleDropdownSubmenu(updatedProps, i)}
                    </li>
                  )
                })}
              </ul>
            </div>
            <CTAGroup
              {...updatedProps?.ButtonGroup}
              htmlAttr={{ className: styles.buttons }}
              links={buttons}
              isLightMode={updatedProps?.isLightBgImage ?? true}
            />
          </nav>
        </LayoutContainer>
      </Kernel>
    </>
  )
}
