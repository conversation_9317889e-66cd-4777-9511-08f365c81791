'use client'
import { useEffect } from 'react'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import { setIsTranslucent } from '../../../../redux/slices/appSlice'
import { useAppDispatch } from '../../../../redux/store'
import Kernel from '../../../Kernel'
import NavigationHamburger from './@Core/NavigationHamburger'
import NavigationPrimaryHeader from './@Core/NavigationPrimaryHeader'
import { ReonomyHeaderD } from './defaults'
import styles from './index.module.scss'
import { ReonomyHeaderI } from './interface'

export default function ReonomyHeader(props: ReonomyHeaderI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: ReonomyHeaderI = {
    ...ReonomyHeaderD,
    ...props,
  }
  const dispatch = useAppDispatch()

  const isLightMode = updatedProps?.isNavLightMode ?? updatedProps.isLightMode
  let translucentMarginB = updatedProps.isTranslucent ? styles.mb100 : ''

  useEffect(() => {
    dispatch(setIsTranslucent(updatedProps?.isTranslucent))
  }, [])

  const { size } = useWindowSize()

  return (
    <>
      <Kernel
        {...updatedProps}
        locale={props.locale}
        isFullXBleed
        isLightMode={isLightMode}
        htmlAttr={{
          className: `${styles.NavigationRoot} ${translucentMarginB}
                        ${updatedProps.htmlAttr?.className}`,
        }}
      >
        {size === 'small' ? (
          <NavigationHamburger
            {...updatedProps?.NavigationSmall}
            isLightMode={isLightMode}
            isTranslucent={updatedProps.isTranslucent}
          />
        ) : (
          <NavigationPrimaryHeader
            {...updatedProps.Navigationlarge!}
            isLightMode={isLightMode}
            isTranslucent={updatedProps.isTranslucent}
            isLightBgImage={updatedProps?.isLightBgImage}
          />
        )}
      </Kernel>
    </>
  )
}
