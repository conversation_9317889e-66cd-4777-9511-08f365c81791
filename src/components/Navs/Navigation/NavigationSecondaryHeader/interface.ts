import { SimpleButtonWIconI } from '../../../../components/CTAs/SimpleButtonWIcon/interface'
import { SimpleCTAsI } from '../../../../components/CTAs/SimpleCTAs/interface'
import { ScrollBoxI } from '../../../Containers/ScrollBox/interface'
import { SimpleParagraphI } from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph/interface'
import { KernelI } from '../../../Kernel'
import { MenuListGenericI } from '../../MenuList/MenuListGeneric/interface'

export interface NavigationSecondaryHeaderI extends KernelI {
  //...enlist other
  variant: 'Sticky' | 'Non-Sticky' | 'Dropdown'
  elements: {
    link: SimpleParagraphI
    cta: SimpleCTAsI
    menulist: MenuListGenericI
  }[]
  contactUsBtn: SimpleButtonWIconI
  cta6: SimpleCTAsI
  scrollbar: ScrollBoxI
}
