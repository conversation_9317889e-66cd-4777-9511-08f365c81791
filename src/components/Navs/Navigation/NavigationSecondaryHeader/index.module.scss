.invisible {
  display: none !important;
}

.scrollBox {
  box-shadow: none;
  // width: "1450px";
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;

  &::-webkit-scrollbar {
    height: 0px;
  }
}

.stickyDiv {
  position: sticky;
  top: 0px;
  z-index: 3;
}

.menuRoot {
  width: 100%;
  // background-color: white;
  position: relative;
  margin: auto;
  // padding: 10px 0;
}

.menuInnerRoot {
  // background-color: lightblue;
  width: 100%;
  display: flex;
  // gap: 30px;
  align-items: center;
  padding: 0 30px 0 0;
  overflow-x: auto;

  // touch-action: none; /* Add also this */
  &::-webkit-scrollbar {
    height: 0px;
  }
}

.menuItem {
  // background-color:yellow;
  // min-width:100px;
  padding: 40px 80px 40px 0;
  font-family: $fSansBld;

  @media (max-width: $smScreenSize) {
    padding: 40px 60px 40px 0;
  }
}

.menuSubItems {
  background-color: white;
  box-shadow:
    rgba(50, 50, 93, 0.25) 0px 50px 50px -20px,
    rgba(0, 0, 0, 0.3) 0px 30px 60px -30px;
  width: fit-content;
  min-width: 200px;
  height: max-content;
  display: none;
  position: absolute;
  // padding-top: 40px;
  z-index: 2;
  margin-left: -20px;
  touch-action: pan-y;
  /* Disable horizontal panning */
  overflow-x: hidden;
  font-family: $fSansMed;
}

.menuList ul {
  margin: 0 !important;
}

.menuList ul li {
  padding: 10px 20px !important;
}

.menuSubItems p {
  padding: 10px 20px;
}

.menuItem:hover .menuSubItems {
  display: inherit;
}

.NavigationRoot {
  display: flex;
  flex-direction: column;
  padding: 30px 0;
  background-color: $cs2;

  .menu {
    width: 100%;
    //height: 120px;
    /*    max-width: 721px;
    min-width: 573px; */
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      gap: 80px;

      ul.divContainer {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        gap: 48px;

        li {
          float: left;

          a {
            text-decoration: none;
            display: block;
            // text-align: center;
          }
        }

        @media screen and (max-width: $mScreenSize) {
          gap: 25px;
        }
      }

      @media screen and (max-width: $mScreenSize) {
        gap: 30px;
      }
    }
  }
}

.liEventSticky {
  &:hover {
    .hoverClass {
      color: $cp1;
      //font-weight: 700;
    }
  }
}

.liEventNon-Sticky {
  &:hover {
    .hoverClass {
      color: $cp2;
    }
  }
}

.ctas {
  min-width: auto;
  text-decoration: none !important;
}

.wrapper {
  position: relative;

  .menuItem {
    display: none;
    top: 0px;
    left: -50px;
    position: absolute;
    background-color: $cs2;
    padding: 20px;
    margin-top: 70px;
    min-width: 240px;
    z-index: 1;
    // display: flex;
    // flex-direction: column;
    //align-items: center;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
  }

  li {
    a {
      text-decoration: none;
      display: block;
      padding-left: 20px;
      //text-align: center;
    }
  }
}

.scrollBox {
  box-shadow: none;
  // width: "1450px";
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    right: 0;
    z-index: 3;
    width: 50px;
    height: 100%;
    background: linear-gradient(-90deg, #fff 0%, rgba(255, 255, 255, 0) 75%);
  }

  &::before {
    background: none;
    z-index: 0;
  }

  &::-webkit-scrollbar {
    height: 0px;
  }
}

.showcaseContainer {
  //margin-bottom: 113px;
  box-shadow: none;

  .content {
    padding: 0;
    overflow-x: unset;
    overflow-y: unset;

    &::-webkit-scrollbar {
      height: 0px;
    }
  }

  .logoContainer {
    display: flex;
    box-shadow: none;
    justify-content: flex-start;
    /* Start at the left edge */
    width: 100%;

    // overflow: hidden; /* Hide the overflow to create the scroll effect */
    // animation: scrollAnimation 20s linear infinite; /* Adjust the duration as needed */
    .scrollBox {
      &::-webkit-scrollbar {
        height: 0px;
      }
    }
  }
}

.stickyDiv {
  position: sticky;
  top: 0px;
}

.buttoncta {
  // padding-left: 40px;
  z-index: 3;
}

@media screen and (max-width: $mScreenSize) {
  .menuRoot {
    padding: 0;
  }

  .buttoncta {
    z-index: 0;
  }

  .menuSubItems {
    // padding-top: 20px;
    // margin-left: -20px;
  }

  .menuInnerRoot:first-child {
    padding-left: 20px;
  }
}

.headingDropdown {
  font-family: $fSansBld;
  font-size: $fs4;
  padding: 5px 0;
  color: $cp1;
  white-space: nowrap;
}

.headingSticky {
  font-family: $fSansBld;
  font-size: $fs4;
  color: $cn3;
  white-space: nowrap;
}

.headingNon-Sticky {
  font-family: $fSansBld;
  font-size: $fs4;
  //font-weight: 400;
  padding: 5px 0;
  color: $cp1;
  white-space: nowrap;
}

@media screen and (max-width: $smScreenSize) {
  .menuSubItems {
    margin-left: -30px;
  }
}

.activesticky {
  color: $cp1 !important;
}

.activenonsticky {
  //color: $cp2 !important;
}

.fixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  background-color: white;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 50px;
  // transition: all 0.5s ease;
}

.blur {
  &::before {
    content: '';
    position: absolute;
    left: 0;
    width: 50px;
    height: 100%;
    background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 75%);
    z-index: 2;
  }
}
