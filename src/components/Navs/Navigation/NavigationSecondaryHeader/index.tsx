'use client'
import { useEffect, useRef, useState } from 'react'
import SimpleButtonWIcon from '../../../../components/CTAs/SimpleButtonWIcon'
import { handleElementScrollIntoView } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import SimpleCTAs from '../../../CTAs/SimpleCTAs'
import LayoutContainer from '../../../Containers/LayoutContainer'
import ScrollBox from '../../../Containers/ScrollBox'
import SimpleParagraph from '../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../Kernel'
import MenuListGeneric from '../../MenuList/MenuListGeneric'
import { NavigationSecondaryHeaderD } from './defaults'
import styles from './index.module.scss'
import { NavigationSecondaryHeaderI } from './interface'

interface DropdownState {
  [key: string]: boolean
}

export default function NavigationSecondaryHeader(
  props: NavigationSecondaryHeaderI
) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationSecondaryHeaderI = {
    ...NavigationSecondaryHeaderD,
    ...props,
  }

  let classSticky
  const [isMenuSubItemsVisible, setMenuSubItemsVisible] = useState(false)
  if (updatedProps.variant === 'Sticky') {
    classSticky = styles.stickyDiv
  } /*  else if (updatedProps.variant === 'Fixed') {
    position = 'fixed'
  } else {
    position = 'fixed'
  } */
  const [activeMenuItemIndex, setActiveMenuItemIndex] = useState()
  const [menuSubItemsPosition, setMenuSubItemsPosition] = useState({
    top: 0,
    left: 0,
  })
  const { width, size } = useWindowSize()
  const [menuSubItemsVisibilityClass, setMenuSubItemsVisibilityClass] =
    useState('')
  const isDropdownVariant = updatedProps.variant === 'Dropdown'
  const isSmallWindow = size === 'small'
  const sectionRef = useRef(null)

  // Define state to track if scrolling has started horizontally within the section
  const [isSectionScrolling, setIsSectionScrolling] = useState(false)

  // Function to handle scroll events within the section
  const handleSectionScroll = () => {
    // Check if scrolling horizontally within the section
    if (!isSectionScrolling && sectionRef?.current?.scrollLeft !== 0) {
      setIsSectionScrolling(true) // Set scrolling state to true
    } else if (isSectionScrolling && sectionRef?.current?.scrollLeft === 0) {
      setIsSectionScrolling(false) // Set scrolling state to false when scroll position is back to initial
    }
  }

  // function to handle menu items click
  const handleMenuItemClick = (event, index) => {
    // Get the boundingClientRect of the clicked menuItem
    const menuItemRect = event.currentTarget.getBoundingClientRect()
    setActiveMenuItemIndex(index)
    // Set the position of menuSubItems based on menuItem's position
    setMenuSubItemsPosition({
      top: menuItemRect.bottom,
      left: menuItemRect.left,
    })
    setMenuSubItemsVisible(true)
    // Add console logs for debugging
    // l('Clicked menuItem index:', index)
    // l('MenuItem position:', menuItemRect)
  }

  // function to control menuSubItems visibility
  const handleTouchMove = () => {
    setMenuSubItemsVisible(false)
    // l('Touching outside menu items and menuSubItems. Closing menuSubItems.')
  }

  const handleDocumentClick = (event) => {
    // Check if the click occurred outside the menu items and menuSubItems
    const menuItems = document.querySelectorAll(`.${styles.menuItem}`)
    const menuSubItems = document.querySelector(`.${styles.menuSubItems}`)

    if (
      !Array.from(menuItems).some((menuItem) =>
        menuItem.contains(event.target)
      ) &&
      menuSubItems &&
      !menuSubItems.contains(event.target)
    ) {
      // if the click occurred outside the menu items and menuSubItems, close the menuSubItems
      setMenuSubItemsVisible(false)
      // l('Clicked outside menu items and menuSubItems. Closing menuSubItems.')
    }
  }

  //scrollspy feature start here
  // Effect to update active state based on URL changes and scroll events
  useEffect(() => {
    let prevId = ''

    const handleScrollAndUrlChange = () => {
      // Get the current hash ID from the URL
      const currentHashId = window.location.hash.substring(1)

      // Iterate through elements to find a match with the current hash ID
      if (currentHashId && prevId !== currentHashId) {
        updatedProps.elements.forEach((ele, index) => {
          if (ele.cta && ele.cta.href === `#${currentHashId}`) {
            setActiveMenuItemIndex(index)
            prevId = currentHashId
          }
        })
      }
    }

    // Attach the event listeners for scroll and URL changes
    window.addEventListener('scroll', handleScrollAndUrlChange)
    window.addEventListener('hashchange', handleScrollAndUrlChange)

    // Call the handleScrollAndUrlChange on component mount to check the initial URL
    handleScrollAndUrlChange()
    // Cleanup the event listeners on component unmount
    return () => {
      window.removeEventListener('scroll', handleScrollAndUrlChange)
      window.removeEventListener('hashchange', handleScrollAndUrlChange)
    }
  }, [updatedProps.elements.map((ele) => ele.cta?.href).join(',')]) // Listen for changes in props.links
  // ScrollSpy Feature ends here

  useEffect(() => {
    // Attach click and touchmove event listeners to the document when menuSubItems is visible
    if (isMenuSubItemsVisible) {
      document.addEventListener('click', handleDocumentClick)
      document.addEventListener('touchmove', handleTouchMove)
      setMenuSubItemsVisibilityClass('') // Remove the "invisible" class
      // l('Added click and touchmove event listeners to the document.')
    } else {
      setMenuSubItemsVisibilityClass(styles.invisible) // Add the "invisible" class
    }

    // Cleanup the event listeners when the component unmounts or when menuSubItems is not visible
    return () => {
      document.removeEventListener('click', handleDocumentClick)
      document.removeEventListener('touchmove', handleTouchMove)
      // l('Removed click and touchmove event listeners from the document.')
    }
  }, [isMenuSubItemsVisible])

  const menuRootRef = useRef(null)
  const [isFixed, setIsFixed] = useState(false) // state for sticky navigation bar
  const [menuRootTop, setMenuRootTop] = useState(0) // state to store the top position of menuRoot
  const [menuRootBottom, setMenuRootBottom] = useState(0) // state to store the bottom position of menuRoot

  // Effect to update the position of menuRoot on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (menuRootRef.current) {
        const menuRootRect = menuRootRef.current.getBoundingClientRect()

        setMenuRootTop(menuRootRect.top)
      }
    }
    // Attach the scroll event listener

    window.addEventListener('scroll', handleScroll)

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, []) // Empty dependency array means this effect runs once on mount

  useEffect(() => {
    setTimeout(() => {
      const hash = window.location.hash
      if (hash) {
        const el = document.querySelector(hash)
        if (el) {
          window.scrollTo(0, el.getBoundingClientRect().top - 100)
        }
      }
    }, 1000)
  }, [])

  const theNav = (
    <>
      <div
        className={`${styles.menuRoot} ${
          isFixed && updatedProps.variant === 'Sticky'
            ? styles.fixedContainer
            : ''
        }`}
      >
        <section
          className={`${styles.menuInnerRoot} ${
            updatedProps.variant === 'Dropdown' ? 'secNav' : ''
          }`}
          ref={sectionRef}
          onScroll={handleSectionScroll}
        >
          {updatedProps?.elements &&
            updatedProps.elements.map((ele, ind) => {
              const isActive = ind === activeMenuItemIndex
              const activeClass = isActive
                ? updatedProps.variant === 'Sticky'
                  ? styles.activesticky
                  : styles.activenonsticky
                : ''
              return (
                <div
                  key={ind}
                  className={`${styles.menuItem} ${
                    styles[`liEvent${updatedProps.variant}`]
                  } ${
                    updatedProps.variant === 'Dropdown' ? 'secNavChild' : ''
                  }`}
                  onClick={(event) => {
                    handleMenuItemClick(event, ind)
                    if (
                      updatedProps?.elements[ind]?.cta?.href?.startsWith('#')
                    ) {
                      handleElementScrollIntoView(
                        updatedProps?.elements[ind]?.cta?.href ?? '#',
                        120,
                        120
                      )
                    }
                  }}
                >
                  {isDropdownVariant ? (
                    <SimpleParagraph
                      {...ele.link}
                      isLightMode={updatedProps.isLightMode}
                      htmlAttr={{
                        className: `${styles.hoverClass} ${
                          styles[`heading${updatedProps.variant}`]
                        }`,
                      }}
                    />
                  ) : (
                    <SimpleCTAs
                      {...ele.cta}
                      htmlAttr={{
                        className: styles.ctas,
                        onClick: updatedProps?.elements[
                          ind
                        ]?.cta?.href?.startsWith('#')
                          ? (e) => {
                              e.preventDefault()
                              // handleElementScrollIntoView(
                              //   updatedProps?.elements[ind]?.cta?.href ?? '#',
                              //   120,
                              //   120
                              // )
                            }
                          : null,
                      }}
                    >
                      <SimpleParagraph
                        {...ele.link}
                        isLightMode={updatedProps.isLightMode}
                        htmlAttr={{
                          className: ` ${styles.hoverClass} ${
                            isActive ? activeClass : ''
                          } ${styles[`heading${updatedProps.variant}`]}`,
                        }}
                      />
                    </SimpleCTAs>
                  )}

                  {updatedProps.variant === 'Dropdown' && (
                    <div className={styles.emptyDiv}></div>
                  )}
                  {isDropdownVariant ? (
                    isSmallWindow ? (
                      <div
                        className={`${styles.menuSubItems} ${menuSubItemsVisibilityClass}`}
                        style={{
                          top: 98,
                          left: menuSubItemsPosition.left - 40,
                        }}
                      >
                        <MenuListGeneric
                          {...ele.menulist}
                          htmlAttr={{ className: styles.menuList }}
                          isLightMode={updatedProps.isLightMode}
                        />
                      </div>
                    ) : (
                      <div
                        className={`${styles.menuSubItems}`}
                        style={{ top: 98 }}
                      >
                        <MenuListGeneric
                          {...ele.menulist}
                          htmlAttr={{ className: styles.menuList }}
                          isLightMode={updatedProps.isLightMode}
                        />
                      </div>
                    )
                  ) : (
                    <div
                      className={`${styles.menuSubItems} ${styles.invisible}`}
                      style={{ top: 98 }}
                    >
                      <MenuListGeneric
                        {...ele.menulist}
                        htmlAttr={{ className: styles.menuList }}
                        isLightMode={updatedProps.isLightMode}
                      />
                    </div>
                  )}
                </div>
              )
            })}
          {props.contactUsBtn && (
            <div className={styles.buttoncta}>
              <SimpleButtonWIcon {...updatedProps.contactUsBtn} />
            </div>
          )}
        </section>
      </div>
      {updatedProps.children}
    </>
  )

  const scrollbox = (
    <ScrollBox
      htmlAttr={{
        className: `${styles.scrollBox} ${
          isSectionScrolling ? styles.blur : ''
        }`,
      }}
      content={{
        htmlAttr: {
          className: styles.content,
        },
      }}
    >
      <div className={styles.logoContainer}>{theNav}</div>
    </ScrollBox>
  )

  return (
    <>
      {isFixed && updatedProps.variant === 'Sticky' && (
        <div style={{ height: '100px' }}></div>
      )}
      <LayoutContainer
        isFullXBleed
        htmlAttr={{
          style: {
            top: -1,
            position: menuRootTop < 0 || isFixed ? 'sticky' : 'static',
            zIndex: 10,
            backgroundColor: 'white',
          },
          className: `shadow3`,
          ref: menuRootRef,
        }}
      >
        <LayoutContainer isFullXBleed={size === 'small'}>
          <Kernel
            {...updatedProps}
            isFullXBleed
            htmlAttr={{
              className: `${updatedProps.htmlAttr?.className} ${classSticky} `,
            }}
          >
            {/* <LayoutContainer  */}
            {/* {size !== 'large' ? ( */}
            <div className={styles.showcaseContainer}>{scrollbox}</div>
            {/* ) : ( */}
            {/*     theNav */}
            {/* )} */}
          </Kernel>
        </LayoutContainer>
      </LayoutContainer>
    </>
  )
}
