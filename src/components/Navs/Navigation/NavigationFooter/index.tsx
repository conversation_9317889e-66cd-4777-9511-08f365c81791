'use client'
import { useEffect } from 'react'
import { setLocalStorageItem } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import Kernel from '../../../Kernel'
import styles from './index.module.scss'
import { NavigationFooterI } from './interface'
import NavigationFooterHamburger from './NavigationFooterHamburger'
import NavigationFooterMain from './NavigationFooterMain'

export default function NavigationFooter(props: NavigationFooterI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationFooterI = {
    // ...NavigationFooterD,
    isPresent: true,
    isVisible: true,
    ...props,
  }

  setLocalStorageItem('locale', props?.locale)

  let background = updatedProps.isLightMode ? 'bs2' : 'bs4'

  const { size } = useWindowSize()

  useEffect(() => {
    document
      .getElementsByTagName('html')[0]
      .setAttribute('lang', props?.locale?.split('-')[0] || 'en')
  }, [])

  return (
    <Kernel
      {...updatedProps}
      isFullXBleed
      as={'footer'}
      htmlAttr={{
        className: `${styles.NavigationRoot} ${background} ${updatedProps.htmlAttr?.className}`,
        id: 'footer',
      }}
    >
      {size === 'small' ? (
        <NavigationFooterHamburger
          {...updatedProps.NavigationSmall}
          isLightMode={updatedProps.isLightMode}
        />
      ) : (
        <NavigationFooterMain
          {...updatedProps.Navigationlarge}
          isLightMode={updatedProps.isLightMode}
          locale={props.locale}
        />
      )}
    </Kernel>
  )
}