import LayoutContainer from '../../../../Containers/LayoutContainer'
import Richtext from '../../../../ContentBlocks/Richtext'
import SimpleParagraph from '../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../../Kernel'
import GenericIcon from '../../../../Multimedia/Icons/SysIcon'
import Logo from '../../../../Multimedia/Images/GenericLogo'
import MenuListGeneric from '../../../MenuList/MenuListGeneric'
import SimpleCTAs from './../../../../CTAs/SimpleCTAs/index'
import styles from './index.module.scss'
import { NavigationFooterMainI } from './interface'

export default function NavigationFooterMain(props: NavigationFooterMainI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationFooterMainI = {
    isPresent: true,
    isVisible: true,
    // ...NavigationFooterMainD,
    ...props,
  }

  const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs2'}`
  const logoColor = `${
    updatedProps.isLightMode ? styles.logosBlack : styles.logosWhite
  }`
  const brandLogo = updatedProps.isLightMode
    ? updatedProps.lightLogo
    : updatedProps.darkLogo
  const dividerColor = `${
    updatedProps.isLightMode ? styles.dividerBlack : styles.dividerWhite
  }`
  const background = updatedProps.isLightMode ? 'bs2' : 'bs4'

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationPrimaryRoot} ${background} ${updatedProps.htmlAttr?.className}`,
      }}
    >
      <LayoutContainer>
        <nav className={`${styles.menu}`}>
          <div className={styles.left}>
            {updatedProps?.para?.data?.content?.at(0)?.content?.at(0)?.text && (
              <Logo {...brandLogo!} />
            )}
            <div className={styles.iconRoot}>
              {updatedProps?.para?.data?.content?.at(0)?.content?.at(0)
                ?.text ? (
                <Richtext
                  {...updatedProps.para}
                  htmlAttr={{ className: `${colorClass} ${styles.heading1} ` }}
                />
              ) : (
                <Logo {...brandLogo!} />
              )}
              <div className={styles.iconContainer}>
                {updatedProps.socialIcons?.map((iconProps, i) => (
                  <div key={i} className={styles.icons}>
                    <SimpleCTAs
                      {...iconProps.cta!}
                      htmlAttr={{ className: styles.iconCtas }}
                    >
                      <GenericIcon {...iconProps.icon} size='md' />
                    </SimpleCTAs>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* menu list */}
          <div className={styles.menuList}>
            {updatedProps.menuList?.map((menu, i) => (
              <MenuListGeneric
                {...menu}
                key={i}
                heading={{
                  ...menu?.heading,
                  as: 'h6',
                  colour: 'cp1',
                  fontFamily: 'fSansBld',
                  htmlAttr: { className: styles.fontSize },
                }}
                links={menu?.links?.map((link, i) => ({
                  key: i,
                  ...link,
                  htmlAttr: { className: styles.fontSize },
                }))}
                isLightMode={updatedProps.isLightMode}
              />
            ))}
          </div>
        </nav>
        <div className={`${dividerColor} ${colorClass}`} />{' '}
        <nav className={styles.right}>
          <ul>
            {updatedProps.links?.map((links, i) => (
              <li key={i}>
                <SimpleCTAs
                  {...links.cta!}
                  htmlAttr={{
                    className: styles.ctas,
                  }}
                >
                  <SimpleParagraph
                    {...links.text}
                    htmlAttr={{
                      className: `${colorClass} ${styles.heading} `,
                    }}
                  />
                </SimpleCTAs>
              </li>
            ))}
          </ul>
        </nav>
        <a></a>
      </LayoutContainer>

      <div className={styles.altusFooter}>
        <LayoutContainer>
          <SimpleParagraph
            {...updatedProps.footerPara}
            htmlAttr={{ className: ` ${styles.headings} ` }}
          />
        </LayoutContainer>
      </div>
    </Kernel>
  )
}
