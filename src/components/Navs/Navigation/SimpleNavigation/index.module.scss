//Default SCSS variables are prepended in next.config.js
.NavigationRoot {
  width: 100%;
  z-index: 5;
  position: relative;
  // transition: all .4s ease;
  // background-color: white;
}

.blurBg {
  position: fixed;
  width: 100%;
  height: 100vh;
  backdrop-filter: blur(1rem);
  z-index: 3;
  // top: 80px;
}

.immersive {
  width: 100vw;
  height: 100vh;
  display: block;
  background-color: #000b3d;
  overflow-y: scroll;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 6;
}
