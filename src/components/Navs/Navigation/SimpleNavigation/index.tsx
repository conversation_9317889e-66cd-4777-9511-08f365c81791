'use client'
import { useEffect, useState } from 'react'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import {
  setIsBlur as setIsBlurRedux,
  setIsTranslucent,
} from '../../../../redux/slices/appSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import Kernel from '../../../Kernel'
import NavigationHamburger from './@Core/NavigationHamburger'
import NavigationPrimaryHeader from './@Core/NavigationPrimaryHeader'
import { SimpleNavigationD } from './defaults'
import styles from './index.module.scss'
import { SimpleNavigationI } from './interface'

export default function SimpleNavigation(props: SimpleNavigationI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: SimpleNavigationI = {
    ...SimpleNavigationD,
    ...props,
  }

  // selects the 'isLoginActive' property from the 'login' slice of the Redux state.
  const isLoginActive = useAppSelector((state) => state.login.isLoginActive)

  const [isImmersive, setIsImmersive] = useState<boolean>(false)
  const [ishover, setIsHover] = useState<boolean>(false)
  // const isBlur = useAppSelector(state => state.app.isBlur)
  const dispatch = useAppDispatch()
  // const setIsBlurRedux = (isBlur: boolean) => { dispatch(setIsBlur(isBlur)) }
  const [isBlur, setIsBlur] = useState<boolean>(false)

  useEffect(() => {
    dispatch(setIsBlurRedux(isBlur))
    dispatch(setIsTranslucent(updatedProps?.isTranslucent))
  }, [isBlur, isImmersive])

  let translucentMarginB = updatedProps.isTranslucent ? 'mb-100' : ''

  let background = updatedProps.isLightMode ? 'bs2' : 'bp1'

  const blurBg = `${isBlur ? styles.blurBg : ''}`

  const isLightMode = isImmersive ? false : updatedProps.isLightMode

  const translucent = ishover ? background : ''

  const { size } = useWindowSize()

  return (
    <>
      <div className={blurBg}></div>
      <Kernel
        {...updatedProps}
        locale={props.locale}
        isFullXBleed
        isLightMode={isLightMode}
        htmlAttr={{
          className: `${styles.NavigationRoot} ${translucentMarginB}
                     ${updatedProps.isTranslucent ? translucent : background}
                        ${isImmersive ? styles.immersive : ''}
                        ${updatedProps.htmlAttr?.className}`,
          onMouseEnter: () => setIsHover(true),
          onMouseLeave: () => setIsHover(isLoginActive),
        }}
      >
        {size === 'small' ? (
          <NavigationHamburger
            {...updatedProps.NavigationSmall!}
            isLightMode={isLightMode}
            setBlurBg={setIsBlur}
            isImmersive={isImmersive}
            setIsImmersive={setIsImmersive}
            isLoginHidden={updatedProps.isLoginHidden}
            isLanguageHidden={updatedProps.isLanguageHidden}
            isSearchHidden={updatedProps.isSearchHidden}
            langLocalization={props.langLocalization}
            locale={props.locale}
            languages={props.languages}
          />
        ) : (
          <NavigationPrimaryHeader
            {...updatedProps.Navigationlarge!}
            isLightMode={isLightMode}
            isImmersive={isImmersive}
            setIsImmersive={setIsImmersive}
            setBlurBg={setIsBlur}
            isBlur={isBlur}
            isHover={ishover}
            isLoginHidden={updatedProps.isLoginHidden}
            isLanguageHidden={updatedProps.isLanguageHidden}
            isSearchHidden={updatedProps.isSearchHidden}
            isTranslucent={updatedProps.isTranslucent}
            langLocalization={props.langLocalization}
            locale={props.locale}
            languages={props.languages}
          />
        )}
      </Kernel>
    </>
  )
}
