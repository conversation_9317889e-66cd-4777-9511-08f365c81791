import { SimpleParagraphD } from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph/defaults'
import { KernelD } from '../../../../../Kernel'
import { NavigationLanguageI } from './interface'

export const NavigationLanguageD: NavigationLanguageI = {
  ...KernelD,
  _conf: {
    ...KernelD._conf,
    name: 'Navigation Expertise',
    status: 'DEV',
  },

  heading: {
    ...SimpleParagraphD,
    textContent: 'Please select your preferred language',
    colour: 'cp2',
    fontSize: 'fs3',
    fontFamily: 'fSansReg',
  },
  p1: {
    ...SimpleParagraphD,
    textContent: 'English (EN)',
    colour: 'cn2',
    fontSize: 'fs4',
  },
  p2: {
    ...SimpleParagraphD,
    textContent: 'French (FR)',
    colour: 'cn2',
    fontSize: 'fs4',
  },
  p3: {
    ...SimpleParagraphD,
    textContent: 'German (DE)',
    colour: 'cn2',
    fontSize: 'fs4',
  },
}
