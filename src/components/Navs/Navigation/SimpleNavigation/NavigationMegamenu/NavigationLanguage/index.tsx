'use client'
import { useEffect, useState } from 'react'
import { isBrowser, useTranslation } from '../../../../../../globals/utils'
import SimpleParagraph from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../../../Kernel'
import { NavigationLanguageD } from './defaults'
import styles from './index.module.scss'
import { NavigationLanguageI } from './interface'

export default function NavigationLanguage(props: NavigationLanguageI) {
  const { languages } = props

  const [pathName, setPathName] = useState('#')

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const getLanguageNameByLocale = (locale: string) => {
    switch (locale) {
      case 'en-CA':
        return useTranslation('english', props.locale)
      case 'fr-CA':
        return useTranslation('french', props.locale)
      case 'de-DE':
        return useTranslation('german', props.locale)
      case 'es':
        return useTranslation('spanish', props.locale)
      case 'it':
        return useTranslation('italian', props.locale)
      case 'nl':
        return useTranslation('dutch', props.locale)

      default:
        return ''
    }
  }

  const getLocalizationSlugByLocale = (locale: string) => {
    const isAltus = process.env.NEXT_PUBLIC_DOMAIN === 'domainAltusGroupCom'

    if (isAltus) {
      switch (locale) {
        case 'en-CA':
          return pathName
        case 'fr-CA':
          return '?lang=fr'
        case 'de-DE':
          return '?lang=de'
        case 'es':
          return '?lang=es'
        case 'it':
          return '?lang=it'
        case 'nl':
          return '?lang=nl'
        default:
          return ''
      }
    } else {
      const lang = locale.split('-')[0]

      return props?.langLocalization?.[lang]?.slug || '/'
    }
  }

  const updatedProps: NavigationLanguageI = {
    ...NavigationLanguageD,
    ...props,
    heading: {
      ...props.heading,
      textContent: useTranslation('selectLang', props.locale),
    },
    p1: {
      ...props.p1,
      textContent: useTranslation('english', props.locale),
    },
    p2: {
      ...props.p2,
      textContent: useTranslation('french', props.locale),
    },
    p3: {
      ...props.p3,
      textContent: useTranslation('german', props.locale),
    },
  }

  let background = updatedProps.isLightMode ? 'bs2' : 'bp1'

  const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs1'} ${
    styles.heading
  }`
  const para = `${
    updatedProps.isLightMode ? styles.paraLight : styles.paraDark
  }`

  useEffect(() => {
    if (isBrowser()) {
      setPathName(window.location.pathname)
    }
  }, [])

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationRoot} ${background} ${updatedProps.htmlAttr?.className}`,
        style: updatedProps.htmlAttr?.style,
      }}
    >
      <SimpleParagraph
        {...updatedProps.heading}
        fontSize='fs2'
        htmlAttr={{ className: colorClass }}
      />
      <div className={styles.container}>
        {languages?.length > 0 &&
          languages?.map((lang) => {
            let langProps = updatedProps.p1
            langProps = {
              ...langProps,
              textContent: getLanguageNameByLocale(lang),
            }
            return (
              <a
                // role={'link'}
                href={getLocalizationSlugByLocale(lang)}
                className={styles.link}
              >
                <SimpleParagraph
                  {...langProps}
                  htmlAttr={{ className: para }}
                />
              </a>
            )
          })}

        {/* {BUISNESS_DOMAINS_LOCALE_NAVIGATION[
          process.env.NEXT_PUBLIC_DOMAIN
        ]?.includes('fr-CA') && (
            <a href={props.langLocalization.fr.slug} className={styles.link}>
              <SimpleParagraph
                {...updatedProps.p2}
                htmlAttr={{ className: para }}
              />
            </a>
          )}
        {BUISNESS_DOMAINS_LOCALE_NAVIGATION[
          process.env.NEXT_PUBLIC_DOMAIN
        ]?.includes('de-DE') && (
            <a href={props.langLocalization.de.slug} className={styles.link}>
              <SimpleParagraph
                {...updatedProps.p3}
                htmlAttr={{ className: para }}
              />
            </a>
          )} */}
      </div>
    </Kernel>
  )
}