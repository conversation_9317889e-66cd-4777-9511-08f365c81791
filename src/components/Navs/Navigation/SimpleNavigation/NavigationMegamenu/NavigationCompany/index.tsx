import CardGenericMegamenu from '../../../../../Cards/CardGeneric/CardGenericMegamenu'
import SimpleParagraph from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../../../Kernel'
import MenuListMegamenu from '../../../../MenuList/MenuListMegamenu'
import { NavigationCompanyD } from './defaults'
import styles from './index.module.scss'
import { NavigationCompanyI } from './interface'

export default function NavigationCompany(props: NavigationCompanyI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationCompanyI = {
    ...NavigationCompanyD,
    ...props,
  }
  let background = updatedProps.isLightMode ? 'bs2' : 'bp1'

  const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs1'}`

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationRoot} ${background} ${updatedProps.htmlAttr?.className}`,
        style: updatedProps.htmlAttr?.style,
      }}
    >
      {' '}
      <div className={styles.menus}>
        {updatedProps?.menuLists &&
          updatedProps?.menuLists?.length > 0 &&
          updatedProps.menuLists.map((menuList) => (
            <MenuListMegamenu
              {...menuList}
              isLightMode={updatedProps.isLightMode}
              htmlAttr={{ className: styles.menuList }}
            />
          ))}
      </div>
      {/* <MenuListMegamenu {...updatedProps.menuList2} isLightMode={updatedProps.isLightMode}/> */}
      <div className={styles.cards}>
        {updatedProps?.cards &&
          updatedProps?.cards?.length > 0 &&
          updatedProps.cards?.map((card, index) => (
            <div className={styles.card1}>
              {updatedProps?.cardheadings?.at(index)?.textContent && (
                <SimpleParagraph
                  {...updatedProps?.cardheadings?.at(index)}
                  htmlAttr={{ className: colorClass }}
                  fontSize='fs2'
                />
              )}
              <CardGenericMegamenu
                {...card}
                isLightMode={updatedProps.isLightMode}
              />
            </div>
          ))}
        {/* <div className={styles.card1}>
        <SimpleParagraph {...updatedProps.p1} htmlAttr={{className:colorClass}}/>
        <CardGenericMegamenu {...updatedProps.card1} isLightMode={updatedProps.isLightMode}/>
       </div>
       <div className={styles.card2}>
       <SimpleParagraph {...updatedProps.p2} htmlAttr={{className:colorClass}}/>
        <CardGenericMegamenu {...updatedProps.card2} isLightMode={updatedProps.isLightMode}/>
       </div> */}
      </div>
    </Kernel>
  )
}
