import SimpleButtonWIcon from '../../../../../CTAs/SimpleButtonWIcon'
import Kernel from '../../../../../Kernel'
import MenuListMegamenu from '../../../../MenuList/MenuListMegamenu'
import { NavigationWWOfferD } from './defaults'
import styles from './index.module.scss'
import { NavigationWWOfferI } from './interface'

export default function NavigationWWOffer(props: NavigationWWOfferI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationWWOfferI = {
    ...NavigationWWOfferD,
    ...props,
  }

  const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs1'}`
  let background = updatedProps.isLightMode ? 'bs2' : 'bp1'

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationRoot} ${background} ${updatedProps.htmlAttr?.className}`,
        style: updatedProps.htmlAttr?.style,
      }}
    >
      {/*  {updatedProps.heading && <SimpleHeading {...updatedProps.heading} htmlAttr={{ className: `${styles.heading} ${colorClass}` }} />}
            {(updatedProps?.cards && updatedProps.cards.length > 0) && updatedProps.cards.map((card) => (
                <CardMegamenu {...card} isLightMode={updatedProps.isLightMode} />
            ))} */}
      {updatedProps.menuList1 && (
        <MenuListMegamenu
          {...updatedProps.menuList1}
          isLightMode={updatedProps.isLightMode}
          htmlAttr={{ className: styles.menu }}
        />
      )}

      {updatedProps.menuList2 && (
        <MenuListMegamenu
          {...updatedProps.menuList2}
          isLightMode={updatedProps.isLightMode}
          htmlAttr={{ className: styles.menu }}
        />
      )}
      <div className={styles.menu}>
        {updatedProps.menuList3 && (
          <MenuListMegamenu
            {...updatedProps.menuList3}
            isLightMode={updatedProps.isLightMode}
          />
        )}
        {updatedProps.cta?.textContent && (
          <div className={styles.button}>
            {' '}
            <SimpleButtonWIcon
              {...updatedProps.cta}
              isLightMode={updatedProps.isLightMode}
            />
          </div>
        )}
      </div>
    </Kernel>
  )
}
