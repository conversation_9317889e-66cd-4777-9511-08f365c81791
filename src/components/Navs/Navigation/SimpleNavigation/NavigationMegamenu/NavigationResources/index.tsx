import CardGenericMegamenu from '../../../../../Cards/CardGeneric/CardGenericMegamenu'
import SimpleParagraph from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../../../Kernel'
import MenuListMegamenu from '../../../../MenuList/MenuListMegamenu'
import { NavigationResourcesD } from './defaults'
import styles from './index.module.scss'
import { NavigationResourcesI } from './interface'

export default function NavigationResources(props: NavigationResourcesI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationResourcesI = {
    ...NavigationResourcesD,
    ...props,
  }

  let background = updatedProps.isLightMode ? 'bs2' : 'bp1'
  const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs1'}`

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationRoot} ${background} ${updatedProps.htmlAttr?.className}`,
        style: updatedProps.htmlAttr?.style,
      }}
    >
      {/* <div className={styles.cols}> */}
      <div className={styles.col1}>
        {updatedProps?.menuLists &&
          updatedProps.menuLists.length > 0 &&
          updatedProps.menuLists
            .slice(0, 2)
            .map((menuList) => (
              <MenuListMegamenu
                {...menuList}
                isLightMode={updatedProps.isLightMode}
                htmlAttr={{ className: styles.menuList1 }}
              />
            ))}
      </div>
      <div className={styles.col2}>
        {updatedProps?.menuLists &&
          updatedProps.menuLists.length > 2 &&
          updatedProps.menuLists
            .slice(2)
            .map((menuList) => (
              <MenuListMegamenu
                {...menuList}
                isLightMode={updatedProps.isLightMode}
                htmlAttr={{ className: styles.menuList2 }}
              />
            ))}
      </div>
      {/*  </div> */}
      <div className={styles.cards}>
        {updatedProps?.cards &&
          updatedProps?.cards?.length > 0 &&
          updatedProps.cards?.map((card, index) => (
            <div className={styles.card1}>
              {updatedProps?.cardheadings?.at(index)?.textContent && (
                <SimpleParagraph
                  fontSize='fs2'
                  isLightMode={updatedProps.isLightMode}
                  {...updatedProps?.cardheadings?.at(index)}
                  htmlAttr={{ className: colorClass }}
                />
              )}
              <CardGenericMegamenu
                {...card}
                isLightMode={updatedProps.isLightMode}
              />
            </div>
          ))}
      </div>
    </Kernel>
  )
}
