'use client'
import { useEffect, useState } from 'react'
import { BUISNESS_DOMAINS_LOCALE_NAVIGATION } from '../../../../../../globals/utils'
import LayoutContainer from '../../../../../Containers/LayoutContainer'
import SimpleParagraph from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../../../../Kernel'
import GenericIcon from '../../../../../Multimedia/Icons/SysIcon'
import Logo from '../../../../../Multimedia/Images/GenericLogo'
import SimpleSearch from '../../../../../SystemSearch/SimpleSearch'
import NavigationLanguage from '../../NavigationMegamenu/NavigationLanguage'
import NavigationAccordion from '../NavigationAccordion'
import { NavigationHamburgerD } from './defaults'
import styles from './index.module.scss'
import { NavigationHamburgerI } from './interface'

export default function NavigationHamburger(props: NavigationHamburgerI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: NavigationHamburgerI = {
    ...NavigationHamburgerD,
    ...props,
  }

  const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs2'}`
  /*   const logoColor = `${!updatedProps.isLightMode ? styles.logosBlack : styles.logosWhite
        }` */

  const background = `${updatedProps.isLightMode ? 'bs2' : 'bp1'}`

  const [lang, setLang] = useState<boolean>(false)
  const [showSearch, setShowSearch] = useState<boolean>(
    updatedProps.isImmersive
  )
  const [isHamburger, setIsHamburger] = useState<boolean>(true)

  const langData = {
    textContent: updatedProps?.locale?.split('-')?.[0].toUpperCase() || 'EN',
  }

  const OpenAccordionHandler = () => {
    setIsHamburger(false)
    setLang(false)
    setShowSearch(false)
    updatedProps.setIsImmersive(false)
    updatedProps.setBlurBg(false)
  }

  const CloseAccordionHandler = () => {
    setIsHamburger(true)
  }

  useEffect(() => {
    const closeMenusOnOutsideClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement

      // Check if the click is outside the menus
      if (!target.closest(`.${styles.NavigationHamburgerRoot}`)) {
        setLang(false)
        updatedProps.setBlurBg(false)
      }
    }

    // Add event listener on component mount
    document.body.addEventListener('click', closeMenusOnOutsideClick)

    // Remove event listener on component unmount
    return () => {
      document.body.removeEventListener('click', closeMenusOnOutsideClick)
    }
  }, [lang])

  //logo
  const logo = updatedProps.isLightMode ? (
    <Logo {...updatedProps.lightLogo} />
  ) : (
    <Logo {...updatedProps.darkLogo} />
  )

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        className: `${styles.NavigationHamburgerRoot} ${background}
        }  ${updatedProps.htmlAttr?.className}`,
      }}
    >
      <LayoutContainer
        htmlAttr={{
          className: `${!isHamburger && styles.contentBox} ${
            !isHamburger ? styles.paddingBottom : ''
          }`,
        }}
      >
        {isHamburger ? (
          <nav className={`${styles.menu}`}>
            {showSearch ? <Logo {...updatedProps.darkLogo} /> : logo}

            {!updatedProps.isImmersive && (
              <div className={styles.right}>
                {!updatedProps.isSearchHidden && (
                  <ul
                    aria-expanded={'true'}
                    onClick={() => {
                      setShowSearch(!showSearch)
                      updatedProps.setIsImmersive((pre) => !pre)
                      setLang(false)
                      document.body.style.overflow = 'hidden'
                    }}
                  >
                    <li>
                      <GenericIcon
                        {...updatedProps.searchIcon}
                        htmlAttr={{ className: `${colorClass} ` }}
                      />
                    </li>
                  </ul>
                )}

                {!updatedProps.isLanguageHidden && (
                  <ul
                    aria-expanded={'true'}
                    aria-haspopup={'true'}
                    tabIndex={0}
                    onClick={() => {
                      setLang(true)
                      updatedProps.setIsImmersive(false)
                      setShowSearch(false)
                      updatedProps.setBlurBg(true)
                    }}
                  >
                    <li>
                      <GenericIcon
                        {...updatedProps.earthIcon}
                        htmlAttr={{
                          className: `${colorClass} ${lang ? 'nav' : ''}`,
                        }}
                      />
                    </li>
                    {BUISNESS_DOMAINS_LOCALE_NAVIGATION[
                      process.env.NEXT_PUBLIC_DOMAIN
                    ].length > 0 && (
                      <li>
                        <SimpleParagraph
                          {...langData}
                          htmlAttr={{
                            className: `${colorClass} ${styles.heading} ${
                              lang ? 'nav' : ''
                            }`,
                            tabIndex: 0,
                            'aria-expanded': true,
                            'aria-haspopup': true,
                          }}
                        />
                      </li>
                    )}
                  </ul>
                )}
                <GenericIcon
                  {...updatedProps.hamburger!}
                  htmlAttr={{
                    className: colorClass,
                    onClick: OpenAccordionHandler,
                  }}
                />
              </div>
            )}
          </nav>
        ) : (
          <NavigationAccordion
            {...updatedProps.navigationAccordion}
            onClose={CloseAccordionHandler}
            isLightMode={updatedProps.isLightMode}
            isLoginHidden={updatedProps.isLoginHidden}
          />
        )}
        {lang && (
          <NavigationLanguage
            {...updatedProps.navigationLang}
            isLightMode={updatedProps.isLightMode}
            langLocalization={props.langLocalization}
            locale={props.locale}
            languages={props.languages}
          />
        )}
        {showSearch && <SimpleSearch {...updatedProps.search} />}
        {updatedProps.isImmersive && showSearch && (
          <GenericIcon
            icon='Close'
            htmlAttr={{
              className: styles.CloseIcon,
              onClick: () => {
                updatedProps.setIsImmersive(false)
                updatedProps.setBlurBg(false)
                setShowSearch(false)
                document.body.style.overflow = 'auto'
              },
            }}
          />
        )}
      </LayoutContainer>
    </Kernel>
  )
}
