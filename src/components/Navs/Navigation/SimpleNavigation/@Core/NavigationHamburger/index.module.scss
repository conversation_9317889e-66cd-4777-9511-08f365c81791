//Default SCSS variables are prepended in next.config.js
.NavigationHamburgerRoot {
  width: 100%;

  .menu {
    height: 100px;
    overflow: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .logosWhite {
      color: $cp1;
      filter: invert(10%) sepia(1%) saturate(1%) hue-rotate(1deg)
        brightness(100%) contrast(100%);
    }

    .logosBlack {
      color: $cs2;
      filter: invert(10%) sepia(1%) saturate(1%) hue-rotate(1deg)
        brightness(1000%) contrast(100%);
    }

    .right {
      display: flex;
      gap: 44px;

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 10px;

        li {
          float: left;

          a {
            text-decoration: none;
            display: block;
            text-align: center;
          }
        }
      }

      @media screen and (max-width: $smScreenSize) {
        gap: 20px;
      }
    }
  }
}

.heading {
  font-family: $fSansReg;
  font-size: $fs4;
  // font-weight: 400;
  cursor: pointer;

  &:hover {
    font-family: $fSansBld;
    // font-weight: 700;
  }
}

.ctas {
  min-width: auto;
}

.CloseIcon {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 8%;
  top: 40px;
  border-radius: 50%;
  color: $cp1;
  background-color: $cs2;
  /*   &:hover{
  color:$cs1;
  } */
}

.paddingBottom {
  padding-bottom: 40px;
}

.contentBox {
  border-radius: 0px 0px 5px 5px;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 100px 100px 0px;
}
