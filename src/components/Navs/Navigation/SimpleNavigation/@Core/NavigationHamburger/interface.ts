import { SimpleParagraphI } from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph/interface'
import { KernelI } from '../../../../../Kernel'
import { IconI } from '../../../../../Multimedia/Icons/SysIcon/interface'
import { LogoI } from '../../../../../Multimedia/Images/GenericLogo/interface'
import { SimpleSearchI } from '../../../../../SystemSearch/SimpleSearch/interface'
import { NavigationLanguageI } from '../../NavigationMegamenu/NavigationLanguage/interface'
import { NavigationAccordionI } from '../NavigationAccordion/interface'

export interface NavigationHamburgerI extends KernelI {
  [x: string]: any

  //...enlist other
  search: SimpleSearchI
  lightLogo?: LogoI
  darkLogo?: LogoI
  lang?: SimpleParagraphI
  searchIcon?: IconI
  profileIcon?: IconI
  earthIcon?: IconI
  hamburger?: IconI
  navigationLang: NavigationLanguageI
  navigationAccordion: NavigationAccordionI
  setBlurBg: React.Dispatch<React.SetStateAction<boolean>>
  setIsImmersive: React.Dispatch<React.SetStateAction<boolean>>
  isImmersive: boolean
  isLoginHidden: boolean
  isLanguageHidden: boolean
  isSearchHidden: boolean
}
