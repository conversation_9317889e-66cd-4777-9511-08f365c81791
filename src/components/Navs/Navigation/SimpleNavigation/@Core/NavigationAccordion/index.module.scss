//Default SCSS variables are prepended in next.config.js

.accordionContainer {
  cursor: pointer;

  .accordionHeader {
    padding: 20px 0;
    gap: 10px;
    // use this for style accordion tab header
  }

  .accordionHeader {
    .heading {
      font-family: $fSansBld;
      text-decoration: none;
      font-size: $fs4;
    }

    .ActiveHeading {
      text-decoration: none;
      font-family: $fSansBld;
      font-size: $fs4;
    }

    .darkHeading {
      color: $cp1;
      text-decoration: none;
      font-size: $fs4;
    }

    .lightHeading {
      color: $cs2;
      text-decoration: none;
      font-size: $fs4;
    }
  }
}

.logoContainer {
  background-color: $cp1;
  display: flex;
  align-items: center;
  height: 100px;
}

.Logo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logoColor {
  color: $cs2;
  filter: invert(10%) sepia(1%) saturate(1%) hue-rotate(1deg) brightness(1000%)
    contrast(100%);
}

.button {
  margin-top: 50px;
  width: fit-content;
}

.closeIcon {
  height: 25px;
  width: 25px;
}

.hideIcon {
  display: none;
}
