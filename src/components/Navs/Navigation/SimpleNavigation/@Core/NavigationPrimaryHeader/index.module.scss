//Default SCSS variables are prepended in next.config.js
.NavigationPrimaryRoot {
  width: 100%;
  z-index: 4;
  position: relative;

  .menu {
    height: 100px;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      align-items: center;
      gap: 80px;

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;

        :global .templateSingleDropdown {
          display: none;
          position: absolute;
          left: -15px;
          min-width: max-content;
          box-shadow: rgba(17, 12, 46, 0.15) 0px 100px 100px 0px;

          & ul {
            flex-direction: column;
            padding-bottom: 20px;
          }
        }

        li {
          float: left;
          position: relative;

          &:hover :global .templateSingleDropdown {
            display: flex;
          }

          a {
            text-decoration: none;
            // display: block;
            text-align: center;

            p {
              padding-right: 32px;
            }
          }

          p {
            padding-right: 32px;
          }

          &:hover {
            .hoverState {
              color: $cp2;
            }

            .darkHoverState {
              color: $cs1;
            }
          }
        }
      }

      @media screen and (max-width: 1080px) {
        gap: 60px;

        ul {
          li {
            a {
              p {
                padding-right: 25px;
              }
            }

            p {
              padding-right: 25px;
            }
          }
        }
      }

      @media screen and (max-width: 900px) {
        ul {
          li {
            a {
              p {
                padding-right: 14px;
              }
            }

            p {
              padding-right: 14px;
            }
          }
        }
      }

      @media screen and (max-width: 810px) {
        gap: 25px;

        ul {
          li {
            a {
              p {
                padding-right: 10px;
              }
            }

            p {
              padding-right: 10px;
            }
          }
        }
      }
    }

    .right {
      display: flex;

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: center;
        gap: 10px;
        padding-left: 20px;
        padding-right: 20px;
        cursor: pointer;

        > li {
          float: left;

          a {
            text-decoration: none;
            display: block;
            text-align: center;
          }
        }

        &:first-child {
          padding-left: 0;
        }

        &:last-child {
          padding-right: 0;
        }

        &:hover {
          .hoverState {
            color: $cp2;
          }

          .darkHoverState {
            color: $cs1;
          }
        }
      }

      @media screen and (max-width: 1280px) {
        ul {
          padding-left: 15px;
          padding-right: 15px;
        }
      }

      @media screen and (max-width: 1080px) {
        .earthIcon {
          display: none;
        }

        .loginWord {
          display: none;
        }
      }

      @media screen and (max-width: 900px) {
        ul {
          padding-left: 7px;
          padding-right: 7px;
        }
      }

      @media screen and (max-width: 810px) {
        ul {
          padding-left: 5px;
          padding-right: 5px;
        }
      }
    }
  }
}

.heading {
  font-family: $fSansBld;
  //font-size: $fs4;
  font-size: clamp(0.9375rem, 0.5625rem + 0.4688vw, 1.125rem);
  padding: 40px 0;
  cursor: pointer;
}

.ctas {
  //min-width: auto;
}

.navBar {
  position: absolute;
  width: 100%;
}

@keyframes animationIn {
  0% {
    opacity: 0;
    // top: -400px;
  }

  100% {
    opacity: 1;
    // height: 400px;
    // top: 100px;
  }
}

@keyframes animationOut {
  0% {
    opacity: 1;
    // height: 400px;
  }

  100% {
    opacity: 0.5;
    // height: 0;
  }
}

.animationIn {
  animation: animationIn 0.25s ease;
  top: 100px;
  z-index: 5 !important;
}

.animationOut {
  animation: animationOut 0.5s ease;
  top: 100px;
}

.paddingBottom {
  padding: 0 0 80px 0;
}

.content {
  margin-top: -10px;
  position: absolute;
  z-index: 3;
  transition: all 0.25s ease-in-out;
  width: 100%;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 100px 100px 0px;
}

.searchIcon {
  width: auto;
  height: auto;
}

.searchIconContainer {
  padding: 0 10px;
}

.CloseIcon {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 3%;
  top: 40px;
  border-radius: 50%;
  color: $cp1;
  background-color: $cs2;
}

.CloseIcon2 {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 3%;
  top: 40px;
  border-radius: 50%;
  color: $cs2;
  background-color: $cp1;
}

.overFlowContent {
  width: 100vw;
  height: 100vh;
  display: block;
  overflow-y: scroll;
  left: 0;
  top: 0;
}

.languageContent {
  min-width: max-content;
  display: none;
  position: absolute;
  right: -150px;
  top: 90px;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 100px 100px 0px;

  a {
    text-align: left !important;
  }

  @media screen and (max-width: $mScreenSize) {
    right: -90px;
  }
}

.language {
  position: relative;

  &:hover {
    .languageContent {
      display: flex;
    }
  }
}
