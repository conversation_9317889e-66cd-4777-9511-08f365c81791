'use client'
import {<PERSON>actN<PERSON>, useEffect, useState} from 'react'
import {useDispatch} from 'react-redux'
import {BUISNESS_DOMAINS_LOCALE_NAVIGATION} from '../../../../../../globals/utils'
import {useWindowSize} from '../../../../../../hooks/useWindowSize'
import {setLoginState} from '../../../../../../redux/slices/loginSlice'
import {setSearchState} from '../../../../../../redux/slices/searchSlice'
import {BUISNESS_DOMAINS} from '../../../../../../utils'
import LayoutContainer from '../../../../../Containers/LayoutContainer'
import SimpleParagraph from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import SimpleCTAs from '../../../../../CTAs/SimpleCTAs'
import Kernel from '../../../../../Kernel'
import GenericIcon from '../../../../../Multimedia/Icons/SysIcon'
import Logo from '../../../../../Multimedia/Images/GenericLogo'
import SimpleSearch from '../../../../../SystemSearch/SimpleSearch'
import NavigationCompany from '../../NavigationMegamenu/NavigationCompany'
import {NavigationCompanyI} from '../../NavigationMegamenu/NavigationCompany/interface'
import NavigationExpertise from '../../NavigationMegamenu/NavigationExpertise'
import {NavigationExpertiseI} from '../../NavigationMegamenu/NavigationExpertise/interface'
import NavigationLanguage from '../../NavigationMegamenu/NavigationLanguage'
import {NavigationLanguageD} from '../../NavigationMegamenu/NavigationLanguage/defaults'
import NavigationLogin from '../../NavigationMegamenu/NavigationLogin'
import NavigationResources from '../../NavigationMegamenu/NavigationResources'
import {NavigationResourcesI} from '../../NavigationMegamenu/NavigationResources/interface'
import NavigationSingleDropdown from '../../NavigationMegamenu/NavigationSingleDropdown'
import {NavigationSingleDropdownI} from '../../NavigationMegamenu/NavigationSingleDropdown/interface'
import NavigationWWOffer from '../../NavigationMegamenu/NavigationWWOffer'
import {NavigationWWOfferI} from '../../NavigationMegamenu/NavigationWWOffer/interface'
import {NavigationPrimaryHeaderD} from './defaults'
import styles from './index.module.scss'
import {NavigationPrimaryHeaderI} from './interface'

export default function NavigationPrimaryHeader(
    props: NavigationPrimaryHeaderI
) {
    /**
     * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
     * This way we don't need to keep providing the minimum defaults every time a component is added
     * and plus we don't end up mutating the originally provided props.
     * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
     */
    const updatedProps: NavigationPrimaryHeaderI = {
        ...NavigationPrimaryHeaderD,
        ...props
    }

    const colorClass = `${updatedProps.isLightMode ? 'cp1' : 'cs2'}`

    const textColor = `${updatedProps.isHover ? colorClass : 'cs2'}`

    let hoverColor = updatedProps.isLightMode
        ? styles.hoverState
        : styles.darkHoverState

    const {width} = useWindowSize()

    const logoWidth = updatedProps.isLightMode
        ? updatedProps.lightLogo?.width
        : updatedProps.darkLogo?.width

    //paddingLeft is to left align megamenu with first header.
    let paddingLeft = Number(logoWidth) + 65 + 'px'

    if(width <= 1080 && width > 810) {
        paddingLeft = Number(logoWidth) + 45 + 'px'
    } else if(width <= 810) {
        paddingLeft = Number(logoWidth) + 10 + 'px'
    }

    const background = updatedProps.isLightMode ? 'bs2' : 'bp1'

    const [showSearch, setShowSearch] = useState<boolean>(
        updatedProps.isImmersive
    )

    const [firstLoad, setFirstLoad] = useState(true)

    const [showLogin, setShowLogin] = useState<boolean>(false)

    const [index, setIndex] = useState<number | null>(null)

    const [contentOverflow, setContentOverflow] = useState<boolean>(false)

    const dispatch = useDispatch()

    const handleSearchState = (val: boolean) => {
        dispatch(setSearchState(val))
        setShowSearch(val)
        document.body.style.overflow = val === true ? 'hidden' : 'auto'
    }

    const handleLoginState = (val: boolean) => {
        setShowLogin(val)
        dispatch(setLoginState(val))
        setFirstLoad(false)
    }

    useEffect(() => {
        // Get the megamenuContentElement with the specified class name using querySelector
        const megamenuContentElement = document.querySelector(
            `.${styles.content}`
        ) as HTMLElement
        // Check if the megamenuContentElement is found
        if(megamenuContentElement) {
            // Get the total height of the megamenuContentElement
            const height = megamenuContentElement.offsetHeight

            const windowHeight = window.innerHeight

            setContentOverflow(height > windowHeight)
        }

        //body scroll
        const handleScroll = () => {
            // Close megamenu when the body starts scrolling
            setShowLogin(false)
            setIndex(null)
            updatedProps.setBlurBg(false)
            dispatch(setLoginState(false))
        }

        document.addEventListener('scroll', handleScroll)

        // Remove event listener on component unmount
        return () => {
            // document.body.removeEventListener('click', closeMenusOnOutsideClick)
            document.removeEventListener('scroll', handleScroll)
        }
    }, [dispatch, showLogin, index])

    let megamenuContent

    if(showLogin) {
        megamenuContent = (
            <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                <NavigationLogin
                    {...updatedProps.navigationLogin}
                    isLightMode={updatedProps.isLightMode}
                    htmlAttr={{style: {paddingLeft: paddingLeft}}}
                />
            </LayoutContainer>
        )
    } else if(showSearch) {
        megamenuContent = (
            <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                <SimpleSearch {...updatedProps.search} />
            </LayoutContainer>
        )
    } else {
        switch(index) {
            case 0:
                megamenuContent = (
                    <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                        <NavigationExpertise
                            {...updatedProps.navigationExpertise}
                            isLightMode={updatedProps.isLightMode}
                            htmlAttr={{style: {paddingLeft: paddingLeft}}}
                        />
                    </LayoutContainer>
                )
                break

            case 1:
                megamenuContent = (
                    <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                        <NavigationWWOffer
                            {...updatedProps.navigationWWOffer}
                            isLightMode={updatedProps.isLightMode}
                            htmlAttr={{style: {paddingLeft: paddingLeft}}}
                        />
                    </LayoutContainer>
                )
                break

            case 2:
                megamenuContent = (
                    <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                        <NavigationResources
                            {...updatedProps.navigationResources}
                            isLightMode={updatedProps.isLightMode}
                            htmlAttr={{style: {paddingLeft: paddingLeft}}}
                        />
                    </LayoutContainer>
                )
                break

            case 3:
                megamenuContent = (
                    <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                        <NavigationCompany
                            {...updatedProps.navigationCompany}
                            isLightMode={updatedProps.isLightMode}
                            htmlAttr={{style: {paddingLeft: paddingLeft}}}
                        />
                    </LayoutContainer>
                )
                break
            default:
                megamenuContent = <></>

                break
        }
    }

    const isDynamic = Boolean(
        updatedProps.navigations && updatedProps.navigations?.length > 0
    )

    const getSubmenus = (index: number) => {
        if(
            updatedProps.navigations &&
            updatedProps?.navigations[index]?.template
        ) {
            const template = updatedProps?.navigations[index].template
            switch(template) {
                // 1ml-c (Renamed 'Expertise' to 1ml-c)
                case '1ml-c':
                    return (
                        <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                            <NavigationExpertise
                                {...(updatedProps.navigations[index] as NavigationExpertiseI)}
                                isLightMode={updatedProps.isLightMode}
                                htmlAttr={{style: {paddingLeft: paddingLeft}}}
                            />
                        </LayoutContainer>
                    )
                // 2ml-c (Renamed 'Company' to 2ml-c)
                case '2ml-c':
                    return (
                        <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                            <NavigationCompany
                                {...(updatedProps.navigations[index] as NavigationCompanyI)}
                                isLightMode={updatedProps.isLightMode}
                                htmlAttr={{style: {paddingLeft: paddingLeft}}}
                            />
                        </LayoutContainer>
                    )
                // 2 x 2ml-c (Renamed 'Resources' to 2 x 2ml-c)
                case '2 x 2ml-c':
                    return (
                        <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                            <NavigationResources
                                {...(updatedProps.navigations[index] as NavigationResourcesI)}
                                isLightMode={updatedProps.isLightMode}
                                htmlAttr={{style: {paddingLeft: paddingLeft}}}
                            />
                        </LayoutContainer>
                    )
                // 3ml (Renamed 'What we offer' to 3ml)
                case '3ml':
                    return (
                        <LayoutContainer htmlAttr={{className: styles.paddingBottom}}>
                            <NavigationWWOffer
                                {...(updatedProps.navigations[index] as NavigationWWOfferI)}
                                isLightMode={updatedProps.isLightMode}
                                htmlAttr={{style: {paddingLeft: paddingLeft}}}
                            />
                        </LayoutContainer>
                    )
            }
        } else {
            return null
        }
    }

    //SingleDropdown menu
    const getSingleDropdownSubmenu = (updatedProps, index) => {
        if(
            BUISNESS_DOMAINS['reonomy'] !== process.env.NEXT_PUBLIC_DOMAIN &&
            updatedProps.navigations[index]?.template === 'Single Dropdown'
        ) {
            return (
                <div className={`${styles.singleDropdown} ${background}`}>
                    <NavigationSingleDropdown
                        {...(updatedProps.navigations[index] as NavigationSingleDropdownI)}
                        isLightMode={updatedProps.isLightMode}
                    />
                </div>
            )
        }
    }

    const dynamicNavigationComponent: null | ReactNode =
        index === null ? null : getSubmenus(index)

    const langData = {
        textContent: updatedProps?.locale?.split('-')?.[0].toUpperCase() || 'EN'
    }

    //langauge menu
    const languageMenu = !showSearch ? (
        <NavigationLanguage
            {...NavigationLanguageD}
            isLightMode={updatedProps.isLightMode}
            langLocalization={props.langLocalization}
            locale={props.locale}
            languages={props.languages}
            htmlAttr={{className: styles.languageContent}}
        />
    ) : (
        <></>
    )

    //MegaMenu
    const NavigationMegamenu =
        isDynamic && !showLogin && !showSearch ? (
            <div
                className={`${styles.content} ${
                    index === null
                        ? !firstLoad && styles.animationOut
                        : styles.animationIn
                } ${background}`}
                onMouseEnter={() => {
                    setIndex(index)
                    updatedProps.setBlurBg(true)
                }}
                onMouseLeave={() => {
                    setIndex(null)
                    updatedProps.setBlurBg(showLogin)
                }}
            >
                {' '}
                {dynamicNavigationComponent}
            </div>
        ) : (
            <div
                style={{top: showSearch ? '80px' : ''}}
                className={`${styles.content}  ${
                    showLogin
                        ? styles.animationIn
                        : !firstLoad && !showSearch && styles.animationOut
                } ${background}`}
                onMouseLeave={() => {
                    setShowLogin(false)
                    updatedProps.setBlurBg(false)
                    dispatch(setLoginState(false))
                }}
            >
                {' '}
                {megamenuContent}
            </div>
        )

    //logo
    const logo = updatedProps.isLightMode ? (
        <Logo {...updatedProps.lightLogo} />
    ) : (
        <Logo {...updatedProps.darkLogo} />
    )

    const translucentLogo = updatedProps.isTranslucent ? (
        <Logo {...updatedProps.darkLogo} />
    ) : updatedProps.isLightMode ? (
        <Logo {...updatedProps.lightLogo} />
    ) : (
        <Logo {...updatedProps.darkLogo} />
    )

    const defaultLogo = updatedProps.isHover ? logo : translucentLogo

    return (
        <>
            <Kernel
                {...updatedProps}
                htmlAttr={{
                    className: `${styles.NavigationPrimaryRoot} ${
                        contentOverflow && updatedProps?.isBlur && styles.overFlowContent
                    } ${updatedProps.htmlAttr?.className}`
                }}
            >
                <LayoutContainer>
                    <nav className={`${styles.menu}`}>
                        <div className={styles.left}>
                            {showSearch ? (
                                <Logo
                                    aria-label={'Back to Home link'}
                                    {...updatedProps.darkLogo}
                                />
                            ) : (
                                defaultLogo
                            )}
                            <ul>
                                {updatedProps?.links?.map((data, i) => {
                                    return (
                                        <li
                                            key={i}
                                            aria-expanded={'true'}
                                            // role={'navigation'}
                                            onClick={() => {
                                                handleSearchState(false)
                                                handleLoginState(false)
                                                updatedProps.setIsImmersive(false)
                                                setFirstLoad(false)
                                                updatedProps.setBlurBg(
                                                    updatedProps?.navigations[i]?.template !==
                                                    'Single Dropdown'
                                                )
                                            }}
                                            onMouseEnter={() => {
                                                setIndex(
                                                    updatedProps?.navigations[i]?.template !==
                                                    'Single Dropdown' || !showSearch
                                                        ? i
                                                        : null
                                                )
                                                updatedProps.setBlurBg(
                                                    updatedProps?.navigations[i]?.template !==
                                                    'Single Dropdown' || showLogin
                                                )
                                                setFirstLoad(false)
                                            }}
                                            onMouseLeave={() => {
                                                setIndex(null)
                                                updatedProps.setBlurBg(showLogin)
                                            }}
                                        >
                                            {data.cta?.href ? (
                                                <SimpleCTAs
                                                    {...data.cta!}
                                                    htmlAttr={{
                                                        ...data?.cta?.htmlAttr,
                                                        className: `${styles.ctas} `
                                                    }}
                                                >
                                                    <SimpleParagraph
                                                        {...data.text}
                                                        htmlAttr={{
                                                            ariaExpanded: true,
                                                            className: `${hoverColor} ${
                                                                updatedProps.isTranslucent
                                                                    ? textColor
                                                                    : colorClass
                                                            } ${styles.heading} ${
                                                                index === i
                                                                    ? updatedProps.isLightMode
                                                                        ? 'nav'
                                                                        : 'darkNav'
                                                                    : ''
                                                            }`
                                                        }}
                                                    />
                                                </SimpleCTAs>
                                            ) : (
                                                <SimpleParagraph
                                                    {...data.text}
                                                    htmlAttr={{
                                                        ariaExpanded: true,
                                                        className: `${hoverColor} ${
                                                            updatedProps.isTranslucent
                                                                ? textColor
                                                                : colorClass
                                                        } ${styles.heading} ${
                                                            index === i
                                                                ? updatedProps.isLightMode
                                                                    ? 'nav'
                                                                    : 'darkNav'
                                                                : ''
                                                        }`
                                                    }}
                                                />
                                            )}
                                            {getSingleDropdownSubmenu(
                                                updatedProps,
                                                !showSearch ? i : null
                                            )}
                                        </li>
                                    )
                                })}
                            </ul>
                        </div>
                        <div className={styles.right}>
                            {!updatedProps.isSearchHidden && (
                                <ul
                                    tabIndex={0}
                                    aria-expanded={'true'}
                                    className={styles.searchIconContainer}
                                    onClick={() => {
                                        handleSearchState(true)
                                        handleLoginState(false)
                                        setIndex(null)
                                        updatedProps.setIsImmersive(true)
                                    }}
                                >
                                    <li>
                                        <GenericIcon
                                            {...updatedProps.searchIcon}
                                            htmlAttr={{
                                                className: `${hoverColor} ${
                                                    updatedProps.isTranslucent ? textColor : colorClass
                                                } ${styles.searchIcon} ${showSearch ? 'cs2' : ''}`
                                            }}
                                        />
                                    </li>
                                </ul>
                            )}

                            {!updatedProps.isLanguageHidden && (
                                <ul
                                    tabIndex={0}
                                    aria-expanded={'true'}
                                    onClick={() => {
                                        handleSearchState(false)
                                        handleLoginState(false)
                                        updatedProps.setIsImmersive(false)
                                        updatedProps.setBlurBg(false)
                                        setFirstLoad(false)
                                    }}
                                    className={styles.language}
                                >
                                    <li
                                        className={styles.earthIcon}
                                        // role={'language'}
                                        tabIndex={0}
                                        aria-label={'Language icon/ link'}
                                    >
                                        <GenericIcon
                                            {...updatedProps.earthIcon}
                                            htmlAttr={{
                                                className: ` ${hoverColor} ${
                                                    updatedProps.isTranslucent ? textColor : colorClass
                                                }`
                                            }}
                                        />
                                    </li>
                                    {BUISNESS_DOMAINS_LOCALE_NAVIGATION[
                                        process.env.NEXT_PUBLIC_DOMAIN
                                        ].length > 0 && (
                                        <li aria-expanded={'true'} aria-haspopup={'true'}>
                                            <SimpleParagraph
                                                {...langData}
                                                htmlAttr={{
                                                    tabIndex: 0,
                                                    'aria-haspopup': 'true',
                                                    className: `${hoverColor} ${
                                                        updatedProps.isTranslucent ? textColor : colorClass
                                                    } ${styles.heading}`
                                                }}
                                            />
                                            {languageMenu}
                                        </li>
                                    )}
                                </ul>
                            )}

                            {!updatedProps.isLoginHidden && (
                                <ul
                                    tabIndex={0}
                                    aria-expanded={'true'}
                                    onClick={() => {
                                        handleSearchState(false)
                                        handleLoginState(true)
                                        setIndex(null)
                                        updatedProps.setIsImmersive(false)
                                        updatedProps.setBlurBg(true)
                                        setFirstLoad(false)
                                    }}
                                >
                                    <li className={styles.profileIcon}
                                        // role={'login'}
                                    >
                                        {updatedProps?.navigationLogin?.ctaLogin?.href ? (
                                            <SimpleCTAs
                                                {...updatedProps?.navigationLogin?.ctaLogin!}
                                                htmlAttr={{
                                                    ...updatedProps.ctaLogin?.htmlAttr,
                                                    className: `${styles.ctas} ${styles.loginWord} `
                                                }}
                                            >
                                                <GenericIcon
                                                    {...updatedProps.profileIcon}
                                                    htmlAttr={{
                                                        className: `${hoverColor} ${
                                                            updatedProps.isTranslucent
                                                                ? textColor
                                                                : colorClass
                                                        }  ${
                                                            showLogin
                                                                ? updatedProps.isLightMode
                                                                    ? 'nav'
                                                                    : 'darkNav'
                                                                : ''
                                                        }`
                                                    }}
                                                />
                                            </SimpleCTAs>
                                        ) : (
                                            <GenericIcon
                                                {...updatedProps.profileIcon}
                                                htmlAttr={{
                                                    className: `${hoverColor} ${
                                                        updatedProps.isTranslucent ? textColor : colorClass
                                                    }  ${
                                                        showLogin
                                                            ? updatedProps.isLightMode
                                                                ? 'nav'
                                                                : 'darkNav'
                                                            : ''
                                                    }`
                                                }}
                                            />
                                        )}
                                    </li>
                                    <li>
                                        {updatedProps?.navigationLogin?.ctaLogin?.href ? (
                                            <SimpleCTAs
                                                {...updatedProps?.navigationLogin?.ctaLogin!}
                                                htmlAttr={{
                                                    ...updatedProps.ctaLogin?.htmlAttr,
                                                    className: `${styles.ctas} ${styles.loginWord} `
                                                }}
                                            >
                                                <SimpleParagraph
                                                    {...updatedProps?.navigationLogin?.login}
                                                    htmlAttr={{
                                                        className: `${hoverColor} ${
                                                            updatedProps.isTranslucent
                                                                ? textColor
                                                                : colorClass
                                                        } ${styles.heading}  ${
                                                            showLogin
                                                                ? updatedProps.isLightMode
                                                                    ? 'nav'
                                                                    : 'darkNav'
                                                                : ''
                                                        }`
                                                    }}
                                                />
                                            </SimpleCTAs>
                                        ) : (
                                            <SimpleParagraph
                                                {...updatedProps?.navigationLogin?.login}
                                                htmlAttr={{
                                                    className: `${hoverColor} ${styles.loginWord} ${
                                                        updatedProps.isTranslucent ? textColor : colorClass
                                                    } ${styles.heading}  ${
                                                        showLogin
                                                            ? updatedProps.isLightMode
                                                                ? 'nav'
                                                                : 'darkNav'
                                                            : ''
                                                    }`
                                                }}
                                            />
                                        )}
                                    </li>
                                </ul>
                            )}
                        </div>
                    </nav>

                    {NavigationMegamenu}

                    {updatedProps.isImmersive && (
                        <GenericIcon
                            icon="Close"
                            htmlAttr={{
                                className: styles.CloseIcon,
                                onClick: () => {
                                    updatedProps.setIsImmersive(false)
                                    updatedProps.setBlurBg(false)
                                    handleSearchState(false)
                                    handleLanguageState(false)
                                    handleLoginState(false)
                                    setFirstLoad(true)
                                }
                            }}
                        />
                    )}
                </LayoutContainer>
            </Kernel>
        </>
    )
}