import { SimpleParagraphI } from '../../../../../ContentBlocks/Texts/Paragraphs/SimpleParagraph/interface'
import { SimpleCTAsI } from '../../../../../CTAs/SimpleCTAs/interface'
import { KernelI } from '../../../../../Kernel'
import { IconI } from '../../../../../Multimedia/Icons/SysIcon/interface'
import { LogoI } from '../../../../../Multimedia/Images/GenericLogo/interface'
import { SimpleSearchI } from '../../../../../SystemSearch/SimpleSearch/interface'
import { NavigationCompanyI } from '../../NavigationMegamenu/NavigationCompany/interface'
import { NavigationExpertiseI } from '../../NavigationMegamenu/NavigationExpertise/interface'
import { NavigationLoginI } from '../../NavigationMegamenu/NavigationLogin/interface'
import { NavigationResourcesI } from '../../NavigationMegamenu/NavigationResources/interface'
import { NavigationSingleDropdownI } from '../../NavigationMegamenu/NavigationSingleDropdown/interface'
import { NavigationWWOfferI } from '../../NavigationMegamenu/NavigationWWOffer/interface'

export interface links {
  cta: SimpleCTAsI
  text: SimpleParagraphI
}

export interface NavigationPrimaryHeaderI extends KernelI {
  //...enlist other
  setBlurBg: React.Dispatch<React.SetStateAction<boolean>>
  setIsImmersive: React.Dispatch<React.SetStateAction<boolean>>
  isHover: boolean
  isBlur: boolean
  isImmersive: boolean
  isTranslucent: boolean
  lightLogo?: LogoI
  darkLogo?: LogoI
  links?: links[]
  lang?: SimpleParagraphI
  login?: SimpleParagraphI
  searchIcon?: IconI
  profileIcon?: IconI
  earthIcon?: IconI
  ctaProfile?: SimpleCTAsI
  ctaLogin?: SimpleCTAsI
  navigations?: (
    | NavigationExpertiseI
    | NavigationWWOfferI
    | NavigationResourcesI
    | NavigationLoginI
    | NavigationCompanyI
  )[]
  navigationExpertise?: NavigationExpertiseI
  navigationWWOffer?: NavigationWWOfferI
  navigationResources?: NavigationResourcesI
  navigationLogin?: NavigationLoginI
  navigationCompany?: NavigationCompanyI
  search: SimpleSearchI
  SingleDropdownMenuList: NavigationSingleDropdownI
  isLoginHidden: boolean
  isLanguageHidden: boolean
  isSearchHidden: boolean
}
