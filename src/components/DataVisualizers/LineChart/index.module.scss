//Add Test specific styles only...
.root {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 30px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: start;

        .innerHeader {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-left: 110px;

            .heading {
                font-family: $fSansBld;
                color: $cn2;
            }

            .subheading {
                font-family: $fSansReg;
                color: $cn2;
            }

            @media screen and (max-width: $smScreenSize) {
                align-items: start;
                text-align: start;
                margin-left: 0;
            }
        }
    }

    .source {
        text-align: end;

        .sourceText {
            font-size: 12px;
            font-family: $fSansReg;
            color: $cn3;
        }
    }

    .download {
        background-color: $cn8;
        padding: 20px;
        box-shadow: 0px 10px 100px 1px rgba(10, 44, 55, 0.1);
        border-radius: 4px;
        gap: 10px;
        position: absolute;
        display: none;
        justify-content: space-between;
        flex-wrap: wrap;

        .downloadHeading {
            margin-bottom: 10px;
        }

        .button {
            justify-content: flex-start;
            font-family: $fSansReg;

            &>div {
                gap: 20px
            }

            ;
        }

    }

    .icon {
        position: relative;
        padding: 0 10px 25px 10px;


        &:hover {
            .download {
                display: flex;
                z-index: 2;
                top: 40px;
                right: -60px;
            }
        }
    }

    @media screen and (max-width: $mScreenSize) {
        padding: 20px;
        gap: 20px;
    }

    @media screen and (max-width: $smScreenSize) {
        padding: 10px;
    }
}