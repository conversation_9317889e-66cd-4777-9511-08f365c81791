import Papa from 'papaparse'
import * as XLSX from 'xlsx'
import { useWindowSize } from '../../hooks/useWindowSize'
import { fetchGraphQL } from '../../lib/api'
import {
  getConfigEnginItem,
  getConfigurationsCollectionQuery,
} from '../../lib/queries/globalConfig.query'

export const allColors = [
  '#000b3d',
  '#0028d7',
  '#001c99',
  '#8a8fa6',
  '#333c64',
  '#c9f344',
  '#fff',
  '#0082ab',
  '#e6f9a9',
  '#54661d',
  '#8ecbd2',
  '#000622',
  '#000',
  '#333',
  '#757575',
  '#c6c6c6',
  '#efefef',
  '#f2f2f2',
  '#f9f9f9',
  '#2cde93',
]

const dimensionColors: Record<string, string> = allColors.reduce(
  (acc, color, index) => {
    acc[`dimension${index + 1}`] = color
    return acc
  },
  {} as Record<string, string>
)
const axisIndex = Array.from({ length: 10 }).reduce((acc, _, i) => {
  acc[`dimension${i + 1}`] = 1;
  return acc;
}, {});

export const defaults = {
  'Line chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*  xAxisFonts: {
             fontColor: '#333333',
             fontFamily: 'sans-serif',
             fontSize: 16,
             lineHeight: 24
           }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*   yAxisFonts: {
              fontColor: '#333333',
              fontFamily: 'sans-serif',
              fontSize: 16,
              lineHeight: 24
            }, */
        }
      ]

    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
      ['x-axis']: axisIndex,
      ['y-axis']: axisIndex,

    }
  },
  'Bar chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*  xAxisFonts: {
             fontColor: '#333333',
             fontFamily: 'sans-serif',
             fontSize: 16,
             lineHeight: 24
           }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*   yAxisFonts: {
              fontColor: '#333333',
              fontFamily: 'sans-serif',
              fontSize: 16,
              lineHeight: 24
            }, */
        }
      ]

    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
      ['x-axis']: axisIndex,
      ['y-axis']: axisIndex,
    }
  },
  'Combo chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*  xAxisFonts: {
             fontColor: '#333333',
             fontFamily: 'sans-serif',
             fontSize: 16,
             lineHeight: 24
           }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*   yAxisFonts: {
              fontColor: '#333333',
              fontFamily: 'sans-serif',
              fontSize: 16,
              lineHeight: 24
            }, */
        }
      ]

    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
      ['x-axis']: axisIndex,
      ['y-axis']: axisIndex,
    }
  },
  'Area chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*  xAxisFonts: {
             fontColor: '#333333',
             fontFamily: 'sans-serif',
             fontSize: 16,
             lineHeight: 24
           }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*   yAxisFonts: {
              fontColor: '#333333',
              fontFamily: 'sans-serif',
              fontSize: 16,
              lineHeight: 24
            }, */
        }
      ]

    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
      ['x-axis']: axisIndex,
      ['y-axis']: axisIndex,
    }
  },
  'Pie chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
    }
  },
  'Doughnut chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors
    }
  },
  'Candlestick chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*  xAxisFonts: {
             fontColor: '#333333',
             fontFamily: 'sans-serif',
             fontSize: 16,
             lineHeight: 24
           }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*   yAxisFonts: {
              fontColor: '#333333',
              fontFamily: 'sans-serif',
              fontSize: 16,
              lineHeight: 24
            }, */
        }
      ]

    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
      ['x-axis']: axisIndex,
      ['y-axis']: axisIndex,
    }
  },
  'Waterfall chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*  xAxisFonts: {
             fontColor: '#333333',
             fontFamily: 'sans-serif',
             fontSize: 16,
             lineHeight: 24
           }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*   yAxisFonts: {
              fontColor: '#333333',
              fontFamily: 'sans-serif',
              fontSize: 16,
              lineHeight: 24
            }, */
        }
      ]

    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors,
      ['x-axis']: axisIndex,
      ['y-axis']: axisIndex,
    }
  },
  'Treemap chart': {
    general: {
      showTitle: true,
      title: 'Title',
      isDownloadable:true,
      /*   titleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        subTitleFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        },
        legendFonts: {
          fontColor: '#333333',
          fontFamily: 'sans-serif',
          fontSize: 16,
          lineHeight: 24
        }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
       titlePosition: 'top', */
      isZoomable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'top-right',
      legendOrientation: 'horizontal',
      /*  LegendTop: '0%', */
      radius: '50%',
      seriesName: ''
    },
    styles: {
      graphBgColor: '#EFEFEF',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: '',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#EFEFEF',
      gridLineColor: '#757575',
      gridLineStyle: '',
      gridLeft: 30,
      gridRight: 30,
      gridTop: 30,

      gridBottom: 30,
    },
    chart: {
      colors: dimensionColors
    }
  },
}


export async function fetchGlobalConfigData() {
  const res = await fetchGraphQL(getConfigurationsCollectionQuery(), true).then(
    (res: any) => res?.data?.configurationsCollection?.items
  )

  let response = ''

  const idToFetch =
    res.find((item: any) => {
      return item.type === 'Global'
    })?.sys?.id || ''

  if (idToFetch) {
    response = await fetchGraphQL(getConfigEnginItem(idToFetch), true).then(
      (res: any) => {
        return res?.data?.configurations?.data?.json?.content?.[0]?.content?.[0]
          ?.value
      }
    )
    if (response) {
      return {
        data: JSON.parse(response),
        contentId: idToFetch,
      }
    } else {
      return {
        data: {},
        contentId: idToFetch,
      }
    }
  }
}

export const parseCSVFile = async (url: string) => {
  try {
    const response = await fetch(url)
    const csvText = await response.text()
    const result = Papa.parse(csvText, { header: false })
    const data = result.data || []
    const updatedData = data
      ?.map((row) => row.filter((val) => val !== null && val !== ''))
      .filter((row) => row.length > 0)
    return updatedData
  } catch (error) {
    console.error('Error fetching the CSV file:', error)
    return null
  }
}

export const parseXLSXFile = async (url: string) => {
  try {
    const response = await fetch(url)
    const data = await response.arrayBuffer()
    const workbook = XLSX.read(new Uint8Array(data), { type: 'array' })
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]
    const parsedData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      defval: null,
    })
    const updatedData = parsedData
      .map((row) => row.filter((val) => val !== null))
      .filter((row) => row.length > 0)
    return updatedData
  } catch (error) {
    console.error('Error fetching the XLSX file:', error)
    return null
  }
}

export const getParsedData = async (url: string) => {
  const fileExtension = url?.split('.').pop().toLowerCase()
  if (fileExtension === 'csv') {
    const parsedData = await parseCSVFile(url)
    return parsedData
  } else if (fileExtension === 'xlsx') {
    const parsedData = await parseXLSXFile(url)
    return parsedData
  } else {
    console.error('Unsupported file type:', fileExtension)
  }
}

export const getAxes = (axes: any, grid: any, categories: any) => {
  const { size } = useWindowSize()
  const x = axes?.['x-axis']?.map((axis: any, index: number) => {
    const isVisible = axes?.['x-axis']?.length > 1
    return {
      show: axes?.showAxes && axis?.show,
      name: axis?.title,
      nameLocation: size !== 'large' ? 'center' : axis?.nameLocation || 'center',
      nameRotate: size !== 'large' ? 0 : axis?.nameRotate ? 90 : 0,
      nameGap: size !== 'large' ? 25 : (axis?.nameGap || 30),
      type: axis?.type,
      ...(axis?.type === 'category' && {
        data: categories?.[`x-axis-${index + 1}`],
      }),
      ...(isVisible && {
        ...(axis?.axisPosition && { position: axis?.axisPosition }),
        ...(axis?.alignTicks && { alignTicks: axis?.alignTicks }),
        ...(axis?.offset && { offset: Number(axis?.offset) }),
      }),
      ...(axis?.type === 'value' && {
        ...(axis?.min && { min: Number(axis.min) }),
        ...(axis?.max && { max: Number(axis.max) }),
        ...(axis?.interval && { interval: Number(axis?.interval) }),
      }),
      splitLine: {
        show: grid?.showGrid ? grid?.gridFormat !== 'Horizontal' ? true : false : false,
        lineStyle: {
          color: grid?.showGrid && grid?.gridLineColor,
          type: grid?.showGrid && grid?.gridLineStyle
        }
      },
      axisLine: {
        show: axis?.showAxisLine,
        lineStyle: {
          color: axis?.xAxisLineColor
        },
      },
      axisLabel: {
        width: size !== 'large' ? '50' : '100',
        overflow: "truncate",
        ellipsis: '...',
        fontSize: size === 'small' ? '10' : '12'
      },
      inverse: axis?.reverseAxis ? true : false,
      nameTextStyle: {
        fontFamily: 'Aeonik Regular sans-serif',
        /*   color: axis?.xAxisFonts?.fontColor,
          fontFamily: axis?.xAxisFonts?.fontFamily, 
        fontSize: axis?.xAxisFonts?.fontSize ,
        lineHeight:  axis?.xAxisFonts?.lineHeight */
      }
    }
  })

  const y = axes?.['y-axis']?.map((axis: any, index: number) => {
    const isVisible = axes?.['y-axis']?.length > 1

    return {
      show: axes?.showAxes && axis?.show,
      name: axis?.title,
      nameLocation: size !== 'large' ? 'end' : axis?.nameLocation || 'end',
      nameRotate: size !== 'large' ? 0 : axis?.nameRotate ? 90 : 0,
      nameGap: size !== 'large' ? 20 : (axis?.nameGap || 30),
      type: axis?.type,
      ...(axis?.type === 'category' && {
        data: categories?.[`y-axis-${index + 1}`],
      }),
      ...(isVisible && {
        ...(axis?.axisPosition && { position: axis?.axisPosition }),
        ...(axis?.alignTicks && { alignTicks: axis?.alignTicks }),
        ...(axis?.offset && { offset: Number(axis?.offset) }),
      }),
      ...(axis?.type === 'value' && {
        ...(axis?.min && { min: Number(axis.min) }),
        ...(axis?.max && { max: Number(axis.max) }),
        ...(axis?.interval && { interval: Number(axis?.interval) }),
      }),
      splitLine: {
        show: grid?.showGrid ? grid?.gridFormat !== 'Vertical' ? true : false : false,
        lineStyle: {
          color: grid?.showGrid && grid?.gridLineColor,
          type: grid?.showGrid && grid?.gridLineStyle

        }
      },
      axisLine: {
        show: axis?.showAxisLine,
        lineStyle: {
          color: axis?.yAxisLineColor
        },
      },
      axisLabel: {
        width: size === 'small' ? 20 : size === 'mid' ? 50 : 100,
        overflow: "truncate",
        ellipsis: '...',
        fontSize: size === 'small' ? '10' : '12'
      },
      inverse: axis?.reverseAxis ? true : false,
      nameTextStyle: {
        fontFamily: 'Aeonik Regular sans-serif',
        /*   color: axis?.yAxisFonts?.fontColor,
          fontFamily: axis?.yAxisFonts?.fontFamily, 
        fontSize: axis?.yAxisFonts?.fontSize ,
        lineHeight:  axis?.yAxisFonts?.lineHeight */
      }
    }
  })

  return { x, y }

}

export const getData = (data: any) => {
  // Get headers (first row)
  const headers = data[0];

  // Get all data except the headers
  const remainingData = data.slice(1)?.filter(value => value !== null);;


  // Get the dimensions 
  const dimensions = headers.filter(header => !header.startsWith('y-axis') && !header.startsWith('x-axis'));
  // Initialize an empty object to store categories for x and y columns
  const categoriesData: any = {};

  // Filter columns that start with 'x' or 'y'
  const filteredHeaders = headers.filter(header => header.startsWith('x-axis') || header.startsWith('y-axis'));

  filteredHeaders.forEach(header => {
    const columnData = remainingData.map(row => row[headers.indexOf(header)]).filter(value => value !== null); // Remove null values
    if (columnData.length > 0) {
      categoriesData[header] = columnData; // Only add if there's valid data
    }
  });

  // Prepare seriesData
  const sData = dimensions.map(col => {
    return remainingData
      .map(row => row[headers.indexOf(col)])
      .filter(value => value !== null); // Remove null values
  });

  return { categoriesData, dimensions, sData }

}

export const generateMarkLine = (markPreference: any, header: string) => {
  const { size } = useWindowSize()
  // Create a map of mark lines grouped by series name
  const markLineMap = markPreference?.markLineData?.reduce((acc, markLine) => {
    const seriesName = markLine.series;
    if (!acc[seriesName]) {
      acc[seriesName] = [];
    }
    acc[seriesName].push(markLine);
    return acc;
  }, {});

  return {
    label: {
      show: true,
      position: size !== 'large' ? 'insideEndTop' : 'end',    // Places the label at the end of the line
      formatter: '{b}',
      padding: 5,         // Padding: [top, right, bottom, left]
      borderRadius: 2,    // Optional: Rounding the label box
      borderColor: '#ccc',// Optional: Label border color
      borderWidth: 1,
      // Optional: Label border width
    },

    symbol: ['circle', 'arrow'],
    animation: true,
    animationDuration: 1000,
    data: markLineMap?.[header]?.map(data => {
      const axisLine = data?.axis === 'x-axis' ? 'xAxis' : 'yAxis'
      return {
        name: data?.MarkLineName ?? '',
        [`${axisLine}`]: data?.axisPont ?? '',
        label: {
          fontFamily: 'Aeonik Regular sans-serif',
          color: /* data?.markLineFonts?.fontColor ||  */'#fff',
          /*  fontFamily: data?.markLineFonts?.fontFamily,
           fontSize: size === 'small' ? '10' : data?.markLineFonts?.fontSize,
           lineHeight: size === 'small' ? '18' : data?.markLineFonts?.lineHeight, */
          backgroundColor: /*data?.markLineFonts?.bgColor ||  */'#e2222b'
        },
        lineStyle: {
          color: data?.markLineFonts?.bgColor || '#e2222b',
          width: 1,
          type: 'dashed'
        },
      }
    }
    ) || []
  }
};
export const getLegends = (generalPreference: any, dimensions: any) => {
  const { size } = useWindowSize()
  const smallPosition = generalPreference?.titlePosition === 'top' ? 'bottom' : 'top'
  const legend = {
    show: generalPreference?.showLegend,
    data: dimensions,
    type: 'scroll',
    width: '95%',
    orient: size !== 'large' ? 'horizontal' : generalPreference?.legendOrientation ?? 'horizontal',
    ...(size !== 'large' ? {
      bottom: 0,
      left: 'left'
    } : (generalPreference?.legendPosition === 'bottom-left' ? {
      bottom: generalPreference?.isZoomable ? '0' : (generalPreference?.legendOrientation === 'horizontal' ? '0' : '25'),
      left: 'left'
    } :
      {
        top: generalPreference?.legendOrientation === 'horizontal' ? '0' : '20',
        right: 'right'
      })),

    textStyle: {
      fontFamily: 'Aeonik Regular sans-serif',
      width: '200',
      overflow: 'break',
      /*   color: generalPreference?.legendFonts?.fontColor,
      fontFamily:  generalPreference?.legendFonts?.fontFamily 
      /*fontSize: size !== 'large' ? '12' : generalPreference?.legendFonts?.fontSize,
      lineHeight: size !== 'large' ? '20' : generalPreference?.legendFonts?.lineHeight, */
    },
  }
  return legend
}
export const getTitle = (generalPreference: any) => {
  const { size } = useWindowSize()
  const title = {
    show: false,/*  generalPreference?.showTitle, moving title out of Echart container, so making it false*/
    text: generalPreference?.title,
    subtext: generalPreference?.subtitle,
    itemGap: size !== 'large' ? 5 : 10,
    textAlign: "left",
    ...(generalPreference?.titlePosition === 'bottom' && (
      size !== 'large' ? { top: 'top' } : { bottom: '0%' }
    )),
    ...(generalPreference?.titlePosition === 'top' && {
      top: 'top'
    }),
    left: size !== 'large' ? 'left' : generalPreference?.titleAlignment || 'left',
    textStyle: {
      color: generalPreference?.titleFonts?.fontColor,
      fontFamily: generalPreference?.titleFonts?.fontFamily,
      fontSize: size === 'small' ? '16' : generalPreference?.titleFonts?.fontSize,
      lineHeight: size === 'small' ? '10' : generalPreference?.titleFonts?.lineHeight,
    },
    subtextStyle: {
      color: generalPreference?.subTitleFonts?.fontColor,
      fontFamily: generalPreference?.subTitleFonts?.fontFamily,
      fontSize: size === 'small' ? '12' : generalPreference?.subTitleFonts?.fontSize,
      lineHeight: size === 'small' ? '10' : generalPreference?.subTitleFonts?.lineHeight,
    }
  }
  return title
}

export const getGrid = (gridPreference: any, generalPreference: any, axesPreference: any) => {
  const { size } = useWindowSize()
  const legendBLH = generalPreference?.legendOrientation === 'horizontal' && generalPreference?.legendPosition === 'bottom-left'
  const legendTRH = generalPreference?.legendOrientation === 'horizontal' && generalPreference?.legendPosition === 'top-right'
  const legendBLV = generalPreference?.legendOrientation === 'vertical' && generalPreference?.legendPosition === 'bottom-left'
  const legendTRV = generalPreference?.legendOrientation === 'vertical' && generalPreference?.legendPosition === 'top-right'
  const grid = {
    show: gridPreference?.showGrid,
    backgroundColor: gridPreference?.gridBgColor,
    containLabel: true,
    top: (size !== 'large') ? 60 : (gridPreference?.gridTop || 30),
    right: (size !== 'large') ? 30 : (gridPreference?.gridRight || 30),
    left: (size !== 'large') ? 30 : (gridPreference?.gridLeft || 30),
    bottom: generalPreference?.isZoomable ? ((size !== 'large' && !legendBLH) ? '90' : (`${parseInt(gridPreference?.gridBottom || 30) + 40}`)) : ((size !== 'large' && !legendBLH) ? '60' : (`${parseInt(gridPreference?.gridBottom || 30) + 40}`))
  }
  return grid
}
export const getDataZoom = (gridPreference: any, generalPreference: any) => {
  const { size } = useWindowSize()
  const legendBLH = generalPreference?.legendOrientation === 'horizontal' && generalPreference?.legendPosition === 'bottom-left'
  const dataZoom = [
    {
      show: generalPreference?.isZoomable,
      type: 'slider',
      start: 0,
      end: 100,
      bottom: (size !== 'large' && !legendBLH) ? '40' : (gridPreference?.gridBottom || 30),
      height: size !== 'large' ? 5 : 10,
      fillerColor: "#efefef"
    },
    {
      type: 'inside',
      start: 0,
      end: 100,
      disabled: !generalPreference?.isZoomable
    }

  ]
  return dataZoom
}