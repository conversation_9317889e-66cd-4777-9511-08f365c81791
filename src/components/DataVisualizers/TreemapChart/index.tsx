'use client'
import { useEffect } from 'react'
import SimpleChart from '../SimpleDataVisualizer'

import * as echarts from 'echarts'
import { useTranslation } from '../../../globals/utils'
import { useWindowSize } from '../../../hooks/useWindowSize'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import Toolbar from '../../SocialShare/Toolbar'
import { getLegends, getTitle } from '../utils'
import { TreemapChartD } from './defaults'
import styles from './index.module.scss'
import { TreemapChartI } from './interface'

export default function TreemapChart(props: TreemapChartI): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: TreemapChartI = {
    ...TreemapChartD,
    ...props,
  }
  const local = updatedProps?.locale || 'en-CA'
  const { size } = useWindowSize()
  const id = updatedProps?.id
  const {
    generalPreference,
    stylePreference,
    gridPreference,
    colors,
  } = updatedProps?.preferences
  const data = updatedProps?.fileData
  const updatedHeight = stylePreference?.chartHeight || '400'
  const height = size === 'small' ? '400' : updatedHeight
  //builds childrens
  function buildChildrenMap(data) {
    const childrenMap: any = {}
    data?.slice(1)?.forEach((row) => {
      const parent = row[1]
      if (!childrenMap[parent]) {
        childrenMap[parent] = []
      }
      childrenMap[parent].push({ name: row[0], value: row[2], children: [] })
    })

    return childrenMap
  }
  //builds hierarchy
  function buildHierarchy(childrenMap, parent) {
    const hierarchy: any = []
    const children = childrenMap[parent]
    if (children) {
      children.forEach((child) => {
        child.children = buildHierarchy(childrenMap, child.name)
        hierarchy.push(child)
      })
    }
    return hierarchy
  }

  //tree map levels
  function generateTreemapLevels(data: any) {
    const baseBorderColor = '#333'
    const baseBorderWidth = 2
    const baseGapWidth = 2

    // Generate levels array
    const levels = data?.map((item, index) => {
      const level: any = {}

      // Configure colors
      if (index === 0) {
        level.color = colors?.[`dimension${index + 1}`]
        // Configure itemStyle
        level.itemStyle = {
          borderColor: baseBorderColor,
          borderWidth: baseBorderWidth,
          gapWidth: baseGapWidth,
        }
      } else {
        level.colorSaturation = [0.3, 0.6 - 0.1 * (index - 1)]
        // Configure itemStyle
        level.itemStyle = {
          borderColor: baseBorderColor,
          borderWidth: baseBorderWidth - index * 0.5,
          gapWidth: baseGapWidth - index * 1,
          borderColorSaturation: 0.7 - index * 0.1,
        }
      }

      return level
    })

    return levels
  }

  const childrenMap = buildChildrenMap(data)
  const hierarchy = buildHierarchy(childrenMap, '-')
  const legendData = hierarchy?.map((node) => node?.name)
  const levels = generateTreemapLevels(hierarchy)
  const legendBLH = generalPreference?.legendOrientation === 'horizontal' && generalPreference?.legendPosition === 'bottom-left'
  const seriesData = hierarchy?.map((item, index) => {
    return {
      type: 'treemap',
      name: item?.name,
      left: size !== 'large' ? 20 : gridPreference?.gridLeft || 30,
      top: gridPreference?.gridTop || 30,
      right: size !== 'large' ? 20 : gridPreference?.gridRight || 30,
      bottom: (size !== 'large' && !legendBLH) ? 40 : gridPreference?.gridBottom || 30,
      breadcrumb: {
        show: false
      },
      roam: generalPreference?.isZoomable,
      itemStyle: { color: colors?.[`dimension${index + 1}`] },
      data: [item],
      levels: levels,
      label: {
        show: true,
        formatter: '{b}', // Show node name
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}', // Show node name and value
      },
    }
  })

  const tooltip = {
    trigger: 'item',
    formatter: '{b}: {c}',
  }

  const options = {
    // backgroundColor: stylePreference?.graphBgColor,
    title: getTitle(generalPreference),
    legend: getLegends(generalPreference, legendData),
    series: seriesData,
    tooltip: tooltip,
  }

  useEffect(() => {
    // Create a new ECharts instance
    const myChart = echarts.init(document.getElementById(`tree-${id}-chart`)!)

    // Set the chart option
    myChart.setOption(options)
    // Enable ECharts responsiveness
    window.addEventListener('resize', () => {
      myChart.resize()
    })

    // Clean up ECharts instance on unmount
    return () => {
      myChart.dispose()
    }
  }, [updatedProps])

  const handleDownload = (type) => {
    const myChart = echarts.getInstanceByDom(document.getElementById(`tree-${id}-chart`)!)
    // Generate the chart in a hidden div with canvas renderer
    let dataURL
    if (type === 'svg') {
      const hiddenDiv = document.createElement('div');
      hiddenDiv.style.height = `${height}px`;
      hiddenDiv.style.width = "100%";
      hiddenDiv.style.visibility = 'hidden';
      document.body.appendChild(hiddenDiv);

      const hiddenChart = echarts.init(hiddenDiv, null, { renderer: 'svg' });
      hiddenChart.setOption(options); // Copy options from the original chart
      hiddenChart.resize();
      // Get the data URL and download the image
      dataURL = hiddenChart.getDataURL({
        type: 'svg',
        pixelRatio: 2,
        backgroundColor: 'transparent',
        excludeComponents: [],
      })
      document.body.removeChild(hiddenDiv);

    } else {
      dataURL = myChart?.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: 'transparent',
        excludeComponents: [],
      });

    }
    const link = document.createElement('a');
    link.href = dataURL;
    link.download = `${generalPreference?.title}.${type}`
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  const download = <div className={styles.download}>
    <SimpleHeading
      textContent={useTranslation('download', props.locale)}
      as={'h6'}
      colour={'cn2'}
      htmlAttr={{ className: styles.downloadHeading }}
    />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Svg' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('svg') }} textContent='SVG' />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Png' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('png') }} textContent='PNG' />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Jpg' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('jpg') }} textContent='JPG' />

  </div>

  //Returning the input text and it should be enclosed with SimpleText.
  return (
    <SimpleChart
      {...updatedProps}
      htmlAttr={{
        id: generalPreference?.chartId ?? id,
        style: {
          backgroundColor: stylePreference?.graphBgColor,
          borderWidth: `${stylePreference?.borderWidth}px`,
          borderColor: stylePreference?.borderColor,
          borderStyle: stylePreference?.borderType,
          boxShadow: `${stylePreference?.isBorderShadow ? 'rgba(0, 0, 0, 0.05) 0px 0px 0px 1px' : 'none'}`,
        },
        className: styles.root
      }}
    >
      <div className={styles.header}>
        <div className={styles.innerHeader}>
          {generalPreference?.title && <SimpleHeading htmlAttr={{ className: styles.heading }} as='h5' textContent={generalPreference?.title} />}
          {generalPreference?.subtitle && <p className={styles.subheading}>{generalPreference?.subtitle}</p>}
        </div>
        {generalPreference?.isDownloadable && <div className={styles.icon}>
          <GenericIcon icon='Download' />
          {download}</div>}

        <Toolbar
          bookMarkId={generalPreference?.chartId ?? id}
          locale={local}
          hidCalender
          hideClock
          isDvToolBar
        />
      </div>
      <div
        style={{ width: '100%', height: `${height}px` }}
        id={`tree-${id}-chart`}
      ></div>
      {generalPreference?.sourceText && <div className={styles.source}>
        <p className={styles.sourceText}>{generalPreference?.sourceText}</p>
      </div>}
    </SimpleChart>
  )
}
