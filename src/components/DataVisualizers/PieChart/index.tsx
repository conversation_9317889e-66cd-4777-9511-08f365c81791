'use client'
import * as echarts from 'echarts'
import React, { useEffect } from 'react'
import { useTranslation } from '../../../globals/utils'
import { useWindowSize } from '../../../hooks/useWindowSize'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import Toolbar from '../../SocialShare/Toolbar'
import SimpleChart from '../SimpleDataVisualizer'
import { getLegends, getTitle } from '../utils'
import { PieChartD } from './defaults'
import styles from './index.module.scss'
import { PieChartI } from './interface'

export default function PieChart(props: PieChartI): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: PieChartI = {
    ...PieChartD,
    ...props,
  }
  const { size } = useWindowSize()
  const id = updatedProps?.id
  const local = updatedProps?.locale || 'en-CA'
  const {
    generalPreference,
    stylePreference,
    gridPreference,
    colors,
  } = updatedProps?.preferences
  const data = updatedProps?.fileData
  const updatedHeight = stylePreference?.chartHeight || '400'
  const height = size === 'small' ? '400' : updatedHeight
  const items = data[0]
  const rows = data?.slice(1)?.filter((value) => value !== null)

  const series = items?.slice(1)?.map((header, index) => {
    return {
      name: header,
      data: rows
        ?.map((row) => row[index + 1])
        ?.filter((value) => value !== null),
    }
  })

  const legendBLH = generalPreference?.legendOrientation === 'horizontal' && generalPreference?.legendPosition === 'bottom-left'
  const seriesData = [
    {
      name: generalPreference?.seriesName,
      type: 'pie',
      radius: generalPreference?.radius,
      left: size !== 'large' ? 30 : gridPreference?.gridLeft || 30,
      top: size !== 'large' ? 30 : gridPreference?.gridTop || 30,
      right: size !== 'large' ? 30 : gridPreference?.gridRight || 30,
      bottom: (size !== 'large' && !legendBLH) ? 40 : gridPreference?.gridBottom || 30,
      center: ['50%', '50%'],
      emphasis: {
        focus: 'series',
      },
      color: series?.map((series, i) => colors?.[`dimension${i + 1}`]),
      data: series?.map((series) => {
        return {
          value: series?.data[0],
          name: series?.name,
        }
      }),
    },
  ]
  const dimensions = series?.map(data => data?.name)

  const tooltip = {
    trigger: 'item',
  }

  let options = {
    // backgroundColor: stylePreference?.graphBgColor,
    title: getTitle(generalPreference),
    legend: getLegends(generalPreference, dimensions),
    series: seriesData,
    tooltip: tooltip,
  }
  useEffect(() => {
    // Create a new ECharts instance
    const myChart = echarts.init(document.getElementById(`pia-${id}-chart`)!)

    // Set the chart option
    myChart.setOption(options)

    // Enable ECharts responsiveness
    window.addEventListener('resize', () => {
      myChart.resize()
    })

    // Clean up ECharts instance and event listener on unmount
    return () => {
      myChart.dispose()
    }
  }, [updatedProps])

  const handleDownload = (type) => {
    const myChart = echarts.getInstanceByDom(document.getElementById(`pia-${id}-chart`)!)
    // Generate the chart in a hidden div with canvas renderer
    let dataURL
    if (type === 'svg') {
      const hiddenDiv = document.createElement('div');
      hiddenDiv.style.height = `${height}px`;
      hiddenDiv.style.width = "100%";
      hiddenDiv.style.visibility = 'hidden';
      document.body.appendChild(hiddenDiv);

      const hiddenChart = echarts.init(hiddenDiv, null, { renderer: 'svg' });
      hiddenChart.setOption(options) // Copy options from the original chart
      hiddenChart.resize();
      // Get the data URL and download the image
      dataURL = hiddenChart.getDataURL({
        type: 'svg',
        pixelRatio: 2,
        backgroundColor: 'transparent',
        excludeComponents: [],
      })
      document.body.removeChild(hiddenDiv);

    } else {
      dataURL = myChart?.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: 'transparent',
        excludeComponents: [],
      });
    }

    const link = document.createElement('a');
    link.href = dataURL;
    link.download = `${generalPreference?.title}.${type}`
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  const download = <div className={styles.download}>
    <SimpleHeading
      textContent={useTranslation('download', props.locale)}
      as={'h6'}
      colour={'cn2'}
      htmlAttr={{ className: styles.downloadHeading }}
    />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Svg' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('svg') }} textContent='SVG' />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Png' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('png') }} textContent='PNG' />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Jpg' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('jpg') }} textContent='JPG' />

  </div>

  // Returning the input text and it should be enclosed with SimpleText.
  return (
    <SimpleChart
      {...updatedProps}
      htmlAttr={{
        id: generalPreference?.chartId ?? id,
        style: {
          backgroundColor: stylePreference?.graphBgColor,
          borderWidth: `${stylePreference?.borderWidth}px`,
          borderColor: stylePreference?.borderColor,
          borderStyle: stylePreference?.borderType,
          boxShadow: `${stylePreference?.isBorderShadow ? 'rgba(0, 0, 0, 0.05) 0px 0px 0px 1px' : 'none'}`,
        },
        className: styles.root
      }}
    >
      <div className={styles.header}>
        <div className={styles.innerHeader}>
          {generalPreference?.title && <SimpleHeading htmlAttr={{ className: styles.heading }} as='h5' textContent={generalPreference?.title} />}
          {generalPreference?.subtitle && <p className={styles.subheading}>{generalPreference?.subtitle}</p>}
        </div>
        {generalPreference?.isDownloadable && <div className={styles.icon}>
          <GenericIcon icon='Download' />
          {download}</div>}

        <Toolbar
          bookMarkId={generalPreference?.chartId ?? id}
          locale={local}
          hidCalender
          hideClock
          isDvToolBar
        />
      </div>
      <div
        style={{ width: '100%', height: `${height}px` }}
        id={`pia-${id}-chart`}
      ></div>
      {generalPreference?.sourceText && <div className={styles.source}>
        <p className={styles.sourceText}>{generalPreference?.sourceText}</p>
      </div>}
    </SimpleChart>
  )
}
