'use client'
import { useEffect } from 'react'
import SimpleChart from '../SimpleDataVisualizer'

import * as echarts from 'echarts'
import { useTranslation } from '../../../globals/utils'
import { useWindowSize } from '../../../hooks/useWindowSize'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import Toolbar from '../../SocialShare/Toolbar'
import { generateMarkLine, getAxes, getData, getDataZoom, getGrid, getLegends, getTitle } from '../utils'
import { WaterfallChartD } from './defaults'
import styles from './index.module.scss'
import { WaterfallChartI } from './interface'

export default function WaterfallChart(
  props: WaterfallChartI
): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: WaterfallChartI = {
    ...WaterfallChartD,
    ...props,
  }
  const { size } = useWindowSize()
  const id = updatedProps?.id
  const local = updatedProps?.locale || 'en-CA'
  const {
    generalPreference,
    stylePreference,
    axesPreference,
    gridPreference,
    colors,
    seriesX,
    seriesY,
    markPreference
  } = updatedProps?.preferences

  const data = updatedProps?.fileData

  const updatedHeight = stylePreference?.chartHeight || '400'
  const height = size === 'small' ? '400' : updatedHeight
  const { categoriesData, dimensions, sData } = getData(data)
  const newdimenssions = dimensions.filter((series) => series !== 'Placeholder')
  const seriesData = dimensions?.map((header, i) => {
    // Generate a stack key based on the combination of xAxisIndex and yAxisIndex
    const seriesXIndex = seriesX?.[`dimension${i + 1}`] ? Number(seriesX[`dimension${i + 1}`]) - 1 : 0;
    const seriesYIndex = seriesY?.[`dimension${i + 1}`] ? Number(seriesY[`dimension${i + 1}`]) - 1 : 0;

    const stackKey = `${seriesXIndex}-${seriesYIndex}`;
    if (i === 0) {
      return {
        name: 'Placeholder',
        type: 'bar',
        itemStyle: {
          borderColor: 'transparent',
          color: 'transparent'
        },
        emphasis: {
          itemStyle: {
            borderColor: 'transparent',
            color: 'transparent'
          }
        },
        data: sData[i],
        xAxisIndex: seriesXIndex,
        yAxisIndex: seriesYIndex,
        stack: 'stackKey',
      };
    } else {
      return {
        name: header,
        type: 'bar',
        data: sData[i],
        label: {
          show: false,
          position: 'inside'
        },
        itemStyle: {
          color: colors?.[`dimension${i}`],
        },
        xAxisIndex: seriesXIndex,
        yAxisIndex: seriesYIndex,
        emphasis: {
          focus: 'series'
        },
        stack: 'stackKey',
        markLine: generateMarkLine(markPreference, header)
      }
    }
  }
  )



  const formatterFunction = (params) => {
    let tar
    if (params[1] && params[1].value !== '-') {
      tar = params[1]
    } else {
      tar = params[2]
    }
    return tar && tar.name + '<br/>' + tar.seriesName + ' : ' + tar.value
  }

  const tooltip = {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    formatter: formatterFunction,
  }
  const { x, y } = getAxes(axesPreference, gridPreference, categoriesData)

  const options = {
    // backgroundColor: stylePreference?.graphBgColor,
    grid: getGrid(gridPreference, generalPreference, axesPreference),
    title: getTitle(generalPreference),
    legend: getLegends(generalPreference, newdimenssions),
    dataZoom: getDataZoom(gridPreference, generalPreference),
    xAxis: axesPreference?.reverseAxisType ? y : x,
    yAxis: axesPreference?.reverseAxisType ? x : y,
    series: seriesData,
    tooltip: tooltip
  }

  useEffect(() => {
    // Create a new ECharts instance
    const myChart = echarts.init(
      document.getElementById(`waterfall-${id}-chart`)!
    )
    // Set the chart option
    myChart.setOption(options)
    // Enable ECharts responsiveness
    window.addEventListener('resize', () => {
      myChart.resize()
    })

    // Clean up ECharts instance on unmount
    return () => {
      myChart.dispose()
    }
  }, [updatedProps])

  const handleDownload = (type) => {
    const myChart = echarts.getInstanceByDom(document.getElementById(`waterfall-${id}-chart`)!)
    // Generate the chart in a hidden div with canvas renderer
    let dataURL
    if (type === 'svg') {
      const hiddenDiv = document.createElement('div');
      hiddenDiv.style.height = `${height}px`;
      hiddenDiv.style.width = "100%";
      hiddenDiv.style.visibility = 'hidden';
      document.body.appendChild(hiddenDiv);

      const hiddenChart = echarts.init(hiddenDiv, null, { renderer: 'svg' });
      hiddenChart.setOption(options); // Copy options from the original chart
      hiddenChart.resize();
      // Get the data URL and download the image
      dataURL = hiddenChart.getDataURL({
        type: 'svg',
        pixelRatio: 2,
        backgroundColor: 'transparent',
        excludeComponents: [],
      })
      document.body.removeChild(hiddenDiv);

    } else {
      dataURL = myChart?.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: 'transparent',
        excludeComponents: [],
      });

    }
    const link = document.createElement('a');
    link.href = dataURL;
    link.download = `${generalPreference?.title}.${type}`
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  const download = <div className={styles.download}>
    <SimpleHeading
      textContent={useTranslation('download', props.locale)}
      as={'h6'}
      colour={'cn2'}
      htmlAttr={{ className: styles.downloadHeading }}
    />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Svg' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('svg') }} textContent='SVG' />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Png' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('png') }} textContent='PNG' />
    <SimpleButtonWIcon isLightMode isIconPrefixed variant='tertiary2' icon='Jpg' isFormButton htmlAttr={{ className: styles.button, onClick: () => handleDownload('jpg') }} textContent='JPG' />

  </div>

  //Returning the input text and it should be enclosed with SimpleText.
  return (
    <SimpleChart
      {...updatedProps}
      htmlAttr={{
        id: generalPreference?.chartId ?? id,
        style: {
          backgroundColor: stylePreference?.graphBgColor,
          borderWidth: `${stylePreference?.borderWidth}px`,
          borderColor: stylePreference?.borderColor,
          borderStyle: stylePreference?.borderType,
          boxShadow: `${stylePreference?.isBorderShadow ? 'rgba(0, 0, 0, 0.05) 0px 0px 0px 1px' : 'none'}`,
        },
        className: styles.root
      }}
    >
      <div className={styles.header}>
        <div className={styles.innerHeader}>
          {generalPreference?.title && <SimpleHeading htmlAttr={{ className: styles.heading }} as='h5' textContent={generalPreference?.title} />}
          {generalPreference?.subtitle && <p className={styles.subheading}>{generalPreference?.subtitle}</p>}
        </div>
        {generalPreference?.isDownloadable && <div className={styles.icon}>
          <GenericIcon icon='Download' />
          {download}</div>}

        <Toolbar
          bookMarkId={generalPreference?.chartId ?? id}
          locale={local}
          hidCalender
          hideClock
          isDvToolBar
        />
      </div>
      <div
        style={{ width: '100%', height: `${height}px` }}
        id={`waterfall-${id}-chart`}
      ></div>
      {generalPreference?.sourceText && <div className={styles.source}>
        <p className={styles.sourceText}>{generalPreference?.sourceText}</p>
      </div>}
    </SimpleChart>
  )
}
