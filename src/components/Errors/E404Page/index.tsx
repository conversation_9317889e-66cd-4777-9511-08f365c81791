'use client'
import { useEffect, useState } from 'react'
import {
  getLocalStorageItem,
  getQueryParamJson,
  isBrowser,
} from '../../../globals/utils'
import NavigationFooterRouter from '../../../lib/componentsRouter/NavigationFooterRouter'
import NavigationHeaderRouter from '../../../lib/componentsRouter/NavigationHeaderRouter'
import PageRouter from '../../../lib/componentsRouter/PageRouter'
import Popup from '../../Containers/Popup'

function E404Page(props: any) {
  const [footerData, setFooterData] = useState()
  const [componentData, setComponentData] = useState()
  const [navigationData, setNavigationData] = useState({})

  let defaultLangLocalization = {
    en: { slug: null },
    fr: { slug: '?lang=fr' },
    de: { slug: '?lang=de' },
    es: { slug: '?lang=es' },
    it: { slug: '?lang=it' },
    nl: { slug: '?lang=nl' },
  }
  const urlToLocale = {
    en: 'en-CA',
    fr: 'fr-CA',
    de: 'de-DE',
    es: 'es',
    it: 'it',
    nl: 'nl',
  }

  function detectLocaleFromUrl() {
    const url = window.location.href.split('/')
    const lang = getQueryParamJson().lang

    if (urlToLocale[lang]) return urlToLocale[lang]
    else if (Object.keys(defaultLangLocalization).includes(url?.[3]))
      return urlToLocale[url?.[3]]
    else return getLocalStorageItem('locale') || 'en-CA'
  }

  const locale = isBrowser() ? detectLocaleFromUrl() : 'en-CA'

  useEffect(() => {
    setNavigationData(props?.navigationData?.[locale])
    setFooterData(props?.footerData?.[locale])
    setComponentData([props?.error404Data?.[locale]])
  }, [])

  return (
    <>
      <NavigationHeaderRouter
        {...navigationData}
        locale={locale}
        langLocalization={defaultLangLocalization}
      />
      <PageRouter
        componentData={componentData}
        pageData={{ template: 'Generic' }}
      />
      <NavigationFooterRouter locale={locale} {...footerData} />
      <Popup isMaxedOut={false} />
    </>
  )
}

export default E404Page
