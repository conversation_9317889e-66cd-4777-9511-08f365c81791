'use client'

import React from 'react'
import { cl } from '../../globals/utils'

const ExperimentationEvent = ({ props, children }) => {
  const { htmlAttr, ...restProps } = props
  const existingOnClick = htmlAttr?.onClick

  // New onClick handler that calls the API
  const newOnClick = (event) => {
    cl('first Statsig from Kernal : Filter : FIrestKernal : Identifier', props)
    // event.stopPropagation()
    if (existingOnClick) {
      existingOnClick(event)
    }

    // Call the API
    cl('first Statsig from Kernal : Filter : FIrestKernal : Identifier')
  }

  const updatedHtmlAttr = {
    ...htmlAttr,
    onClick: newOnClick, // Ensure this is camel-cased
    onMouseEnter: () => {
      cl(
        'first Statsig from Kernal Hover : Filter : FIrestKernal : Identifier',
        props
      )
    },
  }
  cl(
    React.cloneElement(children, {
      ...restProps,
      ...updatedHtmlAttr,
    }),
    children,
    ''
  )
  // Debugging logs
  // cl('props: Identifier', props)
  // cl('updatedHtmlAttr: Identifier', updatedHtmlAttr)
  // cl('children: Identifier', children)
  // cl(props, updatedHtmlAttr, 'FilterOne Identifier')
  return React.cloneElement(children, {
    ...restProps,
    ...updatedHtmlAttr,
  })
  // return (
  //   <div
  //     // {...updatedHtmlAttr}
  //     onClick={(e) => {
  //       e.stopPropagation()
  //       cl('first Statsig  Click : Filter : FIrestKernal : Identifier')
  //     }}
  //   >
  //     {children}
  //   </div>
  // )
}

export default ExperimentationEvent

// 'use clinet'

// const ExperimentationEvent = ({ props, children }) => {
//   //   const { client } = useStatsigClient()
//   const handleClickStatsig = (data = {}) => {
//     cl('first Statsig from Kernal : Filter : FIrestKernal')
//     // client.logEvent('demo_event', 'next14', {
//     //   price: '9.99',
//     //   item_name: 'diet_coke_48_pack',
//     //   variant: props?.variant,
//     //   // variant: props?.variant,
//     //   ...data,
//     // })
//   }
//   return (
//     <div className='w-100 h-100 experimentFirest' onClick={handleClickStatsig}>
//       {children}
//     </div>
//   )
// }

// export default ExperimentationEvent
