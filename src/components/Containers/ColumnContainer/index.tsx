import { getImageProps } from 'next/image'
import SCSSVars from '../../../brands/altus/styles/_nextSCSSVars.module.scss'
import { CalculateComponent } from '../../../lib/propsMapping/layout.mapping'
import { BackgroundImage } from '../../Multimedia/Images'
import { BackgroundImageI } from '../../Multimedia/Images/BackgroundImage/interface'
import FlexContainer from '../FlexContainer'
import styles from '../FlexContainer/index.module.scss'

// Single Column Component
const ColumnContainer = (item) => {
    // getting the window size
    // l("html", item.htmlAttr)
    // const { size } = useWindowSize()
    const colGap = item?.columnGap ? parseInt(item?.columnGap) / 2 : 30

    return (
        // Using flex container
        <FlexContainer
            // setting flex direction as column
            flexDirection="column"
            // setting justify content property
            justifyContent={item?.alignItems}
            //setting align item property
            alignItems={item?.horizontalContentAlignment}
            // setting min width
            minWidth={SCSSVars.minW}
            // logically calculating the width
            // calc(${item.width} - 30px) here 30px is subtracted because default gap in 60px
            // width={`calc(${item.width} - ${colGap}px)`}
            width={`calc(${item.width} - ${colGap}px)`}
            // logically calculating the maxWidth
            // maxWidth={`calc(${item.width} - ${colGap}px)`}
            maxWidth={`calc(${item.width} - ${colGap}px)`}
            // logically calculating the flex
            flex={((item.width && item.width === '75%' && item.width === '66.666%') ? null : 1) as number}
            // Mapping htmlAttrs
            htmlAttr={{
                ...item?.htmlAttr,
                className: `${item?.htmlAttr?.className} ${styles.resizeClass}`,
                style: {
                    ...item?.htmlAttr?.style,
                    padding: item?.padding,
                    // if it has bg image put relative or put static
                    position: item?.bgImage ? 'relative' : 'static'
                }
            }}
        >
            {/* Background Image */}
            {item?.bgImage && (
                <BackgroundImage
                    {...getImageProps(item?.bgImage) as BackgroundImageI}
                    objectFit="cover"
                />
            )}
            {/* Components */}
            {item.layoutItemCollection.items.map((layoutItem) => layoutItem ? (
                // Dynamically return the component
                <CalculateComponent
                    key={layoutItem?.sys?.id}
                    {...layoutItem}
                    componentId={layoutItem?.sys?.id}
                    componentTypeName={layoutItem.__typename}
                    onChange={item?.onChange}
                    contentfulFormId={item?.contentfulFormId}
                />
            ) : <></>)}
        </FlexContainer>
    )
}

export default ColumnContainer