//Default SCSS variables are prepended in next.config.js

@keyframes slideInFromTop {
  0% {
    transform: translate(-50%, -1000px);
  }

  100% {
    transform: translateY(-50%, 0px);
  }
}

@keyframes SlideOutAnimation {
  0% {
    opacity: 1;
    transform: translate(-50%, 0);
  }

  100% {
    // opacity: 0;
    transform: translate(-50%, 700px);
  }
}

.closeAnimation {
  animation: SlideOutAnimation 0.5s ease forwards !important;
}

.popupRoot {
  // &::-webkit-scrollbar {
  //   width: 10px;
  //   height: 10px
  //   }

  margin: auto;
  min-width: 600px;
  min-height: 200px;
  max-width: 800px;
  max-height: calc(100vh - 10%);
  height: fit-content;
  overflow-y: auto;
  overflow-x: hidden;
  // scrollbar-width: 10px;
  position: fixed;
  z-index: 9;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
  animation: slideInFromTop 0.5s ease-in-out;
  background: $cn8;
  border: 1px solid #dee2e6;

  .popup {
    width: 100%;
    padding: 35px;
    height: fit-content;
    // max-height: 700px;
    // overflow: hidden;
    border-radius: 4px;

    .popupTitleRoot {
      display: flex;
      justify-content: space-between;
      // overflow-y: scroll;
      height: 100%;
      // padding-bottom: 16px;

      .content {
        width: 100%;
        padding: 0 30px;

        & > * {
          box-shadow: none;
          padding: 30px 0px;
        }
      }

      .popupTitle {
        display: flex;
        margin: 0;
      }

      .popupCloseBtn {
        display: inline-flex;
        position: absolute;
        right: 20px;
        top: 20px;
        background-color: white;
        z-index: 2;
        margin-left: 20px;
      }
    }
  }
}

// .popupTitleRoot::-webkit-scrollbar {
//   display: none;
//   }

.blurBg {
  position: fixed;
  width: 100%;
  height: 100vh;
  backdrop-filter: blur(1rem);
  z-index: 6;
  top: 0;
  left: 0;
}

.ddNone {
  display: none !important;
}

.buttonGroup {
  background: $cn6;
  padding: 33px 68px;
  border-radius: 0px 0px 4px 4px;
  border-bottom: 1px solid $cn4;

  @media (max-width: $smScreenSize) {
    padding: 33px 18px;
  }

  .buttons {
    justify-content: space-between;
    flex-wrap: wrap;
  }
}

@media (max-width: $smScreenSize) {
  .popupRoot {
    min-width: calc(100vw - 10%);
    // width: 350px !important;
    padding: 16px 0;
    border-radius: 4px;
    max-height: 80vh;

    .popup {
      padding: 12px !important;
      border-radius: 8px 8px 0px 0px;
      //.popupCloseBtn {
      //  right: 25px;
      //  top: 40px;
      //}
    }
  }
}

//@media (max-width: 450px) {
//  .popupCloseBtn {
//    right: 14px !important;
//  }
//}
.popupRootSmall {
  margin: 0;
  min-height: 400px;
  width: 100%;
  position: relative;
}

.popupCloseBtnSmall {
  display: inline-flex;
  position: absolute;
  right: -8px;
  top: -8px;
  z-index: 2;
}
