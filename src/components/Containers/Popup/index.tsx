'use client'
import Image from 'next/image'
import React, { useEffect } from 'react'
import { mapDataToComponent } from '../../../lib/propsMapping/index.mapping'
import { setPopupData, setShow } from '../../../redux/slices/popupSlice'
import { useAppDispatch, useAppSelector } from '../../../redux/store'
import { lowerCaseFirstLetter } from '../../../systems/AFS/AFSPages/utils'
import Kernel from '../../Kernel'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import imgStyle from '../../Multimedia/Images/SimpleImage/index.module.scss'
import PopupStyles from './index.module.scss'
import { PopupI } from './interface'

export default function Popup(props: PopupI): React.ReactElement {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  let updatedProps: PopupI = {
    // ...PopupD,
    isShow: false,
    ...props,
  }

  let isClosable = PopupStyles.ddNone

  const dispatch = useAppDispatch()
  const [isClosing, setIsClosing] = React.useState(false)
  const isShow =
    useAppSelector((state) => state.popup.isShow) || updatedProps.isShow
  const popupData = useAppSelector((state) => state.popup.popupData)

  useEffect(() => {
    // dispatch(setIsBlur(isShow))
  }, [isShow])

  if (Object.keys(popupData).length > 0) {
    updatedProps.children = popupData?.childrenCollection?.items?.map(
      (item: any) => {
        return mapDataToComponent(item[lowerCaseFirstLetter(item?.__typename)])
      }
    )
  }

  if (popupData.__typename === 'ComponentImage') {
    updatedProps = {
      ...popupData,
      htmlAttr: {
        ...popupData?.htmlAttr,
        style: {
          ...popupData.htmlAttr?.style,
          overflowY: 'auto',
          maxHeight: 'calc(90vh)',
          maxWidth: 'calc(90vw)',
        },
      },
      width: ((popupData?.originalWidth as number) + 130 + 'px') as string,
      height: ((popupData?.originalHeight as number) + 130 + 'px') as string,
    }
    updatedProps.children = (
      <Kernel
        {...updatedProps}
        htmlAttr={{
          ...updatedProps.htmlAttr,
          style: {
            ...updatedProps.htmlAttr.style,
            position: 'relative',
            maxWidth: '100%',
            maxHeight: '90vh',
            height: null,
            aspectRatio: updatedProps?.zoomAspectRatio ?? '1 / 1',
          },
        }}
      >
        <Image
          src={updatedProps.src}
          alt={updatedProps.alt}
          aria-label={updatedProps.alt}
          fill
          className={imgStyle.popupImage}
          loader={updatedProps.loader}
          sizes={updatedProps.sizes}
          quality={updatedProps.quality}
          priority={updatedProps.priority}
          placeholder={updatedProps.placeholder}
          onLoad={updatedProps.onLoad}
          onError={updatedProps.onError}
          loading='lazy'
          blurDataURL={updatedProps.blurDataURL}
          objectPosition={updatedProps.objectPosition}
        />
      </Kernel>
    )
  }

  if (!updatedProps.isClosable) {
    isClosable = '' // or you can set it to another class if needed
  }
  const hasChildren = React.Children.count(updatedProps.children) > 0
  const popupStyle = {
    border: updatedProps.hasBorder ? '1px solid #dee2e6' : 'none',
  }
  const handleShow = () => {
    document.body.style.overflow = 'auto'
    if (props.handleShow) {
      props.handleShow()
    } else {
      // If not, set isPresent to false
      setIsClosing(true)

      // Wait for the animation to complete before dispatching setShow
      setTimeout(() => {
        dispatch(setShow(false))
        setIsClosing(false)
      }, 600)
    }

    dispatch(setPopupData({}))
  }
  //Returning the Popup enclosed within Kernel
  /**
   * IN this component if we pass the children then the children will render and if we do not pass the children the default heading and rich text and buttons will be rendered
   * @todo Popup Children Mapping to Contentful
   */
  return hasChildren ? (
    <div className={isShow ? '' : PopupStyles.ddNone}>
      <div
        className={
          updatedProps.isMaxedOut ? PopupStyles.ddNone : PopupStyles.blurBg
        }
      ></div>
      <Kernel
        {...updatedProps}
        htmlAttr={{
          className: `popupRoot ${
            updatedProps.isMaxedOut
              ? PopupStyles.popupRootSmall
              : PopupStyles.popupRoot
          } ${updatedProps.htmlAttr?.className ?? ''} ${
            isClosing ? PopupStyles.closeAnimation : ''
          }`,
          style: {
            ...updatedProps?.htmlAttr?.style,
            width: updatedProps.width,
            height: updatedProps.height,
          },
        }}
        isPresent={isShow}
        isVisible
      >
        <div className={`${PopupStyles.popup}`} style={popupStyle}>
          <div className={PopupStyles.popupTitleRoot}>
            <div className={PopupStyles.content}>
              {/* children specifies all the children that can be passed in the popup */}
              {updatedProps.children}
              {/* {!hasChildren && (
                                <div className={PopupStyles.optionalChild}>
                                    <ContextualInformation
                                        {...updatedProps.contextualInformation}
                                        heading={{
                                            ...updatedProps?.contextualInformation?.heading,
                                            as: 'h3',
                                            htmlAttr: {
                                                style: {
                                                    marginTop: '0px'
                                                }
                                            }
                                        }}
                                        buttons={{
                                            ...updatedProps?.contextualInformation?.buttons
                                        }}
                                    />
                                </div>
                            )} */}
            </div>

            {/* start close button component */}
            <div
              className={`${
                updatedProps.isMaxedOut
                  ? PopupStyles.popupCloseBtnSmall
                  : PopupStyles.popupCloseBtn
              } ${isClosable}`}
              onClick={handleShow}
            >
              <GenericIcon icon={'Close'} size={'md'} />
            </div>
            {/* end close button component */}
          </div>
        </div>
      </Kernel>
    </div>
  ) : (
    <></>
  )
}