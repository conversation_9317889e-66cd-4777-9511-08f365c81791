//Default SCSS variables are prepended in next.config.js

/* styles/TabBox.module.scss */
.tabContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  width: 100%;

  .contexualInfo {
    max-width: 1071px;
  }

  .excerpt {
    color: $cn2;
    font-family: $fSansReg;
    font-size: $fs4;
    line-height: $fs6;
  }

  .tabItem {
    width: 100%;
    margin-top: 80px;

    @media screen and (max-width: $smScreenSize) {
      margin-top: 30px;
    }
  }
  .active {
    display: block;
  }
  .deactive {
    display: none;
  }
}
