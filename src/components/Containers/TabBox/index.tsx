'use client'

import React, { useState } from 'react'
import { getTabBoxProps } from '../../../lib/propsMapping/tabBox.mapping'
import ContextualInformation from '../../ContentBlocks/ContextualInformation'
import Kernel from '../../Kernel'
import TabsController from './@core/TabsController'
import styles from './index.module.scss'
import { TabBoxI } from './interface'

export default function TabBox(props: TabBoxI): React.ReactElement {
    const [activeTab, setActiveTab] = useState(0)
    let resB = getTabBoxProps(props)
    const updatedProps = {
        ...resB,
        isPresent: true,
        isVisible: true
    }
    // const [updatedProps, setProps] = useState()

    // useEffect(() => {
    //     let res = getTabBoxProps(props)
    //     setProps({
    //         ...res,
    //         isPresent: true,
    //         isVisible: true
    //     })
    // }, [props])

    const handleTabClick = (heading: string, index: number) => {
        setActiveTab(index)
        // Invoke the OnClickHandler prop if provided
        updatedProps.tabController.OnClickHandler?.(heading, index)
    }

    // const renderContent = () => {
    //   // Assuming you have an array of components corresponding to each tab
    //   const components = [
    //     // Component for Tab 1
    //     <SimpleQuotes key={0} />,
    //     // Component for Tab 2
    //     <NavigationFooter key={1} />,
    //     // Component for Tab 3
    //     // <YourComponentForTab3 key={2} />,
    //     // // Component for Tab 4
    //     // <YourComponentForTab4 key={3} />,
    //   ];

    //   // Render the component based on the active tab index
    //   return components[activeTab];
    // };

    // if (!updatedProps?.tabController?.GenericTAbs?.[activeTab]?.children) {
    //   return null
    // }

    return updatedProps ? (
        <Kernel
            {...updatedProps}
            htmlAttr={{
                ...updatedProps.htmlAttr,
                className: `${updatedProps.isLightMode ? styles.TabBoxRoot : styles.darkTabBoxRoot
                    } ${updatedProps.htmlAttr?.className} ${styles.tabContainer}`
            }}
        >
            <ContextualInformation
                {...updatedProps.contexualInfo}
                htmlAttr={{ className: styles.contexualInfo }}
                subHeading={{
                    ...updatedProps.contexualInfo?.subHeading,
                    colour: 'cp2'
                }}
                heading={{ ...updatedProps.contexualInfo?.heading, as: 'h2' }}
                excerpt={{
                    ...updatedProps.contexualInfo?.excerpt,
                    htmlAttr: { className: styles.excerpt }
                }}
            />

            <TabsController
                {...updatedProps.tabController}
                activeTab={activeTab}
                OnClickHandler={handleTabClick}
            />
            {updatedProps?.tabController?.GenericTAbs?.map((seprateTab, index) => <div className={`${styles.tabItem} ${activeTab === index ? styles.active : styles.deactive}`} key={`${seprateTab?.heading?.textContent}_${index}`}>
                {seprateTab?.children}
            </div>)}
            {/* <div className={styles.tabItem}>
                {updatedProps?.tabController?.GenericTAbs?.[activeTab]?.children}
            </div> */}
        </Kernel>
    ) : (
        <p>loading...</p>
    )
}