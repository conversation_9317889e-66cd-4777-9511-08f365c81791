'use client'
import React, { useState } from 'react'
import { getTabBoxHomeProps } from '../../../lib/propsMapping/tabBox.mapping'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import Kernel from '../../Kernel'
import TabsController from './@core/TabsController'
import styles from './index.module.scss'
import { TabBoxHomeI } from './interface'

export default function TabBoxHome(props: TabBoxHomeI): React.ReactElement {
    const [activeTab, setActiveTab] = useState(0)
    let resB = getTabBoxHomeProps(props)
    const updatedProps = {
        ...resB,
        isPresent: true,
        isVisible: true
    }
    // const [updatedProps, setProps] = useState()

    // useEffect(() => {
    //     let res = getTabBoxHomeProps(props)
    //     setProps({
    //         ...res,
    //         isPresent: true,
    //         isVisible: true
    //     })
    // }, [props])


    const handleTabClick = (heading: string, index: number) => {
        setActiveTab(index)
        // Invoke the OnClickHandler prop if provided
        updatedProps.tabController.OnClickHandler?.(heading, index)
    }

    // const { size } = useWindowSize()

    return updatedProps ? (
        <Kernel
            {...updatedProps}
            htmlAttr={{
                ...updatedProps.htmlAttr,
                className: `${updatedProps.isLightMode ? styles.TabBoxRoot : styles.darkTabBoxRoot
                    } ${updatedProps.htmlAttr?.className} ${styles.tabContainer}`
            }}
        >
            <div className={styles.headingContainer}>
                <SimpleHeading {...updatedProps?.heading} as='h3' colour='cp1' htmlAttr={{ className: styles.heading }} />
                <SimpleButtonWIcon {...updatedProps?.button} htmlAttr={{ className: `${styles.button} ${styles["sm-display-none"]} ${styles["md-display-none"]} ` }} isLightMode={updatedProps?.isLightMode} />
                {/* {size === 'large' && <SimpleButtonWIcon {...updatedProps?.button}  isLightMode={updatedProps?.isLightMode} />} */}
            </div>
            <TabsController
                {...updatedProps.tabController}
                activeTab={activeTab}
                OnClickHandler={handleTabClick}
            />
            {/* <div className={styles.tabItem}>
                {updatedProps?.tabController?.GenericTAbs?.[activeTab]?.children}
            </div> */}
            {updatedProps?.tabController?.GenericTAbs?.map((seprateTab, index) => <div className={`${styles.tabItem} ${activeTab === index ? styles.active : styles.deactive}`} key={`${seprateTab?.heading?.textContent}_${index}`}>
                {seprateTab?.children}
            </div>)}
            <SimpleButtonWIcon {...updatedProps?.button} htmlAttr={{ className: `${styles.button} ${styles["lg-display-none"]} ` }} isLightMode={updatedProps?.isLightMode} />
            {/* {size !== 'large' && <SimpleButtonWIcon {...updatedProps?.button} htmlAttr={{ className: `${styles.button} ${styles["lg-display-none"]} ` }} isLightMode={updatedProps?.isLightMode} />} */}
        </Kernel>
    ) : (
        <p>loading...</p>
    )
}