'use client'
import React, { useMemo } from 'react'
import SimpleContainer from '../SimpleContainer'
import { FlexContainerD } from './defaults'
import styles from "./index.module.scss"
import { FlexContainerI, FlexProperties } from './interface'
import { flexPropertiesObj } from './utils'
export default function FlexContainer(
    props: FlexContainerI
): React.ReactElement {
    /**
     * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
     * This way we don't need to keep providing the minimum defaults everytime a component is added
     * and plus we don't end up mutating the originally provided props.
     * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
     */
    const updatedProps: FlexContainerI = {
        ...FlexContainerD,
        ...props
    }

    const {
        alignItems,
        justifyContent,
        flexDirection,
        flexWrap,
        alignSelf,
        justifySelf,
        flex,
        flexBasis,
        flexGrow,
        flexShrink
    } = updatedProps

    const flexProperties: FlexProperties = useMemo(
        () =>
            flexPropertiesObj({
                alignItems,
                justifyContent,
                flexDirection,
                flexWrap,
                alignSelf,
                justifySelf,
                flex,
                flexBasis,
                flexGrow,
                flexShrink
            }),
        [
            alignItems,
            justifyContent,
            flexDirection,
            flexWrap,
            alignSelf,
            justifySelf,
            flex,
            flexBasis,
            flexGrow,
            flexShrink
        ]
    )
    //Returning the FlexContainer enclosed within SimpleContainer
    return (
        <SimpleContainer
            {...updatedProps}
            htmlAttr={{
                ...updatedProps.htmlAttr,
                className: `${updatedProps?.htmlAttr?.className} ${styles["responsive-element-col"]}`,
                style: {
                    display: 'flex',
                    ...flexProperties,
                    ...updatedProps?.htmlAttr?.style
                }
            }}
        >
            {updatedProps.children}
        </SimpleContainer>
    )
}