'use client'
import { useRelatedProducts } from '@algolia/recommend-react'
import { useRouter } from 'next/navigation'
import CardGenericSearch from '../../Cards/CardGeneric/CardGenericSearch'
import SimpleParagraph from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import { SimpleParagraphD } from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph/defaults'
import Kernel from '../../Kernel'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import {
  excludedDomains,
  recommendClient,
  recommendIndexName,
} from '../SearchActive'
import { getCardsData } from '../searchUtil'
import { SearchDefaultD } from './defaults'
import styles from './index.module.scss'
import { SearchDefaultI } from './interface'

export default function SearchDefault(props: SearchDefaultI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const searchHistory = localStorage.getItem('altusSearchHistory')
  const parsedSearchHistory = JSON.parse(searchHistory) || []

  const recentSearches = parsedSearchHistory?.slice(0, 5).map((item) => ({
    text: {
      ...SimpleParagraphD,
      textContent: item.textContent,
      href: item.href,
    },
  }))

  let currentDomain = window.location.hostname
  let recommendationsArray = []

  if (currentDomain.startsWith('www.')) {
    currentDomain = currentDomain.slice(4)
  }

  if (!excludedDomains.includes(currentDomain)) {
    const { recommendations } = useRelatedProducts({
      recommendClient,
      indexName: recommendIndexName,
      objectIDs: parsedSearchHistory?.map((item) => item.objectId),
    })

    recommendationsArray = recommendations
  }

  const cardsData = getCardsData(recommendationsArray)

  const updatedProps: SearchDefaultI = {
    ...SearchDefaultD,
    ...props,
  }

  const ClassText = `${updatedProps.isLightMode ? 'cn3' : 'cs2'}`
  const classHeading = `${updatedProps.isLightMode ? 'cp2' : 'cs1'}`

  const router = useRouter()

  //Returning the Input enclosed within Kernel
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: styles.searchResultContainer,
      }}
    >
      <div className={styles.recent}>
        {recentSearches && recentSearches.length > 0 && (
          <>
            <SimpleParagraph
              {...updatedProps.listHeading1}
              htmlAttr={{ className: classHeading }}
            />
            <ul>
              {recentSearches?.map((search, index) => (
                <li
                  key={index}
                  className={ClassText}
                  onClick={() => router.push(search?.text?.href)}
                >
                  <GenericIcon
                    {...search.timerIcon}
                    icon='Clock'
                    htmlAttr={{ className: styles.icon }}
                  />
                  <SimpleParagraph {...search.text} />
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
      <div className={styles.trending}>
        {cardsData && cardsData.length > 0 && (
          <>
            <SimpleParagraph
              {...updatedProps.listHeading2}
              htmlAttr={{ className: classHeading }}
            />
            <div className={`${styles.cards}`}>
              {cardsData?.map((card, ind) => (
                <CardGenericSearch
                  key={ind}
                  {...card}
                  isLightMode={updatedProps.isLightMode}
                  htmlAttr={{ className: styles.SingleCard }}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </Kernel>
  )
}
