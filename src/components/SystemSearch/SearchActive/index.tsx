'use client'
import recommend from '@algolia/recommend'
import { useEffect, useState } from 'react'
import { useInstantSearch } from 'react-instantsearch'
import { useSelector } from 'react-redux'
import { useWindowSize } from '../../../hooks/useWindowSize'
import { setRecentSearchReducer } from '../../../redux/slices/searchSlice'
import { useAppDispatch } from '../../../redux/store'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import CardGenericSearch from '../../Cards/CardGeneric/CardGenericSearch'
import SimpleParagraph from '../../ContentBlocks/Texts/Paragraphs/SimpleParagraph'
import Kernel from '../../Kernel'
import MenuListMegamenu from '../../Navs/MenuList/MenuListMegamenu'
import { getCardsData, getHref } from '../searchUtil'
import { SearchActiveD } from './defaults'
import {
  exludeInsightsAndEventsFilter,
  filterRecommendations,
} from './filterRecommendation'
import styles from './index.module.scss'
import { SearchActiveI } from './interface'

export const recommendClient = recommend(
  process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID as string,
  process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_ONLY_API_KEY as string
)
export const recommendIndexName = process.env
  .NEXT_PUBLIC_ALGOLIA_INDEX as string

export const excludedDomains = [
  'verifino.com',
  'financeactive.com',
  'one11advisors.com',
]

export default function SearchActive(props: SearchActiveI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults every time a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const { results } = useInstantSearch()

  const [eventsCards, setEventsCards] = useState([])
  const [insightsCards, setInsightsCards] = useState([])

  const linksFromHits = results.hits
    .filter(exludeInsightsAndEventsFilter)
    .map((hit) => ({
      objectId: hit.objectID,
      textContent: hit.shortTitle,

      href: getHref(hit.slug),
    }))

  let currentDomain = window.location.hostname
  let recommendationsArray = []

  if (currentDomain.startsWith('www.')) {
    currentDomain = currentDomain.slice(4)
  }

  // if (!excludedDomains.includes(currentDomain)) {
  //   const { recommendations } = useRelatedProducts({
  //     recommendClient,
  //     indexName: recommendIndexName,
  //     objectIDs: results.hits.map((hit) => hit.objectID),
  //   })

  //   recommendationsArray = recommendations
  // }

  const updatedProps: SearchActiveI = {
    ...SearchActiveD,
    ...props,
    menuList: {
      ...SearchActiveD.menuList,
      links: linksFromHits,
    },
  }

  // l(insightsAndResearchCardD);

  const { size } = useWindowSize()

  const buttonHeading = `${updatedProps.isLightMode ? 'cn2' : 'cs2'}`
  const classHeading = `${updatedProps.isLightMode ? 'cp2' : 'cs1'}`

  const { rePopulate } = useSelector((state: any) => state.search)
  const dispatch = useAppDispatch()

  useEffect(() => {
    const links = updatedProps?.menuList?.links

    if (links && links?.length > 0 && rePopulate) {
      const inputValue = {
        objectId: links[0]?.objectId as string,
        textContent: links[0]?.textContent as string,
        href: links[0]?.href as string,
      }
      dispatch(setRecentSearchReducer(inputValue))
    }
  }, [results])

  useEffect(() => {
    const { eventsAndWebinars, insightsAndResearch } = filterRecommendations(
      results.hits
    )

    setEventsCards(getCardsData(eventsAndWebinars))
    const insightsAndResearchCardD = getCardsData(insightsAndResearch)
    setInsightsCards(insightsAndResearchCardD)
  }, [results.hits])

  //Returning the Input enclosed within Kernel
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: styles.searchResultContainer,
      }}
    >
      {results.hits.length === 0 ? (
        <>
          <div className={styles.button}>
            <p>
              Sorry we couldn&apos;t find any results. Can&apos;t find what
              you&apos;re looking for?{' '}
            </p>
            <SimpleButtonWIcon
              {...updatedProps.button}
              isLightMode={false}
              href={'/contact-us'}
              htmlAttr={{ className: styles.cta }}
              isIconPrefixed={false}
              isChevron2Arrow={true}
            />
          </div>
        </>
      ) : (
        <>
          <div className={styles.left}>
            {linksFromHits?.length > 0 && (
              <MenuListMegamenu
                {...updatedProps?.menuList}
                htmlAttr={{ className: styles.menuList }}
                isLightMode={updatedProps.isLightMode}
              />
            )}
            {size === 'large' && (
              <div className={styles.button}>
                <SimpleParagraph
                  {...updatedProps.ButtonHeading}
                  htmlAttr={{ className: buttonHeading }}
                />
                <SimpleButtonWIcon
                  {...updatedProps.button}
                  isLightMode={false}
                  href={'/contact-us'}
                  htmlAttr={{ className: styles.cta }}
                  isIconPrefixed={false}
                  isChevron2Arrow={true}
                />
              </div>
            )}
          </div>
          <div className={styles.cardsContainer}>
            {insightsCards?.length > 0 && (
              <div className={`${styles.cards}`}>
                <SimpleParagraph
                  {...updatedProps.CardListHeading1}
                  htmlAttr={{ className: classHeading }}
                />
                {insightsCards?.map((card, ind) => (
                  <CardGenericSearch
                    key={ind}
                    {...card}
                    isLightMode={updatedProps.isLightMode}
                    htmlAttr={{ className: styles.SingleCard }}
                  />
                ))}
              </div>
            )}
            {eventsCards.length > 0 && (
              <div className={`${styles.cards}`}>
                <SimpleParagraph
                  {...updatedProps.CardListHeading2}
                  htmlAttr={{ className: classHeading }}
                />
                {eventsCards?.map((card, ind) => (
                  <CardGenericSearch
                    key={ind}
                    {...card}
                    isLightMode={updatedProps.isLightMode}
                    htmlAttr={{ className: styles.SingleCard }}
                  />
                ))}
              </div>
            )}
          </div>
          {(size === 'mid' || size === 'small') && (
            <div className={styles.button}>
              <SimpleParagraph
                {...updatedProps.ButtonHeading}
                htmlAttr={{ className: buttonHeading }}
              />
              <SimpleButtonWIcon
                {...updatedProps.button}
                isLightMode={false}
                href={'/contact-us'}
                htmlAttr={{ className: styles.cta }}
                isIconPrefixed={false}
                isChevron2Arrow={true}
              />
            </div>
          )}
        </>
      )}
    </Kernel>
  )
}
