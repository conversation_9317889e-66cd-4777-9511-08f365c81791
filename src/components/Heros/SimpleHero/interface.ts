import { BGC<PERSON>OURS, B<PERSON><PERSON><PERSON>IENTS, COLOURS } from '../../../globals/types'
import { ContextualInformationI } from '../../ContentBlocks/ContextualInformation/interface'
import { KernelI } from '../../Kernel'
import { BackgroundImageI } from '../../Multimedia/Images/BackgroundImage/interface'
import { BreadCrumbI } from '../../Navs/Breadcrumbs/interface'

export interface SimpleHeroI extends KernelI {
  contextualInformation?: ContextualInformationI
  height?: string
  Heroheight?: string
  bgColor?: BGCOLOURS | BGGRADIENTS
  textColor?: COLOURS
  bgImage?: BackgroundImageI
  breadcrumbs?: BreadCrumbI
  showDropdown?: boolean
  isHeroOverlay?: boolean
  showBreadcrumb?: boolean
  isHeroFirstChild?: boolean
  //...enlist other
}
