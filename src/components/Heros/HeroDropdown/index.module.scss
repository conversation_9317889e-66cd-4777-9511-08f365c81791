//Default SCSS variables are prepended in next.config.js

.heroDropdown {
  position: relative;
  z-index: 3;
  width: fit-content;
}

.heroDropdownButton {
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  cursor: pointer;
  //color: white;
  gap: 20px;
  padding: 0px 0px 0px 0px;
  margin-bottom: 20px;

  &:hover {
    color: $cp2;
  }
}

.label {
  color: $cp1;

  // font-family: $fSansReg;
  &:hover {
    color: $cp2;
    // font-family: $fSansBld;
  }
}

.heroDropdownItem {
  &:hover .heroDropdownButtonSelect .label {
    color: $cp2;
  }
}

.heroDropdownButtonSelect {
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  cursor: pointer;
  gap: 20px;
  padding: 0px 0px 0px 0px;
  margin-top: 20px;
}

.heroDropdownLabel {
  display: flex;
  align-items: center;
  gap: 5px;
}

.twoColHeroImage {
  border-radius: 50px;
}

.heroDropdownList {
  background-color: white;
  border-radius: 4px;
  padding: 24px;
  position: absolute;
  top: -4px;
  width: 248px;
  left: 0;
  transition: opacity 0.3s;
  box-shadow: 0 16px 80px rgba(0, 0, 0, 0.07);
}

.heroDropdownTab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
