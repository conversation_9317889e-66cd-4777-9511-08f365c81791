'use client'
import { useRouter } from 'next/navigation'
import React, { useEffect, useRef, useState } from 'react'
import { setLocalStorageItem } from '../../../globals/utils'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import Kernel from '../../Kernel'
import GenericIcon from '../../Multimedia/Icons/SysIcon'
import { SimpleImage } from '../../Multimedia/Images'
import { HeroDropdownD } from './defaults'
import styles from './index.module.scss'
import { HeroDropdownI } from './interface'

export default function HeroDropdown(props: HeroDropdownI): React.ReactElement {
  const updatedProps: HeroDropdownI = {
    ...HeroDropdownD,
    ...props,
  }
  const router = useRouter()
  const [isDropdownVisible, setDropdownVisible] = useState(false)
  const dropdownRef = useRef(null)
  const colorClass = updatedProps?.isLightMode ? 'cn2' : 'cs2'
  // Select the first option as the default one
  const [selectedOption, setSelectedOption] = useState(
    updatedProps.defaultOption
  )
  const toggleDropdown = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setDropdownVisible(false)
    } else {
      setDropdownVisible(!isDropdownVisible)
    }
  }

  const handleOptionClick = (option) => {
    setSelectedOption(option)
    setLocalStorageItem(updatedProps?.heading, option?.route)
    if (option.route.includes('https://')) {
      router.push(option.route)
    } else {
      router.push(`/${option.route}`)
    }
    setDropdownVisible(false) // Close the dropdown when an option is selected
  }

  useEffect(() => {
    document.addEventListener('click', toggleDropdown)

    // Clean up: Remove the event listener when the component unmounts
    return () => {
      document.removeEventListener('click', toggleDropdown)
    }
  }, [])

  return (
    <Kernel {...updatedProps}>
      <div
        className={`${styles.heroDropdown} ${
          isDropdownVisible ? styles.active : ''
        }`}
        ref={dropdownRef}
        onClick={toggleDropdown}
        // onMouseEnter={toggleDropdown}
        // onMouseLeave={toggleDropdown}
      >
        <div className={styles.heroDropdownButton}>
          <SimpleImage
            src={selectedOption?.image}
            alt='Dropdown Icon'
            width={'30px'}
            height={'30px'}
            objectFit={'cover'}
            htmlAttr={{ className: styles.twoColHeroImage }}
          />
          <div className={styles.heroDropdownLabel}>
            <SimpleHeading
              as={'h6'}
              colour={colorClass}
              {...selectedOption?.label}
            />
            <GenericIcon
              {...updatedProps.icon1}
              size={'sm'}
              iconColour={colorClass}
            />
          </div>
        </div>

        {isDropdownVisible && (
          <div className={styles.heroDropdownList}>
            <div className={styles.heroDropdownTab}>
              <SimpleHeading as={'h6'} colour='cn2' {...updatedProps.heading} />
              <GenericIcon {...updatedProps.icon2} size={'sm'} />
            </div>
            {updatedProps.options
              ?.filter((option) => option !== selectedOption) // Exclude the selected option
              .map((option, index) => (
                <div
                  key={index}
                  className={styles.heroDropdownItem}
                  onClick={() => handleOptionClick(option)}
                >
                  <button className={styles.heroDropdownButtonSelect}>
                    <SimpleImage
                      alt='Dropdown Icon'
                      src={option?.image}
                      width={'30px'}
                      height={'30px'}
                      objectFit={'cover'}
                      htmlAttr={{ className: styles.twoColHeroImage }}
                    />
                    <SimpleHeading
                      as={'h6'}
                      htmlAttr={{ className: styles.label }}
                      {...option?.label}
                    />
                  </button>
                </div>
              ))}
          </div>
        )}
      </div>
    </Kernel>
  )
}
