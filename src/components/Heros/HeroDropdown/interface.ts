import { SimpleHeadingI } from '../../ContentBlocks/Texts/Headings/SimpleHeading/interface'
import { KernelI } from '../../Kernel'
import { IconI } from '../../Multimedia/Icons/SysIcon/interface'

export interface HeroDropdownI extends KernelI {
  options?: {
    image: string
    label: SimpleHeadingI
    route: string
  }[]
  // ... other properties
  defaultOption?: string
  heading?: SimpleHeadingI
  icon1?: IconI
  icon2?: IconI
}
