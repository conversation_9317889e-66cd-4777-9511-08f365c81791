'use client'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import LayoutContainer from '../../../Containers/LayoutContainer'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import CTAGroup from '../../../CTAs/CTAGroup'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import { SimpleCTAsI } from '../../../CTAs/SimpleCTAs/interface'
import SimpleHero from '../../SimpleHero'
import styles from './index.module.scss'
import { HeroSoftwareI } from './interface'

export default function HeroSoftware(props: HeroSoftwareI) {
  let classname = props?.bgColor ? props?.bgColor : 'bp2p1r'

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: HeroSoftwareI = {
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: 'pb4',
    },
  }
  const buttons = updatedProps?.contextualInformation?.buttons?.links?.map(
    (link: SimpleCTAsI) => {
      return {
        ...link,
        isLightMode: link?.isLightMode,
      }
    }
  )
  const { size } = useWindowSize()
  return (
    <SimpleHero {...updatedProps} isFullXBleed>
      <div className={`${classname} ${styles.bgHeroRoot}`}>
        <LayoutContainer>
          <LayoutContainer>
            <div className={styles.bgHeroContext}>
              <ContextualInformation
                {...updatedProps.contextualInformation}
                isLightMode={updatedProps?.isLightMode}
                heading={{
                  ...updatedProps.contextualInformation?.heading,
                  as: 'h1',
                }}
                excerpt={{
                  ...updatedProps.contextualInformation?.excerpt,
                  htmlAttr: { className: styles.excerpt },
                }}
                htmlAttr={{ className: `${styles.contexualInfo}` }}
              />
            </div>
            {updatedProps?.form ? (
              <div className={`${styles.form} ${size === 'small' && 'bleedf'}`}>
                {updatedProps?.form}
              </div>
            ) : (
              <div className={styles.buttons}>
                {size === 'small' ? (
                  <SimpleButtonWIcon
                    {...updatedProps?.button}
                    isLightMode={updatedProps?.button?.isLightMode}
                    textContent={updatedProps?.button?.text}
                  />
                ) : (
                  <CTAGroup
                    {...updatedProps.contextualInformation?.buttons}
                    links={buttons}
                    isLightMode={undefined}
                  />
                )}
              </div>
            )}

            {updatedProps.singleImageOrVideo ? (
              <div className={`${styles.tabsRoot} ${styles.imgRoot}`}>
                {updatedProps.singleImageOrVideo}
              </div>
            ) : (
              <div className={styles.tabsRoot}>
                <div
                  className={`${styles.tabs}  ${size === 'small' && 'bleedf'}`}
                >
                  {updatedProps?.imageTabs}
                </div>
              </div>
            )}
          </LayoutContainer>
        </LayoutContainer>
      </div>
      <LayoutContainer htmlAttr={{ className: styles.logos }}>
        {updatedProps?.logos}
      </LayoutContainer>
    </SimpleHero>
  )
}
