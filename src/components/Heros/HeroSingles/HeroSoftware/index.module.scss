.bgHeroRoot {
  min-height: 600px;
  padding-top: 220px;

  .bgHeroContext {
    display: flex;
    flex-direction: column;
    margin-bottom: 60px;

    .contexualInfo {
      text-align: center;

      .excerpt {
        p {
          text-align: center;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      width: 100%;
      margin-bottom: 30px;
    }
  }

  .buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 60px;
  }

  .form {
    width: 80%;
    margin-left: 10%;
    margin-bottom: 60px;
  }

  .tabsRoot {
    width: 100%;
    display: flex;
    height: 600px;
    margin-bottom: -250px;
    justify-content: center;

    @media (max-width: $smScreenSize) {
      margin-bottom: -150px;
      height: 300px;
    }
  }

  .imgRoot {
    img {
      object-fit: fill !important;
    }

    @media (max-width: $smScreenSize) {
      margin-bottom: -150px;
      height: 300px;
      padding: 5px !important;
    }
  }

  .tabs {
    z-index: 1;
    padding: 8px;
    background: rgba(10, 44, 55, 0.2);
    opacity: 1;
    backdrop-filter: blur(1rem);
    width: 1140px;

    img {
      object-fit: fill !important;
    }

    @media (max-width: $mScreenSize) {
      width: 725px !important;
    }

    @media (max-width: $smScreenSize) {
      width: 370px !important;
      margin-bottom: -150px;
      height: 300px;
      padding: 5px !important;
    }
  }

  @media (max-width: $smScreenSize) {
    padding-top: 100px;

    .buttons {
      margin-bottom: 30px;
    }

    .form {
      width: 100%;
      margin-left: 0;
      margin-bottom: 30px;
    }
  }
}

.logos {
  margin-top: 340px;

  @media (max-width: $smScreenSize) {
    margin-top: 210px;
  }
}
