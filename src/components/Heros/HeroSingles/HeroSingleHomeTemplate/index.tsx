'use client'
import { l } from '../../../../globals/utils'
import LayoutContainer from '../../../Containers/LayoutContainer'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import SimpleHero from '../../SimpleHero'
import styles from './index.module.scss'
import { HeroSingleHomeTemplateI } from './interface'

export default function HeroSingleHomeTemplate(props: HeroSingleHomeTemplateI) {
  let classname = 'bp2p1r'

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: HeroSingleHomeTemplateI = {
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: `${classname} ${styles.bgHeroRoot}`,
    },
  }

  l('button', updatedProps.button)

  return (
    <SimpleHero {...updatedProps} isFullXBleed>
      <LayoutContainer>
        <div className={styles.bgHeroContext}>
          <SimpleHeading
            {...updatedProps.contextualInformation?.heading}
            as={'h1'}
          />
          <div className={`${styles.heroHr} bs1s3d mt1`}></div>
          <div className={`${styles.heroBtnDiv} mt1`}>
            <SimpleHeading
              textContent={updatedProps.contextualInformation?.excerpt}
              as={'h5'}
            />
            {updatedProps.button && (
              <SimpleButtonWIcon {...updatedProps.button} />
            )}
          </div>
        </div>
      </LayoutContainer>
    </SimpleHero>
  )
}
