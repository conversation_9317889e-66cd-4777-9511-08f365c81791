'use client'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import BreadCrumbRouter from '../../../../lib/componentsRouter/BreadCrumbRouter'
import FlexContainer from '../../../Containers/FlexContainer'
import LayoutContainer from '../../../Containers/LayoutContainer'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import BackgroundImage from '../../../Multimedia/Images/BackgroundImage'
import HeroDropdown from '../../HeroDropdown'
import SimpleHero from '../../SimpleHero'
import styles from './index.module.scss'
import { HeroSingleLowerI } from './interface'

export default function HeroSingleLower(props: HeroSingleLowerI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  let className = `${props.htmlAttr?.className} ${styles.singleLowerHeroRoot}`

  const updatedProps: HeroSingleLowerI = {
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: `${className} ${props.htmlAttr?.className ?? ''}`,
    },
    contextualInformation: {
      ...props.contextualInformation,
    },
  }

  let overlay = <></>

  if (!updatedProps.isLightMode) {
    updatedProps.htmlAttr.className += `${styles.hasDarkBg}`
  }

  if (updatedProps.isHeroOverlay) {
    overlay = <div className={'imageOverlay bo1n1d'}></div>
  }

  const { size } = useWindowSize()
  return (
    <SimpleHero isFullXBleed {...updatedProps}>
      {overlay}
      <BackgroundImage
        alt={updatedProps.bgImage?.alt}
        src={updatedProps.bgImage?.src}
        htmlAttr={{
          className: updatedProps.isBgImageEffect ? 'banim1' : '', // Add heroSingleBgImage class if isBgImageEffect is true
        }}
      />
      <LayoutContainer
        htmlAttr={{
          className: `${styles.heroSingleLowerLayout}`,
        }}
      >
        <FlexContainer alignItems={size == 'large' ? 'end' : 'inherit'}>
          <div
            className={`${styles.contentBox} ${updatedProps.contextualInformation?.htmlAttr?.className}`}
          >
            <div className={styles.dropbox}>
              {updatedProps.showDropdown === true ? (
                <HeroDropdown {...updatedProps} htmlAttr={styles.dropdown} />
              ) : updatedProps.showBreadcrumb === true ? (
                <BreadCrumbRouter
                  {...updatedProps.breadcrumbs}
                  isLightMode={false}
                />
              ) : (
                ''
              )}
              <ContextualInformation
                heading={{
                  textContent:
                    updatedProps.contextualInformation?.heading?.textContent,
                  as: 'h1',
                  htmlAttr: {
                    ...updatedProps.contextualInformation?.heading?.htmlAttr,
                    className: `${styles.text}`,
                  },
                }}
                excerpt={null}
                subHeading={{
                  textContent:
                    updatedProps.contextualInformation?.subHeading?.textContent,
                  htmlAttr: {
                    ...updatedProps.contextualInformation?.heading?.htmlAttr,
                    className: `${styles.text}`,
                  },
                }}
                showButtons={false}
              />
            </div>

            {/* Right div */}
            <div className={`${styles.contentInfo}`}>
              <ContextualInformation
                {...updatedProps.contextualInformation}
                subHeading={null}
                heading={{
                  textContent: undefined,
                  htmlAttr: {
                    className: `${styles.heading} ${
                      props.htmlAttr?.className ?? ''
                    }`,
                  },
                }}
                excerpt={{
                  ...updatedProps.contextualInformation?.excerpt,
                  htmlAttr: {
                    className: `${
                      updatedProps.showDropdown === true
                        ? styles.excerpt1
                        : styles.excerpt
                    }`,
                  },
                }}
                showButtons={updatedProps.contextualInformation?.showButtons}
                htmlAttr={{
                  ...updatedProps.contextualInformation?.htmlAttr,
                  className: `${styles.contextualInfo} `,
                }}
              />
            </div>
          </div>
        </FlexContainer>
      </LayoutContainer>
    </SimpleHero>
  )
}
