.heroUpperContent {
  display: flex;
  gap: 100px;
  //position: relative;

  .heroRichtext {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 50%;
    padding-top: 220px;
    }

  /*  .image {
    position: absolute;
    top: 60px;
    right: 0;
    display: flex;
    width: 50%;
    justify-content: end;

    img {
      object-fit: fill !important;
    }
  } */

  @media (max-width: $mScreenSize) {
    .heroRichtext {
      width: 100%;
      padding-top: 160px;
      }
    }

  @media (max-width: $smScreenSize) {
    .heroRichtext {
      width: 100%;
      padding-top: 60px;
      }

    .image {
      width: 100%;
      }
    }
  }

.heroLowerContent {
  }

.heroRoot {
  position: relative;
  width: 100%;
  height: fit-content;
  overflow: hidden;
  }

.bgVideo {
  position: absolute;
  clip-path: polygon(0 -5%, 100% 5%, 100% 95%, 0 95%);
  top: -60px;
  left: 0;
  right: 0;
  width: 100vw;
  height: auto;
  aspect-ratio: 16 / 9;
  z-index: -1;
  pointer-events: none;
  object-fit: cover;

  @media (max-width: 1450px) {
    width: auto;
    height: 100vh;
    }
  }