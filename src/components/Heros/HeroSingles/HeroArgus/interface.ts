import { ReactNode } from 'react'
import { SimpleButtonWIconI } from '../../../CTAs/SimpleButtonWIcon/interface'
import { RichtextI } from '../../../ContentBlocks/Richtext/interface'
import { SimpleImageI } from '../../../Multimedia/Images/SimpleImage/interface'
import { SimpleHeroSingleI } from '../SimpleHeroSingle/interface'

export interface HeroArgusI extends SimpleHeroSingleI {
  button?: SimpleButtonWIconI
  RichText?: RichtextI
  image?: SimpleImageI
  afs?: ReactNode
  videoId?: string
  //...enlist other

}
