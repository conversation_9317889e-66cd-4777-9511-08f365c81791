'use client'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import LayoutContainer from '../../../Containers/LayoutContainer'
import Richtext from '../../../ContentBlocks/Richtext'
import SimpleButtonWIcon from '../../../CTAs/SimpleButtonWIcon'
import SimpleHero from '../../SimpleHero'
import styles from './index.module.scss'
import { HeroArgusI } from './interface'

export default function HeroArgus(props: HeroArgusI) {
  let classname = ''

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: HeroArgusI = {
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: `${classname} ${styles.heroRoot}`,
    },
  }
  const { size } = useWindowSize()
  return (
    <SimpleHero {...updatedProps} isFullXBleed>
      {updatedProps.videoId && (
        <iframe
          className={styles.bgVideo}
          title={'Background video for Argus hero'}
          src={`https://www.youtube.com/embed/${updatedProps.videoId}?rel=0&autoplay=1&mute=1&controls=0&loop=1&playlist=${updatedProps.videoId}`}
        ></iframe>
      )}
      <LayoutContainer>
        <div className={`${styles.heroUpperContent} pb4`}>
          <div className={`${styles.heroRichtext}`}>
            <Richtext {...updatedProps?.RichText} />
            <SimpleButtonWIcon {...updatedProps?.button} />
          </div>
        </div>

        <div
          className={`${styles.heroLowerContent} ${size === 'small' ? 'bleedf' : ''} pb1`}
        >
          {' '}
          {updatedProps?.afs}
        </div>
      </LayoutContainer>
    </SimpleHero>
  )
}