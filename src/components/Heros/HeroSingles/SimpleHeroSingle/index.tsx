'use client'
import { COLOURS } from '../../../../globals/types'
import BreadCrumbRouter from '../../../../lib/componentsRouter/BreadCrumbRouter'
import { showAnyOneField } from '../../../../lib/propsMapping/hero.mapping'
import LayoutContainer from '../../../Containers/LayoutContainer'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import BackgroundImage from '../../../Multimedia/Images/BackgroundImage'
import HeroDropdown from '../../HeroDropdown'
import SimpleHero from '../../SimpleHero'
import HeroDownArrow from '../HeroDownArrow'
import { SimpleHeroSingleD } from './defaults'
import styles from './index.module.scss'
import { SimpleHeroSingleI } from './interface'

export default function SimpleHeroSingle(props: SimpleHeroSingleI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  let className = `${styles.heroRoot} ${props.bgColor}`
  let heroBgImage = <></>

  const colorClass: COLOURS = `${props.isLightMode ? 'cp1' : 'cs2'}`

  const updatedProps: SimpleHeroSingleI = {
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: `${className} ${props.htmlAttr?.className} hero`,
      style: {
        minHeight: `${props.height}`,
        height: `${props.Heroheight}`,
      },
    },
    contextualInformation: {
      ...SimpleHeroSingleD.contextualInformation,
      ...props.contextualInformation,
      subHeading: {
        ...props.contextualInformation?.subHeading,
        colour: colorClass,
      },
    },
  }

  // debugger
  // if no bgColor specified then apply hero background image and overlay
  if (!updatedProps.bgColor) {
    {
      updatedProps.isBgImageEffect
        ? (heroBgImage = (
            <>
              <div className={`imageOverlay bo1n1d`}></div>
              <BackgroundImage
                {...updatedProps.bgImage}
                htmlAttr={{ className: 'banim1' }}
              />
            </>
          ))
        : (heroBgImage = (
            <>
              <div className={`imageOverlay bo1n1d`}></div>
              <BackgroundImage {...updatedProps.bgImage} />
            </>
          ))
    }
  }

  showAnyOneField(updatedProps)

  return (
    <SimpleHero {...updatedProps} isFullXBleed>
      {updatedProps.children}

      {updatedProps.showDownArrow === true ? (
        <HeroDownArrow
          scrollDuration={5000}
          isLightMode={updatedProps.isLightMode}
        />
      ) : undefined}

      {heroBgImage}

      <LayoutContainer
        htmlAttr={{
          className: styles.contextualInfo,
        }}
      >
        {/* {updatedProps.showDropdown ? (
          <HeroDropdown {...updatedProps} htmlAttr={styles.dropdown} />
        ) : (
          <BreadCrumbRouter {...updatedProps.breadcrumbs} isLightMode={false} />
        )} */}
        {updatedProps.showDropdown === true ? (
          <HeroDropdown {...updatedProps} htmlAttr={styles.dropdown} />
        ) : updatedProps.showBreadcrumb === true ? (
          <BreadCrumbRouter
            {...updatedProps.breadcrumbs}
            isLightMode={updatedProps.isLightMode}
          />
        ) : (
          ''
        )}
        <ContextualInformation
          {...updatedProps.contextualInformation}
          heading={{
            ...updatedProps.contextualInformation?.heading,
            htmlAttr: { className: colorClass },
          }}
          excerpt={{
            ...updatedProps.contextualInformation?.excerpt,
            htmlAttr: { className: `${styles.excerpt} ${colorClass} fs5` },
          }}
        />
      </LayoutContainer>
    </SimpleHero>
  )
}
