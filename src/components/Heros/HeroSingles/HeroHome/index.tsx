'use client'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import BreadCrumbRouter from '../../../../lib/componentsRouter/BreadCrumbRouter'
import FlexContainer from '../../../Containers/FlexContainer'
import LayoutContainer from '../../../Containers/LayoutContainer'
import ContextualInformation from '../../../ContentBlocks/ContextualInformation'
import SimpleHeading from '../../../ContentBlocks/Texts/Headings/SimpleHeading'
import BackgroundImage from '../../../Multimedia/Images/BackgroundImage'
import HeroDropdown from '../../HeroDropdown'
import SimpleHero from '../../SimpleHero'
import styles from './index.module.scss'
import { HeroHomeI } from './interface'

export default function HeroHome(props: HeroHomeI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  let className = `${props.htmlAttr?.className} ${styles.singleLowerHeroRoot}`

  const updatedProps: HeroHomeI = {
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: `${className} ${props.htmlAttr?.className ?? ''}`,
      style: {
        minHeight: `${props.height}`,
        height: `${props.Heroheight}`,
      },
    },
    contextualInformation: {
      ...props.contextualInformation,
    },
  }

  if (!updatedProps.isLightMode) {
    updatedProps.htmlAttr.className += `${styles.hasDarkBg}`
  }

  const { size } = useWindowSize()
  return (
    <SimpleHero isFullXBleed {...updatedProps}>
      <div className={'imageOverlay bo1n1d'}></div>
      <BackgroundImage
        alt={updatedProps.bgImage?.alt}
        src={updatedProps.bgImage?.src}
        htmlAttr={{
          className: updatedProps.isBgImageEffect ? 'banim1' : '', // Add heroSingleBgImage class if isBgImageEffect is true
        }}
      />
      <LayoutContainer
        htmlAttr={{
          className: `${styles.HeroHomeLayout}`,
        }}
      >
        <FlexContainer alignItems={size == 'large' ? 'end' : 'inherit'}>
          <div
            className={`${styles.contentBox} ${updatedProps.contextualInformation?.htmlAttr?.className}`}
          >
            <div className={styles.dropbox}>
              {updatedProps.showDropdown === true ? (
                <HeroDropdown {...updatedProps} htmlAttr={styles.dropdown} />
              ) : updatedProps.showBreadcrumb === true ? (
                <BreadCrumbRouter
                  {...updatedProps.breadcrumbs}
                  isLightMode={false}
                />
              ) : (
                ''
              )}
              <SimpleHeading
                textContent={
                  updatedProps.contextualInformation?.heading?.textContent
                }
                as={'h1'}
                htmlAttr={{
                  ...updatedProps.contextualInformation?.heading?.htmlAttr,
                  className: `${styles.text}`,
                }}
              />
            </div>
            <div className={`${styles.contentInfo}`}>
              <ContextualInformation
                {...updatedProps.contextualInformation}
                heading={{
                  textContent: undefined,
                  htmlAttr: {
                    className: `${styles.heading} ${
                      props.htmlAttr?.className ?? ''
                    }`,
                  },
                }}
                subHeading={undefined}
                showButtons={true}
                excerpt={{
                  ...updatedProps.contextualInformation?.excerpt,
                  htmlAttr: {
                    className: `${
                      updatedProps.showDropdown === true
                        ? styles.excerpt1
                        : styles.excerpt
                    }`,
                  },
                }}
                htmlAttr={{
                  ...updatedProps.contextualInformation?.htmlAttr,
                  className: `${styles.contextualInfo} `,
                }}
              />
            </div>
          </div>
        </FlexContainer>
      </LayoutContainer>
    </SimpleHero>
  )
}
