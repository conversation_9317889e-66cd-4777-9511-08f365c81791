'use client'

import { useWindowSize } from '../../../hooks/useWindowSize'
import CardGenericHeadline from '../../Cards/CardGeneric/CardGenericHeadline'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import SimpleFeaturedRows from '../SimpleFeaturedRows'
import styles from './index.module.scss'
import { FeaturedRowsHomeI } from './interface'

export default function FeaturedRowsHome(props: FeaturedRowsHomeI) {
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */

  const updatedProps: FeaturedRowsHomeI = {
    // ...FeaturedRowsHomeD,
    ...props,
  }
  let themeColor = `${updatedProps.isLightMode ? styles.light : styles.dark}`
  let headingCOlor = `${updatedProps.isLightMode ? 'cp1' : ' cs2'}`

  const { size } = useWindowSize()

  const cardHome = updatedProps?.CardHome?.map((item, i) => (
    <CardGenericHeadline
      key={i}
      {...item}
      htmlAttr={{ className: styles.card }}
      isLightMode={updatedProps?.isLightMode}
    />
  ))

  return (
    <SimpleFeaturedRows
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: `${themeColor}`,
      }}
    >
      <div className={styles.FeaturedHomeRoot}>
        <div className={styles.headingRoot}>
          <SimpleHeading
            {...updatedProps.heading}
            as='h2'
            htmlAttr={{ className: `${styles.heading} ${headingCOlor}` }}
          />
          {size === 'large' && (
            <SimpleButtonWIcon
              {...updatedProps.button}
              isLightMode={updatedProps?.isLightMode}
            />
          )}
        </div>
        <div className={styles.cardContainer}>{cardHome}</div>
        {size !== 'large' && (
          <SimpleButtonWIcon
            {...updatedProps.button}
            htmlAttr={{ className: styles.button2 }}
            isLightMode={updatedProps?.isLightMode}
          />
        )}
      </div>
    </SimpleFeaturedRows>
  )
}
