//Default SCSS variables are prepended in next.config.js

.light {
  text-decoration: none;
  color: $cn8;
}

.dark {
  text-decoration: none;
  color: $cs2;
}

// .progressBarStyle{
//   width: 70px;
// }
.carouselContainerRoot {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  overflow-x: clip;

  .carouselContainer {
    width: 100%;

    .indicatorsPositionLeft {
      /* Add styles for left-aligned items */
      .carousel {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .carousel {
      display: flex;
      width: 100%;
      height: 100%;
      transition: transform 0.5s ease;
    }

    .carouselItem {
      flex: 0 0 100%;
      width: 100%;

      transition: transform 0.5s ease;

      &.active {
      }
    }
  }

  // This is currently not in use
  .carouselIconsLeft {
    position: absolute;
    left: 30px;
    bottom: 50%;
    transform: translateY(50%);
    height: auto;
    width: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .carouselIcon {
      width: 5px;
      height: 50px;
      margin: 6px 0;
      font-size: $fs4;
      border-radius: 5px;
      cursor: pointer;
      width: 50px;
      rotate: 90deg;

      &:hover {
        width: 5px;
      }

      @media (max-width: $smScreenSize) {
        &:hover {
          width: 5px;
        }
      }

      @media (max-width: $mScreenSize) {
        &:hover {
          width: 5px;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      left: 20px;
    }
  }

  .carouselIconsRight {
    position: absolute;
    right: 50px;
    bottom: 50%;
    transform: translateY(50%);
    height: auto;
    width: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .carouselIcon {
      width: 50px;
      height: 5px;
      margin: 30px 0;
      font-size: $fs4;
      border-radius: 5px;
      cursor: pointer;
      rotate: 90deg;

      &:hover {
        height: 5px;
      }

      @media (max-width: $mScreenSize) {
        &:hover {
          height: 5px;
        }
      }

      @media (max-width: $smScreenSize) {
        &:hover {
          height: 5px;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      right: 10px;
    }
  }

  // This is currently not in use
  .carouselIconsTop {
    position: absolute;
    left: 50%;
    top: 50px;
    transform: translateX(-50%);
    height: 10px;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;

    .carouselIcon {
      height: 5px;
      width: 50px;
      cursor: pointer;
      margin: 0 6px;
      border-radius: 5px;

      &:hover {
        height: 10px;
      }

      @media (max-width: $smScreenSize) {
        &:hover {
          height: 5px;
        }
      }

      @media (max-width: $mScreenSize) {
        &:hover {
          height: 5px;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      top: 30px;
    }
  }

  .carouselIconsBottom {
    position: absolute;
    left: 50%;
    bottom: 50px;
    transform: translateX(-50%);
    width: auto;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 30px;

    .carouselIcon {
      height: 5px;
      width: 50px;
      cursor: pointer;
      margin: 0 6px;
      border-radius: 5px;

      &:hover {
        height: 10px;
      }

      @media (max-width: $smScreenSize) {
        &:hover {
          height: 5px;
        }
      }

      @media (max-width: $mScreenSize) {
        &:hover {
          height: 5px;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      bottom: 30px;
      padding-bottom: 0px;
    }

    @media (max-width: $mScreenSize) {
      bottom: 0px;
    }
  }

  .carouselIconsInvisible {
    position: absolute;
    left: 50%;
    bottom: 50px;
    transform: translateX(-50%);
    width: auto;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 30px;
    display: none;

    .carouselIcon {
      height: 5px;
      width: 50px;
      cursor: pointer;
      margin: 0 6px;
      border-radius: 5px;
      display: none;

      &:hover {
        height: 10px;
      }

      @media (max-width: $smScreenSize) {
        &:hover {
          height: 5px;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      bottom: 30px;
      padding-bottom: 0px;
    }
  }

  .carouselIconsNormal {
    position: absolute;
    left: 50%;
    bottom: 50px;
    transform: translateX(-50%);
    width: auto;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    .carouselIcon {
      height: 5px;
      width: 50px;
      cursor: pointer;
      margin: 0 6px;
      border-radius: 5px;

      &:hover {
        height: 5px;
      }

      @media (max-width: $smScreenSize) {
        &:hover {
          height: 5px;
        }
      }

      @media (max-width: $mScreenSize) {
        &:hover {
          height: 5px;
        }
      }
    }

    @media (max-width: $smScreenSize) {
      bottom: 30px;
    }

    @media (max-width: $mScreenSize) {
      position: absolute;
      left: 50%;
      bottom: 50px;
      transform: translateX(-50%);
      width: auto;
      height: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .progressBarContainer {
    display: flex;
    // max-width: 1330px;
    width: 100%;
    align-items: center;
    justify-content: center;
    padding-bottom: 30px;
  }

  // .progressMainBar{
  //   width: 25%;
  //   }

  .prevButton,
  .nextButton {
    position: absolute;
    font-size: 320px;
    cursor: pointer;
    background: transparent;
    border: none;
    top: 37%;
    transform: translateY(-50%);
    z-index: 222222;
    width: 33%;
    color: transparent;

    @media (max-width: $smScreenSize) {
      font-size: 200px;
      width: 5%;
      top: 30%;
    }
  }

  .prevButton {
    left: 10px;
  }

  .nextButton {
    right: 10px;
  }
}

.headingList {
  display: flex;
  max-width: 600px;
  width: 100%;
  justify-content: space-around;
  align-items: center;
}

.logos {
  display: flex;
  justify-content: space-around;

  width: 100%;
  gap: 5px;
}

.logosSingle {
  filter: invert(47%) sepia(86%) saturate(0%) hue-rotate(244deg) brightness(84%)
    contrast(114%);
}

.logosSinglefiltered {
  filter: none;
}

.progressIndicatorStyle {
  border-radius: 5px;
}

.tabProgressBarContainer {
  display: flex;
  max-width: 600px;
  width: 100%;
  align-items: center;
  justify-content: center;
  position: absolute;
  // bottom: -90px;
  bottom: 0;
  // padding: 0 0 15px 0;
  backdrop-filter: blur(1rem);
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;

  .TabList {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 0 0 15px 0;
    cursor: pointer;

    .innerProgressContainer {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
    }

    .tabHeading {
      font-family: $fSansBld;
      font-size: $fs2;
      padding-top: 10px;
    }
  }
}

.heroSoftwaretabsContainer {
  display: flex;
  max-width: 600px;
  width: 100%;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  backdrop-filter: blur(1rem);
  background: rgba(10, 44, 55, 0.2);
  border-radius: 5px;

  .heroSoftwaretabsList {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 10px 5px;
    cursor: pointer;
    border-radius: 5px;
    margin: 8px;

    @media screen and (max-width: $smScreenSize) {
      margin: 5px;
      padding: 5px;
    }

    .heroSofTabinnerProgContainer {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
    }

    .heroSofTabHeading {
      font-family: $fSansBld;
      font-size: $fs2;
    }
  }

  .tab {
    p {
      color: $cn3;
    }
  }

  .activeTab {
    background: $cs2;

    p {
      color: $cn1;
    }
  }
}
