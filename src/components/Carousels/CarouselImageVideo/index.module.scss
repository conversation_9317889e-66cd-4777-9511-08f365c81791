//Default SCSS variables are prepended in next.config.js
.mainContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 50px;
}

.layout {
  position: relative;

  .prevIcon {
    opacity: 0;
    height: 40px;
    min-width: auto;
    width: 40px;
    position: absolute;
    border-radius: 50%;
    cursor: pointer;
    background-color: $cn5;
    transition:
      transform 0.5s ease-in,
      opacity 0.7s cubic-bezier(0.15, 0, 0.2, 1) 0.1s;

    &:hover {
      background-color: $cn4;
      transition: 0.25s ease;
      opacity: 1;
    }
  }

  .nextIcon {
    opacity: 0;
    height: 40px;
    min-width: auto;
    width: 40px;
    position: absolute;
    border-radius: 50%;
    cursor: pointer;
    background-color: $cn5;
    transition:
      transform 0.5s ease-in,
      opacity 0.7s cubic-bezier(0.15, 0, 0.2, 1) 0.1s;

    &:hover {
      background-color: $cn4;
      transition: 0.25s ease;
      opacity: 1;
    }
  }

  .r1 {
    right: 8%;
  }

  .r2 {
    right: -8%;
  }

  .l1 {
    left: 8%;
  }

  .l2 {
    left: -8%;
  }

  &:hover {
    .prevIcon {
      opacity: 1;
    }

    .nextIcon {
      opacity: 1;
    }
  }
}

.indexContainer {
  width: 100%;
  overflow: hidden;
}

.theHousing {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  -ms-overflow-style: none;
  /* IE 11 */
  scrollbar-width: none;
  /* firefox */

  &::-webkit-scrollbar {
    display: none;
  }
}

.theItem {
  scroll-snap-stop: always;
  scroll-snap-align: center;

  .cardparent {
    padding: 0 40px;
  }

  .card {
    width: 100%;
    padding: 10px;
  }

  @media (max-width: $mScreenSize) {
    .cardparent {
      padding: 0 10px;
    }
  }

  @media (max-width: $smScreenSize) {
    .card {
      width: $minWidth;
    }

    .cardparent {
      display: flex;
      justify-content: center;
      padding: 0;
    }
  }
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;

  .button {
    min-width: auto;
  }
}

.subHeading {
  color: $cp2 !important;
}

.subHeadingDark {
  color: $cs1 !important;
}

.media {
  width: 100%;
}

.description {
  margin-top: 50px;

  @media (max-width: $mScreenSize) {
    margin-top: 25px;
  }
}
