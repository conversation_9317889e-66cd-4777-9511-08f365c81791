import { ReactNode } from 'react'
import { SimpleButtonWIconI } from '../../CTAs/SimpleButtonWIcon/interface'
import { ContextualInformationI } from '../../ContentBlocks/ContextualInformation/interface'
import { RichtextI } from '../../ContentBlocks/Richtext/interface'
import { SimpleHeadingI } from '../../ContentBlocks/Texts/Headings/SimpleHeading/interface'
import { KernelI } from '../../Kernel'

interface CarouselData {
  media?: ReactNode
  mediaHeading?: SimpleHeadingI
  mediaInfo?: RichtextI
}

export interface CarouselImageVideoI extends KernelI {
  //...enlist other
  carouselData?: CarouselData[]
  button?: SimpleButtonWIconI
  contextualInformation?: ContextualInformationI
}
