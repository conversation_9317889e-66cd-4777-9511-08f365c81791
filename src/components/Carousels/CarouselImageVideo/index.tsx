'use client'
import { useEffect, useRef, useState } from 'react'
import { useWindowSize } from '../../../hooks/useWindowSize'
import { SimpleButtonD } from '../../CTAs/SimpleButton/defaults'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import SimpleIconButton from '../../CTAs/SimpleIconButton'
import LayoutContainer from '../../Containers/LayoutContainer'
import ContextualInformation from '../../ContentBlocks/ContextualInformation'
import Richtext from '../../ContentBlocks/Richtext'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import Kernel from '../../Kernel'
import { CarouselImageVideoD } from './defaults'
import styles from './index.module.scss'
import { CarouselImageVideoI } from './interface'

export default function CarouselImageVideo(props: CarouselImageVideoI) {
  const isLightMode = props.isLightMode
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CarouselImageVideoI = {
    ...CarouselImageVideoD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
    },
  }

  const subHeadingColor = isLightMode
    ? styles.subHeading
    : styles.subHeadingDark
  const sliderRef = useRef<HTMLDivElement>(null)
  const mediaRef = useRef<HTMLDivElement>(null)
  const [isOverflowing, setIsOverflowing] = useState<boolean>(false)
  const [scrolledStart, setScrolledStart] = useState<boolean>(false)
  const [scrolledEnd, setScrolledEnd] = useState<boolean>(false)
  const [activeItem, setActiveItem] = useState<number>(0)
  const [controllerPosition, setControllerPosition] = useState<number>(null)
  const { size, width } = useWindowSize()
  //item width
  const itemWidth = size === 'small' ? width : width / 3

  useEffect(() => {
    const slider = sliderRef.current
    const media = mediaRef.current
    if (slider) {
      setIsOverflowing(slider.scrollWidth > slider.clientWidth)
    }
    if (media) {
      const heightOfMedia = media.offsetHeight
      setControllerPosition(heightOfMedia / 2)
    }
  }, [width, updatedProps.carouselData])

  useEffect(() => {
    const slider = sliderRef.current

    // Function to handle scroll event and update active item
    const handleScroll = () => {
      const slider = sliderRef.current
      if (slider) {
        const center = Math.floor(width / 2)
        const items = slider.querySelectorAll(`.${styles.theItem}`)

        items.forEach((item, index) => {
          const itemRect = item.getBoundingClientRect()
          const itemCenterX = Math.floor(itemRect.left + itemRect.width / 2)
          // Check if the item's center is within the viewport's center bounds
          if (itemCenterX <= center) {
            setActiveItem(index)
          }
        })
        const scrollLeft = sliderRef.current.scrollLeft
        const scrollWidth = sliderRef.current.scrollWidth
        const clientWidth = sliderRef.current.clientWidth
        setScrolledEnd(Math.abs(scrollLeft - (scrollWidth - clientWidth)) < 1)
        setScrolledStart(scrollLeft === 0)
      }
    }
    if (slider) {
      slider.addEventListener('scroll', handleScroll)
      // Clean up event listener when component unmounts
      return () => {
        slider.removeEventListener('scroll', handleScroll)
      }
    }
  }, [updatedProps.carouselData, width])

  // Handle left arrow click
  const handlePrevClick = () => {
    const slider = sliderRef.current
    if (slider) {
      scrolledStart || activeItem === 1
        ? slider.scrollTo({
            left: slider.scrollWidth - slider.clientWidth,
            behavior: 'smooth',
          })
        : slider.scrollTo({
            left: slider.scrollLeft - itemWidth,
            behavior: 'smooth',
          })
    }
  }

  // Handle right arrow click
  const handleNextClick = () => {
    const slider = sliderRef.current
    if (slider) {
      // If already at the end, jump to the start
      scrolledEnd || activeItem === updatedProps?.carouselData?.length - 2
        ? slider.scrollTo({
            left: 0,
            behavior: 'smooth',
          })
        : // If not at the end, scroll right by itemWidth
          slider.scrollTo({
            left: slider.scrollLeft + itemWidth,
            behavior: 'smooth',
          })
    }
  }

  const TheItems = (updatedProps?.carouselData ?? []).map((item, index) => {
    const isActive = size === 'small' || index === activeItem

    return (
      <div key={index} className={styles.theItem}>
        <div
          className={`${styles.cardparent}`}
          style={{ width: `${itemWidth + 'px'}` }}
        >
          <div className={styles.card}>
            <div className={styles.media} ref={mediaRef}>
              {item?.media}
            </div>

            <div
              className={styles.description}
              style={{ opacity: `${isActive ? '1' : '0'}` }}
            >
              {item?.mediaHeading && (
                <SimpleHeading
                  {...item?.mediaHeading}
                  as='h6'
                  colour={`${isLightMode ? 'cn2' : 'cs2'}`}
                  htmlAttr={{
                    ...item.mediaHeading?.htmlAttr,
                  }}
                />
              )}
              {item?.mediaInfo?.data && (
                <Richtext
                  {...item?.mediaInfo}
                  htmlAttr={{
                    ...item?.mediaInfo?.htmlAttr,
                    className: `${isLightMode ? 'cn2' : 'cs2'}`,
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    )
  })

  const TheIndicator1 = (
    <SimpleIconButton
      isFormButton
      isLightMode={updatedProps.isLightMode}
      icon={'LeftChevron'}
      // isButton
      variant={'secondary'}
      htmlAttr={{
        className: ` ${styles.prevIcon} ${updatedProps?.isFullXBleed ? styles.l1 : styles.l2}`,
        style: { top: `${controllerPosition + 'px'}` },
        onClick: () => handlePrevClick(),
      }}
    />
  )
  const TheIndicator2 = (
    <SimpleIconButton
      isFormButton
      isLightMode={updatedProps.isLightMode}
      icon={'RightChevron'}
      // isButton
      variant={'secondary'}
      htmlAttr={{
        className: `${styles.nextIcon} ${updatedProps?.isFullXBleed ? styles.r1 : styles.r2}`,
        style: { top: `${controllerPosition + 'px'}` },
        onClick: () => handleNextClick(),
      }}
    />
  )

  const Button = (
    <SimpleButtonWIcon
      {...(updatedProps?.button ?? SimpleButtonD)}
      htmlAttr={{ ...updatedProps.button?.htmlAttr, className: styles.button }}
      isLightMode={isLightMode}
    />
  )

  // ... (existing code)
  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: `${styles.mainContainer} bleedf ${props.htmlAttr?.className}`,
      }}
    >
      {' '}
      <LayoutContainer>
        <div className={styles.headerContainer}>
          <ContextualInformation
            {...updatedProps.contextualInformation}
            isLightMode={isLightMode}
            htmlAttr={{
              ...updatedProps.htmlAttr,
              className: styles.contextualInfoContainer,
            }}
            heading={{
              ...updatedProps.contextualInformation?.heading,
              colour: isLightMode ? 'cp1' : 'cs2',
            }}
            subHeading={{
              ...updatedProps.contextualInformation?.subHeading,
              htmlAttr: {
                ...updatedProps.contextualInformation?.subHeading?.htmlAttr,
                className: subHeadingColor,
              },
            }}
            excerpt={{
              ...updatedProps.contextualInformation?.excerpt,
              htmlAttr: {
                ...updatedProps.contextualInformation?.subHeading?.htmlAttr,
                className: `${isLightMode ? 'cn2' : 'cs2'}`,
              },
            }}
          />
          {size === 'large' ? updatedProps?.button && Button : ''}
        </div>
      </LayoutContainer>
      <LayoutContainer>
        <div
          className={`${styles.layout} ${updatedProps?.isFullXBleed ? 'bleedf' : ''}`}
        >
          <div className={styles.indexContainer}>
            <div className={styles.theHousing} ref={sliderRef}>
              {TheItems}
            </div>
          </div>

          {isOverflowing && TheIndicator1}
          {isOverflowing && TheIndicator2}
        </div>
      </LayoutContainer>
      {size !== 'large' ? updatedProps?.button && Button : ''}
    </Kernel>
  )
}
