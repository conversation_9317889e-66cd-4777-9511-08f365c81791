'use client'
import { useEffect, useRef, useState } from 'react'
import { useWindowSize } from '../../../hooks/useWindowSize'
import LayoutContainer from '../../Containers/LayoutContainer'
import SimpleHeading from '../../ContentBlocks/Texts/Headings/SimpleHeading'
import { SimpleButtonD } from '../../CTAs/SimpleButton/defaults'
import SimpleButtonWIcon from '../../CTAs/SimpleButtonWIcon'
import SimpleIconButton from '../../CTAs/SimpleIconButton'
import Kernel from '../../Kernel'
import { CarouselHomeD } from './defaults'
import styles from './index.module.scss'
import { CarouselHomeI } from './interface'

export default function CarouselHome(props: CarouselHomeI) {
  const { width } = useWindowSize()

  const isLightMode = props.isLightMode
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: CarouselHomeI = {
    ...CarouselHomeD,
    ...props,
  }

  const sliderRef = useRef<HTMLDivElement>(null)
  const [scrolledEndOfDiv, setScrolledEndOfDiv] = useState<boolean>(false)
  const [scrolledToStartOfDiv, setScrolledToStartOfDiv] =
    useState<boolean>(false)
  const [isOverflowing, setIsOverflowing] = useState<boolean>(false)

  const { size } = useWindowSize()
  //item width
  const itemWidth = size === 'small' ? 296 : 400
  const pr = size === 'small' ? 20 : 40

  useEffect(() => {
    const slider = sliderRef.current
    if (slider) {
      setIsOverflowing(slider.scrollWidth > slider.clientWidth)
    }
  }, [width, updatedProps.carouselData])

  const scrollToSlide = (slider: HTMLDivElement, direction: number) => {
    if (!slider) return
    slider.scrollTo({
      left: slider.scrollLeft + direction * itemWidth,
      behavior: 'smooth',
    })
  }

  const handlePrevClick = () => {
    scrollToSlide(sliderRef.current, -1)
  }

  const handleNextClick = () => {
    scrollToSlide(sliderRef.current, +1)
  }

  useEffect(() => {
    function handleScroll() {
      if (!sliderRef.current) return
      const atEnd =
        Math.floor(
          sliderRef.current.scrollWidth -
            sliderRef.current.scrollLeft -
            sliderRef.current.clientWidth -
            pr
        ) <= 0
      const atStart = sliderRef.current.scrollLeft === 0
      setScrolledEndOfDiv(atEnd)
      setScrolledToStartOfDiv(atStart)
    }

    handleScroll() // Calculate initial scroll position

    // Attach scroll event listener
    if (sliderRef.current) {
      sliderRef.current.addEventListener('scroll', handleScroll)
    }

    // Cleanup function
    return () => {
      if (sliderRef.current) {
        sliderRef.current.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  const TheItems = (updatedProps?.carouselData ?? []).map((item, index) => {
    return (
      <div key={index} className={styles.theItem}>
        <div
          className={`${index === updatedProps?.carouselData?.length - 1 && styles.lastCard} ${styles.cardparent}`}
        >
          <div className={styles.card}>{item}</div>
        </div>
      </div>
    )
  })

  const TheIndicator1 = (
    <SimpleIconButton
      isFormButton
      isLightMode={updatedProps.isLightMode}
      icon={'LeftChevron'}
      // isButton
      variant={'secondary'}
      htmlAttr={{
        className: `${scrolledToStartOfDiv && styles.hidden} ${styles.prevIcon}`,
        onClick: () => handlePrevClick(),
      }}
    />
  )
  const TheIndicator2 = (
    <SimpleIconButton
      isFormButton
      isLightMode={updatedProps.isLightMode}
      icon={'RightChevron'}
      // isButton
      variant={'secondary'}
      htmlAttr={{
        className: `${scrolledEndOfDiv && styles.hidden} ${styles.nextIcon}`,
        onClick: () => handleNextClick(),
        ariaLabel: 'Button with icon',
      }}
    />
  )

  const Button = (
    <SimpleButtonWIcon
      {...(updatedProps?.button ?? SimpleButtonD)}
      isLightMode={isLightMode}
    />
  )

  return (
    <Kernel
      {...updatedProps}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: `${styles.mainContainer} bleedf ${props.htmlAttr?.className}`,
      }}
    >
      <div className={styles.indexContainer}>
        <div className={styles.theHousing} ref={sliderRef}>
          {TheItems}
        </div>

        {isOverflowing && TheIndicator1}
        {isOverflowing && TheIndicator2}
      </div>
      <LayoutContainer>
        <div className={styles.description}>
          {size !== 'small' && (
            <SimpleHeading
              {...updatedProps?.description}
              as='h3'
              colour='cp1'
              htmlAttr={{ className: styles.heading }}
            />
          )}
          {updatedProps?.button && Button}
        </div>
      </LayoutContainer>
    </Kernel>
  )
}
