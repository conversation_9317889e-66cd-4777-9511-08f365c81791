'use client'
import { useEffect, useRef, useState } from 'react'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import HeroSingleDisplay from '../../../Heros/HeroSingles/HeroSingleDisplay'
import HeroSingleGeneric from '../../../Heros/HeroSingles/HeroSingleGeneric'
import HeroSingleLower from '../../../Heros/HeroSingles/HeroSingleLower'
import SimpleCarousel from '../../SimpleCarousel'
import { CarouselHeroVerticalD } from './defaults'
import style from './index.module.scss'
import { CarouselHeroVerticalI } from './interface'

export default function CarouselHeroVertical(props: CarouselHeroVerticalI) {
  let className = `${props.isLightMode ? style.light : style.dark} ${
    props.htmlAttr?.className ?? ''
  } `

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const carouselRef = useRef<HTMLDivElement>(null)
  const updatedProps: CarouselHeroVerticalI = {
    ...CarouselHeroVerticalD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: className,
      style: props.htmlAttr?.style,
    },
  }
  const [isCarouselHovered, setIsCarouselHovered] = useState(false)

  const { size } = useWindowSize()
  const handleMouseEnter = () => {
    setIsCarouselHovered(true)
  }

  const handleMouseLeave = () => {
    setIsCarouselHovered(false)
  }
  const timer = isCarouselHovered ? undefined : updatedProps?.timer
  const items = (updatedProps?.carouselData ?? []).map((item, index) => {
    let HeroComponent

    switch (item?.template) {
      case 'SingleDisplay':
        HeroComponent = HeroSingleDisplay
        break
      case 'SingleGeneric':
        HeroComponent = HeroSingleGeneric
        break
      case 'SingleLower':
        HeroComponent = HeroSingleLower
        break
      // Add more cases as needed

      default:
        // Default to a fallback component or handle the case as needed
        HeroComponent = HeroSingleDisplay
        break
    }

    return (
      <HeroComponent
        key={index}
        showDownArrow={false}
        // ... (pass other props as needed)
        heading={{
          ...item?.heading,
          htmlAttr: {
            ...item.contextualInformation?.heading?.htmlAttr,
            className: style.heroHeadingout,
          },
        }}
        contextualInformation={{
          ...item?.contextualInformation,
          htmlAttr: {
            ...item?.contextualInformation?.htmlAttr,
            className: style.heroContextualInfo,
          },
          excerpt: {
            ...item?.contextualInformation?.excerpt,
            htmlAttr: { className: style.heroExcerpt },
          },
          buttons: {
            ...item?.contextualInformation?.buttons,
            htmlAttr: {
              ...item?.contextualInformation?.buttons?.htmlAttr,
              className: style.heroButtonGroup,
            },
          },
        }}
        bgImage={{
          ...item?.bgImage,
        }}
        options={item?.options}
        defaultOption={item?.defaultOption}
        breadcrumbs={item?.breadcrumbs}
        template={item?.template}
        isBgImageEffect={item?.isBgImageEffect}
        showDropdown={item?.showDropdown}
        htmlAttr={item?.htmlAttr}
      />
    )
  })
  // State to track the maximum height of heroes
  const [maxHeroHeight, setMaxHeroHeight] = useState<number>(0)

  useEffect(() => {
    // Calculate the maximum height among all hero components
    const newMaxHeight = Math.max(
      ...Array.from(
        carouselRef.current?.querySelectorAll(`.${style.heroMain}`) ?? []
      ).map((element: HTMLElement) => element.offsetHeight)
    )

    setMaxHeroHeight(newMaxHeight)
  }, [items]) // Re-run the effect when items change

  const itemsWithFixedHeight = items.map((item, index) => {
    let HeroComponent

    switch (item?.props?.template) {
      case 'SingleDisplay':
        HeroComponent = HeroSingleDisplay
        break
      case 'SingleGeneric':
        HeroComponent = HeroSingleGeneric
        break
      case 'SingleLower':
        HeroComponent = HeroSingleLower
        break
      // Add more cases as needed

      default:
        // Default to a fallback component or handle the case as needed
        HeroComponent = HeroSingleDisplay
        break
    }
    // debugger
    return (
      <HeroComponent
        key={index}
        showDownArrow={false}
        // Heroheight={`${maxHeroHeight}px`}
        heading={{
          ...item.props?.heading,
          htmlAttr: {
            ...item.props?.contextualInformation?.heading?.htmlAttr,
            className: style.heroHeadingout,
          },
        }}
        isBgImageEffect={item?.props?.isBgImageEffect}
        contextualInformation={{
          ...item?.props?.contextualInformation,
          htmlAttr: {
            ...item?.props?.contextualInformation?.htmlAttr,
            className: style.heroContextualInfo,
          },
          heading: {
            ...item?.props?.contextualInformation?.heading,
            htmlAttr: { className: style.heroHeading },
          },
          excerpt: {
            ...item?.props?.contextualInformation?.excerpt,
            htmlAttr: {
              className: `${style.heroExcerpt} ${style.heroMainRoot}`,
            },
          },
          buttons: {
            ...item.props?.contextualInformation?.buttons,
            htmlAttr: {
              ...item?.props?.contextualInformation?.buttons?.htmlAttr,
              className: style.heroButtonGroup,
            },
          },
        }}
        htmlAttr={{
          ...item?.props?.htmlAttr,
          className: style.heroMain,
        }}
        bgImage={{
          ...item?.props?.bgImage,
          className: style.heroContextualInfo,
        }}
        options={item?.props?.options}
        defaultOption={item?.props?.defaultOption}
        showDropdown={item?.props?.showDropdown}
        breadcrumbs={item?.props?.breadcrumbs}
      />
    )
  })
  // debugger
  return (
    <SimpleCarousel
      {...updatedProps}
      isFullXBleed
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: `${className} ${style.simpleCarousel}`,
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        style: {
          height: `${
            itemsWithFixedHeight[0]?.props?.template === 'HeroHome'
              ? size === 'small'
                ? 'calc(80vh - 100px)'
                : '80vh'
              : '100vh'
          }`,
        },
      }}
      items={itemsWithFixedHeight}
      // carouselHeight={`${maxHeroHeight}px`}
      ref={carouselRef}
      timer={timer}
    >
      {updatedProps.children}
    </SimpleCarousel>
  )
}
