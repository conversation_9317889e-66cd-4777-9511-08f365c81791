'use client'
import { useEffect, useRef, useState } from 'react'
import { l } from '../../../../globals/utils'
import { useWindowSize } from '../../../../hooks/useWindowSize'
import HeroHome from '../../../Heros/HeroSingles/HeroHome'
import HeroSingleDisplay from '../../../Heros/HeroSingles/HeroSingleDisplay'
import HeroSingleGeneric from '../../../Heros/HeroSingles/HeroSingleGeneric'
import HeroSingleLower from '../../../Heros/HeroSingles/HeroSingleLower'
import SimpleCarousel from '../../SimpleCarousel'
import { CarouselHeroHorizontalD } from './defaults'
import style from './index.module.scss'
import { CarouselHeroHorizontalI } from './interface'

export default function CarouselHeroHorizontal(props: CarouselHeroHorizontalI) {
  let className = `${props.isLightMode ? style.light : style.dark} ${
    props.htmlAttr?.className ?? ''
  } `

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const carouselRef = useRef<HTMLDivElement>(null)
  const updatedProps: CarouselHeroHorizontalI = {
    ...CarouselHeroHorizontalD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
      className: className,
      style: props.htmlAttr?.style,
    },
  }

  const { size } = useWindowSize()

  const [isCarouselHovered, setIsCarouselHovered] = useState(false)
  const handleMouseEnter = () => {
    setIsCarouselHovered(true)
  }

  const handleMouseLeave = () => {
    setIsCarouselHovered(false)
  }

  const timer = isCarouselHovered ? undefined : updatedProps.timer
  const items = (updatedProps?.carouselData ?? []).map((item, index) => {
    let HeroComponent

    switch (item?.template) {
      case 'SingleDisplay':
        HeroComponent = HeroSingleDisplay
        break
      case 'SingleGeneric':
        HeroComponent = HeroSingleGeneric
        break
      case 'SingleLower':
        HeroComponent = HeroSingleLower
      case 'HeroHome':
        HeroComponent = HeroHome
        break
      // Add more cases as needed

      default:
        // Default to a fallback component or handle the case as needed
        HeroComponent = HeroSingleDisplay
        break
    }

    return (
      <HeroComponent
        key={index}
        showDownArrow={false}
        // ... (pass other props as needed)
        heading={{
          ...item?.heading,
          htmlAttr: {
            ...item.contextualInformation?.heading?.htmlAttr,
            className: style.heroHeadingout,
          },
        }}
        contextualInformation={{
          ...item?.contextualInformation,
          htmlAttr: {
            ...item?.contextualInformation?.htmlAttr,
            className: style.heroContextualInfo,
          },
          excerpt: {
            ...item?.contextualInformation?.excerpt,
            htmlAttr: { className: style.heroExcerpt },
          },
          buttons: {
            ...item?.contextualInformation?.buttons,
            htmlAttr: {
              ...item?.contextualInformation?.buttons?.htmlAttr,
              className: style.heroButtonGroup,
            },
          },
        }}
        bgImage={{
          ...item?.bgImage,
        }}
        breadcrumbs={item?.breadcrumbs}
        template={item?.template}
        isBgImageEffect={item?.isBgImageEffect}
        showDropdown={item?.showDropdown}
        htmlAttr={item?.htmlAttr}
        options={item?.options}
        defaultOption={item?.defaultOption}
      />
    )
  })
  // State to track the maximum height of heroes
  const [maxHeroHeight, setMaxHeroHeight] = useState<number>(0)
  // debugger
  useEffect(() => {
    // Calculate the maximum height among all hero components
    const newMaxHeight = Math.max(
      ...Array.from(
        carouselRef.current?.querySelectorAll(`.${style.heroContextualInfo}`) ??
          []
      ).map((element: HTMLElement) => element.offsetHeight)
    )

    // Update the maximum height state
    setMaxHeroHeight(newMaxHeight)
  }, [items]) // Re-run the effect when items change
  const itemsWithFixedHeight = items.map((item, index) => {
    let HeroComponent

    switch (item?.props?.template) {
      case 'SingleDisplay':
        HeroComponent = HeroSingleDisplay
        break
      case 'SingleGeneric':
        HeroComponent = HeroSingleGeneric
        break
      case 'SingleLower':
        HeroComponent = HeroSingleLower
        break
      case 'HeroHome':
        HeroComponent = HeroHome
        break
      // Add more cases as needed

      default:
        // Default to a fallback component or handle the case as needed
        HeroComponent = HeroSingleDisplay
        break
    }
    // debugger

    return (
      <HeroComponent
        key={index}
        showDownArrow={false}
        Heroheight={`${item?.props?.template === 'HeroHome' ? '' : `${maxHeroHeight}px`}`}
        heading={{
          ...item.props?.heading,
          htmlAttr: {
            ...item.props?.contextualInformation?.heading?.htmlAttr,
            className: style.heroHeadingout,
          },
        }}
        isBgImageEffect={item?.props?.isBgImageEffect}
        contextualInformation={{
          ...item?.props?.contextualInformation,
          htmlAttr: {
            ...item?.props?.contextualInformation?.htmlAttr,
            className: style.heroContextualInfo,
          },
          excerpt: {
            ...item?.props?.contextualInformation?.excerpt,
            htmlAttr: { className: style.heroExcerpt },
          },
          buttons: {
            ...item.props?.contextualInformation?.buttons,
            htmlAttr: {
              ...item?.props?.contextualInformation?.buttons?.htmlAttr,
              className: style.heroButtonGroup,
            },
          },
        }}
        htmlAttr={{ ...item?.props?.htmlAttr, className: style.heroMain }}
        showDropdown={item?.props?.showDropdown}
        breadcrumbs={item?.props?.breadcrumbs}
        template={item?.props?.template}
        bgImage={{
          ...item?.props?.bgImage,
        }}
        options={item?.props?.options}
        defaultOption={item?.props?.defaultOption}
      />
    )
  })
  l('heroHome', itemsWithFixedHeight[0])

  return (
    <SimpleCarousel
      {...updatedProps}
      isFullXBleed
      indicatorsPosition={'Normal'}
      indicatorActiveColour={'bs2'}
      indicatorInactiveColour={
        itemsWithFixedHeight[0]?.props?.template === 'HeroHome'
          ? 'bgTransparent'
          : 'bn3'
      }
      isProgressBar={false}
      isLogoProgress={false}
      isTabProgress={itemsWithFixedHeight[0]?.props?.template === 'HeroHome'}
      htmlAttr={{
        ...updatedProps.htmlAttr,
        className: `${className} ${style.simpleCarousel}`,
        style: {
          height: `${
            itemsWithFixedHeight[0]?.props?.template === 'HeroHome'
              ? size === 'small'
                ? 'calc(80vh - 100px)'
                : '80vh'
              : '100vh'
          }`,
        },
        onMouseEnter:
          itemsWithFixedHeight[0]?.props?.template === 'HeroHome'
            ? undefined
            : handleMouseEnter,
        onMouseLeave:
          itemsWithFixedHeight[0]?.props?.template === 'HeroHome'
            ? undefined
            : handleMouseLeave,
      }}
      items={itemsWithFixedHeight}
      ref={carouselRef}
      timer={timer}
    >
      {updatedProps.children}
    </SimpleCarousel>
  )
}
