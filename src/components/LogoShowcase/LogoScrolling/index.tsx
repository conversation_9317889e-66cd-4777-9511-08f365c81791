'use client'
import Kernel from '../../Kernel'
import Logo from '../../Multimedia/Images/GenericLogo'
import { LogoScrollingD } from './defaults'
import styles from './index.module.scss'
import { LogoScrollingI } from './interface'

export default function LogoScrolling(props: LogoScrollingI) {
  const isLightMode = props.isLightMode
  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: LogoScrollingI = {
    ...LogoScrollingD,
    ...props,
    htmlAttr: {
      ...props.htmlAttr,
    },
  }
  const TheItems = (updatedProps?.Logos ?? []).map((item, index) => {
    return (
      <div key={index} className={styles.slide}>
        <Logo {...item} />
      </div>
    )
  })

  return (
    <Kernel {...updatedProps} htmlAttr={{ className: styles.logos }}>
      <div className={styles.logosSlide}>{TheItems}</div>
      <div className={styles.logosSlide}>{TheItems}</div>
    </Kernel>
  )
}
