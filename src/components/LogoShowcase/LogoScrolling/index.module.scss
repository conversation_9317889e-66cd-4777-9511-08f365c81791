@keyframes slide {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

.logos {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
}

.logos:before,
.logos:after {
  position: absolute;
  top: 0;
  width: 10%;
  height: 100%;
  content: '';
  z-index: 1;
}

.logos:before {
  left: -1px;
  background: linear-gradient(to left, rgba(255, 255, 255, 0), white);
}

.logos:after {
  right: -1px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
}

.logos:hover .logosSlide {
  animation-play-state: paused;
}

.logosSlide {
  display: inline-flex;
  animation: slide 30s infinite linear;
}

.slide {
  margin: 0 40px;
}

@media screen and (max-width: $smScreenSize) {
  .slide {
    margin: 0 20px;
  }
}
