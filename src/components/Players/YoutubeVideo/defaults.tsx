import { KernelD } from '../../Kernel'
import { YoutubeVideoI } from './interface'

export const YoutubeVideoD: YoutubeVideoI = {
  ...KernelD,
  _conf: {
    ...KernelD._conf,
    name: 'YoutubePlayer',
    genus: '',
    status: 'DEV',
  },
  htmlAttr: {
    style: {
      aspectRatio: '1.77 / 1',
      width: '100%',
      // height: '100%',
    },
  },
  as: 'div',
  /**
   *@todo ...specify prop values for all the corresponding props specified in the SimpleNotificationCompI
   */
  videoId: 'wbkxtcA8Sq4',
}
