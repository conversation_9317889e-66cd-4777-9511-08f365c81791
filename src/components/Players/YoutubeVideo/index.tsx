'use client'
import React, {useEffect, useRef} from 'react'
import Kernel from '../../Kernel'

import {YoutubeVideoD} from './defaults'
import {YoutubeVideoI} from './interface'

/**
 * @todo Describe/explain/provide information about this component over here
 */
export default function YoutubeVideo(props: YoutubeVideoI) {
  let as: YoutubeVideoI['as'] = props.as
  // @ts-ignore
  let htmlAttr: React.ComponentProps<typeof props.as> = {}

  if (props) {
    /**
     * @todo add custom logic if required otherwise remove this block
     */
  }

  /**
   * @todo Add any logic and evaluation code over here
   */

  /**
   * updatedProps prepends the default values for the props of a component to the custom props provided and serve as the single source of props.
   * This way we don't need to keep providing the minimum defaults everytime a component is added
   * and plus we don't end up mutating the originally provided props.
   * @summary Unless you have a very good reason not to, use updatedProps where ever possible.
   */
  const updatedProps: YoutubeVideoI = {
    ...YoutubeVideoD,
    ...props,
    as,
    htmlAttr: {
      ...YoutubeVideoD.htmlAttr,
      ...htmlAttr,
      ...props.htmlAttr,
    },
  }

  const iframeRef = useRef<HTMLIFrameElement>(null)
  const isMuted = !updatedProps.videoSettings?.includes('Enable sound')
  const isAutoplay = updatedProps.videoSettings?.includes('Enable autoplay')
  const isLoop = updatedProps.videoSettings?.includes('Enable loop')
  const isCaption = updatedProps.videoSettings?.includes('Enable CC')

  let paramObj: Record<string, string> = {
    // autoplay: isAutoplay ? '1' : '0',
    rel: '0',
    mute: isMuted || isAutoplay ? '1' : '0',
    // controls: controls ? '1' : '0',
    loop: isLoop ? '1' : '0',
    cc_load_policy: isCaption ? '1' : '0',
    // start: start ? start.toString() : '',
    // end: end ? end.toString() : '',
  }

  if (isLoop) {
    paramObj['playlist'] = updatedProps.videoId
  }

  const youtubeParams = new URLSearchParams(paramObj)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (isAutoplay && entry.isIntersecting) {
            if (
              iframeRef?.current?.src &&
              !iframeRef?.current?.src.includes('autoplay=1')
            ) {
              iframeRef.current.src += '&autoplay=1'
            }
          }
        })
      },
      { threshold: 0.0 } // Adjust the threshold as needed
    )

    setTimeout(() => {
      if (isAutoplay && iframeRef.current) {
        observer.observe(iframeRef.current)
      }
    }, 1000)

    return () => {
      if (isAutoplay && iframeRef.current) {
        observer.unobserve(iframeRef.current)
      }
    }
  }, [])

  /**
   * The return block should only return the actual UI component.
   * Make sure any logic/evaluation has been computed prior and the values updated in updatedProps.
   * There should be no computation/evaluation in the return block.
   * For Simpletons, return Kernel only.
   */
  return (
    <Kernel {...updatedProps}>
      <iframe
        ref={iframeRef}
        src={`https://www.youtube.com/embed/${updatedProps.videoId}?${youtubeParams.toString()}`}
        title='YouTube video player'
        style={{ border: 'none', width: '100%', height: '100%' }}
        allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'
        allowFullScreen
      ></iframe>
    </Kernel>
  )
}