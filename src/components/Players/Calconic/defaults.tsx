import { KernelD } from '../../Kernel'
import { CalconicI } from './interface'

export const CalconicD: CalconicI = {
  ...KernelD,
  _conf: {
    ...KernelD._conf,
    name: 'Calconic',
    genus: '',
    status: 'DEV',
  },
  htmlAttr: {
    style: {
      aspectRatio: '1.77 / 1',
      width: '100%',
      height: '100%',
    },
  },
  as: 'div',
  /**
   *@todo ...specify prop values for all the corresponding props specified in the SimpleNotificationCompI
   */
  calculatorId: '6425160d2997df001fe71b8d',
}
