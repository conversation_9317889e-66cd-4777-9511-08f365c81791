'use client'

import React, { ReactNode, useEffect, useState } from 'react'
import { extractErroredComponentFilePathAndLineNumber } from '../../../../globals/utils'
import styles from './index.module.scss'

type ErrorProps = {
  error: Partial<Error>
}

/**
 * `TheError` is the client side HOC wrapper that encapsulates every component with an ErrorBoundary by default and renders its final HTML node when `isUiErrorDisabled` is false.
 * Note - If you are enabling `isUiErrorDisabled`, make sure to wrap your component within a custom ErrorBoundary otherwise the app won't crash, but the page components will not render either.
 * When `isUiRemoved` is true or there is no content to render, `isUiErrorDisabled` will be enabled automatically.
 * Do not use directly. To throw custom errors, use `IntentionalError`.
 * @param {Error} error
 * @returns {React.ReactNode}
 * @constructor
 * @uses [React ErrorBoundary]{@link https://www.npmjs.com/package/react-error-boundary}
 * @see IntentionalError
 */

interface FileErrorObj {
  filePath: string
  lineNumber: string
  columnNumber: string
}

export default function TheError({ error }: ErrorProps): ReactNode {
  const [errorFile, setErrorFile] = useState<FileErrorObj>({} as FileErrorObj)

  useEffect(() => {
    setErrorFile(
      extractErroredComponentFilePathAndLineNumber(error.stack || '')
    )
  }, [error])

  useEffect(() => {
    if (errorFile) console.log('errorFile: ', errorFile)
  }, [errorFile])

  // const [showTechnicalInfo, setShowTechnicalInfo] = useState(false)

  // const toggleTechnicalInfo = () => {
  //   setShowTechnicalInfo(!showTechnicalInfo)
  // }

  return (
    <div role='alert' className={`${styles.rootContainer} shadow1`}>
      Something went wrong.
      {/* 
            {showTechnicalInfo && (
                <div className={styles.technicalInfo}>
                    <hr className={styles.hrStyle}/>
                    <pre className={styles.errorMessage}>{error.message}</pre>
                    <span className={styles.errorDetails}>
                        The error occurred in<br/>
                        <code>{errorFile.filePath}/{errorFile.lineNumber}</code>
                    </span>
                    <hr className={styles.hrStyle}/>
                </div>
            )}
            &nbsp;
            <button
                onClick={toggleTechnicalInfo}
                className={styles.toggleButton}
            >
                {showTechnicalInfo ? 'Hide technical info' : 'View technical info'}
            </button>
            <button onClick={resetBoundary}>There is a problem</button> */}
    </div>
  )
}
