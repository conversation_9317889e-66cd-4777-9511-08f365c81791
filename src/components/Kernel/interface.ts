import { CoreUiConfI, CoreUiI, InteractiveI } from '../../globals/interfaces'
import { HTMLTAGS, HTMLTags } from '../../globals/types'

/**
 * At its core, every component simpleton must extend the Kernel interface.
 * The simpletons defaults should also inherit the Kernel defaults during their instantiation.
 * NOTE: Simpleton should only return Kernel, unless you know what you're doing.
 * Other family components should extend / return the Simpleton or sibling components as required by their logic.
 *
 * TIP: For evidently common props like colors, fonts, size, alignment, orientation, team, etc. Please check the global types if there’s a pre-existing usable type before defining a custom data type for your components.
 * If you have identified that there is a need of another global data type, please define it under the global types instead of at the component level.
 * Additionally, if you have identified that there is a need of a core global prop, please define it here instead of in the component interface.
 * @summary Kernel is the brain and heart of each component in the AWP V3 ecosystem.
 * @extends InteractiveI
 */
export interface KernelI<T extends HTMLTags = HTMLTAGS>
  extends InteractiveI,
    CoreUiI {
  /**
   * Configuration information and meta-data of the component.
   */
  _conf?:
    | CoreUiConfI
    | {
        /**
         * Genus specifies the sub-classification of the component within a family.
         * For example `Card` is a family and `InsightsCard` is the genus of the family.
         */
        genus?: string
        /**
         * Family is used to group similar types of components(that share core functionality).
         * The families must be defined in Kernel.
         */
        family?:
          | ''
          | 'PAGE ELEMENT'
          | 'AVATAR'
          | 'BAR'
          | 'BUBBLE'
          | 'BUTTON'
          | 'CARD'
          | 'CAROUSEL'
          | 'CONTROL'
          | 'CALENDAR'
          | 'IMAGE'
          | 'LINK'
          | 'TEXT'
          | 'CONTENT BLOCK'
          | 'TIP'
          | 'PLACEHOLDER'
          | 'POPUP'
          | 'ICON'
          | 'CONTAINER'
          | 'ERROR'
          | 'HERO'
          | 'INDICATOR'
          | 'NAV'
          | 'TAB'
      }

  /**
   * Tag to use for the parent wrapper of the component when `isAsIs` is false. Defaults to `div`.
   */
  as?: T
  /**
   * Return the component `as is` without any parent wrapper. When true, `as` will not apply.
   */
  isAsIs?: boolean

  /**
   * @todo if true, Show in full screen popup
   */
  isImmersive?: boolean

  /**
   * `IsFullBleed` allows a component to fill the entire width of a LayoutContainer.
   * However, keep in mind that this prop is applicable only when the component is a direct child of the `LayoutContainer`.
   */
  isFullXBleed?: boolean
  /**
   * `isRightFullXBleed` allows a component to fill the middle and the right column of a LayoutContainer.
   * However, keep in mind that this prop is applicable only when the component is a direct child of the `LayoutContainer`.
   */
  isRightFullXBleed?: boolean
  /**
   * `isLeftFullXBleed` allows a component to fill the middle and the left column of a LayoutContainer.
   * However, keep in mind that this prop is applicable only when the component is a direct child of the `LayoutContainer`.
   */
  isLeftFullXBleed?: boolean
  /**
   * @todo
   * Set whether the component should be physically present / absent on the frontend (and DOM) on small viewports.
   * Disables the component from rendering when false.
   */
  isVisibleOnSmallScreen?: boolean
  /**
   * @todo
   * Set whether the component should be physically present / absent on the frontend (and DOM) on large viewports.
   * Disables the component from rendering when false.
   */
  isVisibleOnLargeScreen?: boolean
  /**
   * Set the component alignment at its wrapper level (using flex).
   * Not to be confused with `textAlign`.
   * @todo rename to `flexAlign`
   */
  align?: 'LEFT' | 'RIGHT' | 'CENTER'
  isExperimentation?: boolean
  experimentEvents?: string
  locale?: string
  //Enlist other global non-HTML props…
}
