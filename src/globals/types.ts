import React from 'react'

export type ICONS =
    | 'Tag'
    | 'SendXFill'
    | 'IoMdRadioButtonOff'
    | 'BsCardText'
    | 'BsLayoutTextSidebarReverse'
    | 'BsBoxArrowUpRight'
    | 'FaCircleCheck'
    | 'AiOutlineInfoCircle'
    | 'RiCheckboxBlankLine'
    | 'GrFormClose'
    | 'BsEnvelope'
    | 'Close'
    | 'Download'
    | 'BsCheckLg'
    | 'BsCheck'
    | 'Apple'
    | 'BsYoutube'
    | 'BsLinkedin'
    | 'LeftChevron'
    | 'RightChevron'
    | 'DownChevron'
    | 'Delete'
    | 'Mics'
    | 'GeoAlt'
    | 'UpChevron'
    | 'RightArrow'
    | 'BarUp'
    | 'BsArrowDown'
    | 'RiCheckboxBlankFill'
    | 'BsFillCheckSquareFill'
    | 'RiRadioButtonFill'
    | 'Slash'
    | 'Calendar'
    | 'Buildings'
    | ' Spotify'
    | 'Clock'
    | 'MapPin'
    | 'Play'
    | 'Pdf'
    | 'Mic'
    | 'Upload'
    | 'Exclamation'
    | 'Bulb'
    | 'Earth'
    | 'CirclePlay'
    | 'CardText'
    | 'Share'
    | 'Telephone'
    | 'LinkedIn'
    | 'Twitter'
    | 'Facebook'
    | 'Filter'
    | 'Send'
    | 'Link'
    | 'Search'
    | 'Profile'
    | 'Hamburger'
    | 'Linkedin'
    | 'Email'
    | 'VolumeDown'
    | 'VolumeUp'
    | 'VolumeMute'
    | 'VolumeFill'
    | 'Fullscreen'
    | 'FullscreenExit'
    | 'Pause'
    | 'Skip'
    | 'Caption'
    | 'Pip'
    | 'PipFill'
    | 'Expand'
    | 'BsPlayBtn'
    | 'BsXLarge'
    | 'Globe'
    | 'Svg'
    | 'Png'
    | 'Jpg'

export type COLOURS =
    | 'inherit'
    | 'cp1'
    | 'cp2'
    | 'cp4'
    | 'cs1'
    | 'cs2'
    | 'cn1'
    | 'cn2'
    | 'cn3'
    | 'cn4'
    | 'cn5'
    | 'cp3'
export type GRADIENTS = 'inherit' | 'cs1s3d' | 'cs1s3h'

export type BGCOLOURS =
    | 'inherit'
    | 'bgTransparent'
    | 'bgTranslucent'
    | 'bnone'
    | 'bp1'
    | 'bp2'
    | 'bs1'
    | 'bs2'
    | 'bs3'
    | 'bs5'
    | 'bn1'
    | 'bn3'
    | 'bn4'
    | 'bn5'
    | 'bn6'
    | 'bn7'
export type BGGRADIENTS =
    | 'inherit'
    | 'bs1s3d'
    | 'bs1s3h'
    | 'bo1n1d'
    | 'bp2s3d'
    | 'bp1p2d'

export type ORIENTATION = 'portrait' | 'portrait-detailed' | 'landscape'

export type FONTFAMILIES =
    | 'inherit'
    | 'fSansReg'
    | 'fSansMed'
    | 'fSansBld'
    | 'fSans'
    | 'fSerif'

export type HEADINGS = 'inherit' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'

export type FONTSIZES =
    | HEADINGS
    | 'fs1'
    | 'fs2'
    | 'fs3'
    | 'fs4'
    | 'fs5'
    | 'fs6'
    | 'fs7'

export type SIZES = 'min' | 's' | 'm' | 'l' | 'xl' | 'max'

export type HTMLTEXTTAGS =
    | 'p'
    | 'span'
    | 'em'
    | 'sup'
    | 'sub'
    | 'a'
    | Omit<HEADINGS, 'inherit'>
export type HTMLTAGS = HTMLTEXTTAGS | 'div' | 'section'

export interface GetDateAndTimeOutputI {
    date: string
    time: string
}

export interface DateTimeFormatOptions {
    year?: 'numeric' | '2-digit'
    month?: 'numeric' | '2-digit' | 'short' | 'long'
    day?: 'numeric' | '2-digit'
    hour?: 'numeric' | '2-digit'
    minute?: 'numeric' | '2-digit'
    timeZoneName?: 'short' | 'long'
    hour12?: boolean
}

export type HTMLTags =
    | keyof React.JSX.IntrinsicElements
    | React.JSXElementConstructor<any>