import React from 'react'
import { ErrorToastI } from '../components/Notifications/Toasts/ErrorToast/interface'
import { SuccessToastI } from '../components/Notifications/Toasts/SuccessToast/interface'
import {
  flattenFiltersObject,
  objectToQueryString,
} from '../systems/AFS/lib/copyFilters/copyFiltersLink'

interface UseCopyFilterHookI {
  setToastState: React.Dispatch<React.SetStateAction<boolean | string>>
  setToastMessage: React.Dispatch<
    React.SetStateAction<SuccessToastI | ErrorToastI>
  >
  filtersObject?: any
  locale?: string
}

const useCopyFilterHook = (props: UseCopyFilterHookI) => {
  const copyFilterOnClickHandler = () => {
    try {
      const falattenedFilters = flattenFiltersObject(props.filtersObject)
      const queryString = objectToQueryString(falattenedFilters)
      const localeStr =
        props.locale && props.locale !== 'en-CA'
          ? `&lang=${props?.locale?.split('-')[0]}`
          : ''
      const fullUrl = `${window.location.origin}${window.location.pathname}?${queryString}${localeStr}`
      navigator.clipboard.writeText(fullUrl)
      props.setToastState('success')
      const message = {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: 'Link Copied to clipboard',
                },
              ],
            },
          ],
        },
      }
      props.setToastMessage(message)
    } catch (error) {
      const message = {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: 'Failed to copy',
                },
              ],
            },
          ],
        },
      }
      props.setToastMessage(message)
      props.setToastState('failure')
    }
  }

  const clearFilterOnClickHandler = () => {
    try {
      props.setToastState('success')
      const message = {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: 'Filters Cleared',
                },
              ],
            },
          ],
        },
      }
      props.setToastMessage(message)
    } catch {
      const message = {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: 'Failed to clear filters',
                },
              ],
            },
          ],
        },
      }
      props.setToastMessage(message)
      props.setToastState('failure')
    }
  }
  return {
    copyFilterOnClickHandler,
    clearFilterOnClickHandler,
  }
}

export default useCopyFilterHook
