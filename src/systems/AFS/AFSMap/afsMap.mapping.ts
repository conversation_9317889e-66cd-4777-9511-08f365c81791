import { Location } from "../../../components/Maps/Map/interface"
import { findProcessAndPlaceObjects } from "../../../lib/propsMapping/index.mapping"

export const getAfsMapProps = (props, isFeaturedActive, locale) => {

  const usedTags = new Map()

  const res = {
    isFeaturedActive,
    addresses: props.map((address) => {
      const addressTags = address?.contentfulMetadata?.tags?.map((tag) => {
        usedTags.set(tag.id, tag.name)
        return tag.id
      })
      return { pageTags: addressTags, ...address }
    }),
    // results: {
    //     textContent: `Found ${props.length} results`,
    // },
  }

  return { res, usedTags }
}

export function transformAddresses(addresses: Array<unknown>): Array<Location> {
  return addresses?.map((address) => {
    console.log("map", addresses)
    return {
      position: {
        lat: address.altusCoordinates?.lat,
        lng: address.altusCoordinates?.lon
      },
      country: address.altusCountry,
      city: address.altusCity,
      phone: address.altusPhone,
      email: address.altusEmail,
      postcode: address.altusPostcode,
      link: address.altusUrl,
      richContent: address.richContent
    }
  })
}

export async function processAddressItems(response, locale) {
  const items = response?.data?.addressObjectCollection?.items || [];
  const addressData = [];

  for (const item of items) {
    await findProcessAndPlaceObjects({
      obj: item,
      locale,
      visitedIds: ['afs-map'],
    })

    addressData.push(item);
  }

  return addressData;
}

export const fetchCoordinatesAndBoundsByLocation = async (location: string) => {
  const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_GEOCODER_API_KEY as string
  const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(location)}&key=${googleMapsApiKey}`;;


  try {
    const response = await fetch(url);
    const data = await response.json();

    if (data.results && data.results.length > 0) {
      const result = data.results[0];
      const location = result.geometry.location;
      const bounds = result.geometry.bounds;
      console.log("mmm:", location, bounds);
      return {
        lat: location.lat,
        lng: location.lng,
        bounds: bounds,
      }
    } else {
      console.error('No results found for location:', location)
    }
  } catch (error) {
    console.error('Error fetching coordinates and bounds:', error)
  }

  return null
}

function convertToJson(dataArray) {
  // Initialize an empty object to hold countries and cities
  const jsonData = { countries: [] }

  // Iterate through the array starting from the second row (since the first row is headers)
  for (let i = 1; i < dataArray.length; i++) {
    const [country, city, asset, richtext_id] = dataArray[i]

    // Find the country object in jsonData or create a new one if it doesn't exist
    let countryObj = jsonData.countries.find((c) => c.country === country)
    if (!countryObj) {
      countryObj = { country: country, cities: [] }
      jsonData.countries.push(countryObj)
    }

    // Find the city object within the country or create a new one if it doesn't exist
    let cityObj = countryObj.cities.find((c) => c.city === city)
    if (!cityObj) {
      cityObj = { city: city, assets: [] }
      countryObj.cities.push(cityObj)
    }

    // Add the asset type and richtext_id to the city object
    cityObj.assets.push({
      type: asset,
      richtext_id: richtext_id,
    })
  }

  return jsonData
}

export function collectRichtextIds(data) {
  const richtextIds = []

  // Loop through each country
  data.countries.forEach((country) => {
    // Loop through each city in the country
    country.cities.forEach((city) => {
      // Loop through each asset in the city
      city.assets.forEach((asset) => {
        // Push the richtext_id to the richtextIds array
        richtextIds.push(asset.richtext_id)
      })
    })
  })

  return richtextIds
}

export function collectCitiesAndCountries(data) {
  const locations = []

  // Loop through each country
  data.countries.forEach((country) => {
    // Loop through each city in the country
    country.cities.forEach((city) => {
      // Add both country and city to the array
      locations.push({ country: country.country, city: city.city })
    })
  })

  return locations
}

export async function getAfsCsvData(fileUrl: string) {
  if (!fileUrl) {
    console.error('No file URL provided')
    return null
  }
  const csvData = await fetch(fileUrl)
    .then((response) => response.text())
    .then((text) => {
      const data = text
        .split('\n')
        .map((row) => row.split(',').map((cell) => cell.trim()))
      return data
    })

  return convertToJson(csvData)
}

// Function to combine country, city with coordinates and bounds using Promise.all
export const getCitiesWithCoordinatesAndBounds = async (data) => {
  const locations = collectCitiesAndCountries(data) // Get cities and countries

  // Map each location to a fetch request, which returns a promise
  const promises = locations.map(async (loc) => {
    const fullLocation = `${loc.city}, ${loc.country}` // Combine city and country
    const coordinatesAndBounds =
      await fetchCoordinatesAndBoundsByLocation(fullLocation)

    if (coordinatesAndBounds) {
      return {
        country: loc.country,
        city: loc.city,
        position: {
          lat: coordinatesAndBounds.lat,
          lng: coordinatesAndBounds.lng,
        },
        bounds: coordinatesAndBounds.bounds,
      }
    }
    return null
  })

  // Use Promise.all to wait for all the fetches to complete
  const results = await Promise.all(promises)

  // Filter out any null results (in case of failed fetches)
  return results.filter((result) => result !== null)
}
