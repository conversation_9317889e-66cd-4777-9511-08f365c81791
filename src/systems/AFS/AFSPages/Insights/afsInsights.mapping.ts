import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { AfsInsightsListingD } from '../../../../components/Cards/CardGeneric/AfsInsightsListing/default'
import { AfsInsightsListingI } from '../../../../components/Cards/CardGeneric/AfsInsightsListing/interface'
import { RadioButtonD } from '../../../../components/Controls/RadioButton/defaults'
import { useTranslation } from '../../../../globals/utils'
import { getDisplayTagName } from '../utils'

export const getAfsInsightsListingProps = (props, isFeaturedActive, locale) => {
  const usedTags = new Map()

  const res: AfsInsightsListingI = {
    ...AfsInsightsListingD,
    isFeaturedActive,
    cards: props?.map((item) => ({
      date: {
        textContent: moment(item?.publishDate?.split('T')?.[0])
          .locale(locale)
          .format('MMM D, YYYY'),
        enDate: moment(item?.publishDate),
      },
      excerpt: {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: item?.seoDescription,
                },
              ],
            },
          ],
        },
      },
      thumbnail: {
        src: item?.pageThumbnail?.url,
        alt: '',
        height: '100%',
        width: '100%',
      },
      cta: {
        ...item?.cta,
        href: `/${item?.slug}`,
        isChevron2Arrow: true,
        //isIconPrefixed: true,
        textContent: useTranslation('readMore', locale),
        variant: 'tertiary',
      },
      heading: {
        textContent: item?.title,
      },
      pageTags: item?.contentfulMetadata?.tags?.map((tag) => {
        const displayTagName = getDisplayTagName(tag.name)
        usedTags.set(tag.id, displayTagName)
        return tag?.id
      }),
    })),
    results: {
      textContent: `Found ${props.length} results`,
    },
  }

  return { res, usedTags }
}

export const getSortOptions = (locale: any) => {
  return {
    option: {
      options: [
        {
          label: {
            textContent: useTranslation('oldest', locale),
            value: 'Oldest',
          },
        },
        {
          label: {
            textContent: useTranslation('newest', locale),
            value: 'Newest',
          },
        },
        {
          ...RadioButtonD,
          label: {
            ...RadioButtonD.label,
            textContent: useTranslation('nameAsc', locale),
            value: 'Name (A - Z)',
          },
        },
        {
          ...RadioButtonD,
          label: {
            ...RadioButtonD.label,
            textContent: useTranslation('nameDesc', locale),
            value: 'Name (Z - A)',
          },
        },
      ],
    },
    dropDownTab: {
      tabText: {
        textContent: useTranslation('sortBy', locale),
      },
    },
  }
}
