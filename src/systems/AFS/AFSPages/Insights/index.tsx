'use client'
import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import AfsInsightsListing from '../../../../components/Cards/CardGeneric/AfsInsightsListing'
import LayoutContainer from '../../../../components/Containers/LayoutContainer'
import SystemFiltration from '../../../../components/SystemFilteration'
import { SystemFiltrationI } from '../../../../components/SystemFilteration/interface'
import { getLocalStorageItem, useTranslation } from '../../../../globals/utils'
import { setFiltersState } from '../../../../redux/slices/filterSlice/filtersSlice'
import { setTagsMap } from '../../../../redux/slices/tagMapSlice'
import {
  getFiltersObject,
  queryStringToObject,
} from '../../lib/copyFilters/parseFiltersLink'
import { filterItems } from '../../lib/filters'
import { getSystemFilterationTabsProps } from '../utils'
import { getAfsInsightsListingProps } from './afsInsights.mapping'

const AFSInsights = (props: any) => {
  const filterType = props.afsFilterData.template
    .toLowerCase()
    .replace(/\s/g, '')
  const locale = props?.locale || getLocalStorageItem('locale')

  const afsFilterCollection = props.afsFilterData.afsFiltersCollection
  const [mappedAfsPages, setMappedAfsPages] = useState<any>([]) // state that will hold the mapped data
  const [systemFiltrationProps, setSystemFiltrationProps] =
    useState<SystemFiltrationI>() // state that will hold the system filteration props

  const dispatch = useDispatch()

  const filters = useSelector((state: any) => state?.filters[filterType])

  const foundStr = useTranslation('found', locale)

  const resultStr = useTranslation('results', locale)

  const [afsInsightsListingProps, setAfsInsightsListingProps] = React.useState()

  // Effect that will run when the component mounts
  React.useEffect(() => {
    const queryString = window.location.search // get query string from url

    // get mapped data and store it in mappedAfsPages state
    const { res: mappedPageData, usedTags } = getAfsInsightsListingProps(
      props.afsPageData,
      true,
      locale
    )
    debugger
    setMappedAfsPages(mappedPageData)

    // set tags map in redux
    dispatch(
      setTagsMap({
        field: `${filterType}TagsMap`,
        value: Object.fromEntries(usedTags),
      })
    )

    // get system filteration tabs props
    const filtersData: SystemFiltrationI = {
      tabs: getSystemFilterationTabsProps(
        afsFilterCollection,
        filterType,
        usedTags
      ),
      filterType,
    }

    // set system filteration tabs props
    setSystemFiltrationProps({ ...filtersData, locale })

    // if query string exists, parse it and set filters in redux state
    if (queryString) {
      const queryObject = queryStringToObject(queryString) // parse query string
      const filtersObject = getFiltersObject(queryObject) // parsed query string to filters object of specific type or format
      delete filtersObject.lang
      // set filters in redux
      dispatch(
        setFiltersState({
          filterType,
          filterState: filtersObject,
        })
      )
    } else {
      // simply set mappedAfsPages state
      setAfsInsightsListingProps(mappedPageData)
    }
  }, [props.afsPageData])

  // Effect that will every time filters state changes and will filter items
  React.useEffect(() => {
    if (filters) {
      // was causing issue when no filters were applied
    }
    const cards = filterItems(mappedAfsPages.cards, filters)
    const data = {
      cards,
      results: {
        // textContent: `Found ${cards?.length} results`
        textContent: `${cards?.length} ${resultStr} ${foundStr} `,
      },
      isFeaturedActive: false,
    }
    setAfsInsightsListingProps(data)
  }, [filters, mappedAfsPages.cards])

  return (
    <>
      {systemFiltrationProps && <SystemFiltration {...systemFiltrationProps} />}
      <LayoutContainer>
        {afsInsightsListingProps && (
          <AfsInsightsListing {...afsInsightsListingProps} locale={locale} />
        )}
      </LayoutContainer>
    </>
  )
}

export default AFSInsights
