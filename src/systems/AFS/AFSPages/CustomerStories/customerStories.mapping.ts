import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { CardFeaturedAfsInsightsListingD } from '../../../../components/Cards/CardFeatured/CardFeaturedAfsInsightsListing/default'
import { CardFeaturedAfsInsightsListingI } from '../../../../components/Cards/CardFeatured/CardFeaturedAfsInsightsListing/interface'
import { RadioButtonD } from '../../../../components/Controls/RadioButton/defaults'

export const getCardFeaturedAfsInsightsListingProps = (props) => {
  if (props?.length === 0) {
    return null
  }

  const item = props.filter((item) => {
    return item?.contentfulMetadata?.tags?.some((tag) => tag.id === 'featured')
  })[0]

  const res = item && {
    contextualInformation: {
      heading: {
        as: 'h4',
        textContent: item?.title,
      },
      subHeading: {
        textContent: item?.shortTitle,
      },
      showButtons: false,
      excerpt: {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: '',
                },
              ],
            },
          ],
        },
      },
    },
    cta: {
      href: item?.slug,
      variant: 'tertiary3',
      textContent: 'Read more',
      isChevron2Arrow: true,
      isIconPrefixed: true,
    },
    image: {
      src: item?.pageThumbnail?.url,
      objectFit: 'cover',
    },
  }

  return res
}

export const getCustomerStoriesListingProps = (
  props,
  isFeaturedActive,
  locale
) => {
  const usedTags = new Map()

  const res: CardFeaturedAfsInsightsListingI = {
    ...CardFeaturedAfsInsightsListingD,
    isFeaturedActive,
    results: {
      textContent: `Found ${props.length} results`,
    },
    cardFeaturedLarge: getCardFeaturedAfsInsightsListingProps(props),
    cards: props.map((item) => ({
      date: {
        textContent: moment(props?.startTime)
          .locale(locale)
          .format('MMM D, YYYY'),
      },
      contextualInformation: {
        heading: {
          as: 'h4',
          textContent: item?.title,
        },
        subHeading: {
          textContent: item?.shortTitle,
        },
        showButtons: false,
        excerpt: {
          data: {
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                attrs: {
                  textAlign: 'left',
                },
                content: [
                  {
                    type: 'text',
                    text: '',
                  },
                ],
              },
            ],
          },
        },
      },
      cta: {
        href: item?.slug,
        variant: 'tertiary3',
        textContent: 'Read more',
        isChevron2Arrow: true,
        isIconPrefixed: true,
      },
      image: {
        src: item?.pageThumbnail?.url,
        objectFit: 'cover',
      },
      pageTags: item.contentfulMetadata.tags.map((tag) => {
        usedTags.set(tag.id, tag.name)
        return tag?.id
      }),
    })),
    Sort: {
      option: {
        options: [
          {
            label: {
              textContent: 'Oldest',
            },
          },
          {
            label: {
              textContent: 'Newest',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: 'Name (A - Z)',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: 'Name (Z - A)',
            },
          },
        ],
      },
      dropDownTab: {
        tabText: {
          textContent: 'Sort By',
        },
      },
    },
  }

  return { res, usedTags }
}
