import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { AfsEventsListingD } from '../../../../components/Cards/CardEvents/AfsEventsListing/default'
import { AfsEventsListingI } from '../../../../components/Cards/CardEvents/AfsEventsListing/interface'
import { LiveEventI } from '../../../../components/Cards/CardEvents/LiveEvent/interface'
import { OnDemandI } from '../../../../components/Cards/CardEvents/OnDemand/interface'
import { RadioButtonD } from '../../../../components/Controls/RadioButton/defaults'
import { useTranslation } from '../../../../globals/utils'
import { getDisplayTagName } from '../utils'

function getEventCardProps(
  props: unknown,
  usedTags: Map<string, string>,
  locale: string
): LiveEventI {
  var address = ''
  if (props?.address) {
    address = Object.entries(props?.address)
      .filter(([key, val]) => key !== 'altusUrl' && props?.address[key])
      .map(([_, val]) => val)
      .join(', ')
  }
  // console.log('getEventCardProps: ', props)

  let eventTime = ''
  let startTime = moment(props?.startTime?.split('.')[0])
    .locale(locale)
    .format('hA')
  let endTime = moment(props?.endTime?.split('.')[0]).locale(locale).format('hA')
  let timeZone = props?.timeZone || ''

  let startDate = moment(props?.startTime).locale(locale).format('MMM D, YYYY')

  let endDate = moment(props?.endTime).locale(locale).format('MMM D, YYYY')

  let eventDate = ''

  eventDate = startDate === endDate ? startDate : `${startDate} - ${endDate}`

  eventTime =
    startTime === endTime || !endTime
      ? `${startTime} (${timeZone})`
      : `${startTime} - ${endTime} (${timeZone})`

  const cardProps: LiveEventI = {
    isLightMode: props?.isLightMode,
    startTime: props.startTime,
    contextualInfo: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    eventBanner: {
      src: props?.image?.url,
      alt: props?.image?.name,
      objectFit: 'cover',
      width: '333px',
      height: '280px',
    },
    liveEventDetail: {
      date: {
        textContent: eventDate, // using moment js

        enDate: `${moment(props?.startTime).format('MMM D, YYYY')} - ${moment(
          props?.endTime
        ).format('MMM D, YYYY')}`,
      },
      time: {
        textContent: eventTime,
      },
      venue: { textContent: address },
      mapLink: {
        href: props?.button?.internalLink?.slug
          ? props?.button?.internalLink?.slug
          : props?.button?.externalLink || '#',
        icon: props?.button?.icon,
        isIconPrefixed:
          props?.button?.iconPlacement === 'Prefix' ? false : true,
        textContent: props?.button?.text,
        variant: props?.button?.template.toLowerCase(),
        isLightMode: props?.button?.isLightMode,
        isChevron2Arrow: props?.button?.isChevron2Arrow,
        target: props?.button?.openInNewTab ? '_blank' : '_self',
      },
    },
    eventText: { data: props?.description },
    cta: { links: props?.buttonGroupCollection?.items },
    pageTags: props?.contentfulMetadata?.tags?.map((tag) => {
      const displayTagName = getDisplayTagName(tag.name)
      usedTags.set(tag.id, displayTagName)
      return tag?.id
    }),
  }

  return cardProps
}

function getOnDemandWebinarProps(
  props: unknown,
  usedTags: Map<string, string>,
  locale: string
): OnDemandI {
  const cardProps: OnDemandI = {
    isLightMode: props?.isLightMode,
    startTime: props?.startTime,
    contextualInfo: {
      showButtons: false,
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      excerpt: {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  text: '',
                  type: 'text',
                },
              ],
            },
          ],
        },
      },
    },
    eventBanner: {
      src: props?.image?.url,
      alt: props?.image?.name,
      objectFit: 'cover',
      width: '333px',
      height: '280px',
    },
    EventTypeText: {
      textContent: 'On-demand',
    },
    eventText: { data: props?.description },
    cta: {
      links: [
        {
          __typename: props?.button?.__typename || 'LinkComponent',
          template: props?.button?.template || 'Primary',
          text: props?.button?.text || useTranslation('registerNow', locale),
          icon: props?.button?.icon || 'RightChevron',
          iconPlacement: props?.button?.iconPlacement || 'Suffix',
          isChevron2Arrow: props?.button?.isChevron2Arrow || true,
          href: props?.button?.internalLink?.slug,
          ...props.button,
        },
      ],
    },
    liveEventDetail: undefined,
    pageTags: props?.contentfulMetadata?.tags?.map((tag) => {
      const displayTagName = getDisplayTagName(tag.name)
      usedTags.set(tag.id, displayTagName)
      return tag?.id
    }),
  }

  return cardProps
}

export const getAfsEventsListingProps = (props, locale) => {
  const usedTags = new Map()

  const totalResults = props?.length
  const cardsProps = {
    cards: props.map((item) => {
      if (item?.template === 'CardOnDemandWebinar') {
        return getOnDemandWebinarProps(item, usedTags, locale)
      } else if (item?.template === 'CardLiveEventWebinar') {
        return getEventCardProps(item, usedTags, locale)
      }
    }),
  }

  const res: AfsEventsListingI = {
    ...AfsEventsListingD,
    results: {
      ...AfsEventsListingD.results,
      textContent: `Found ${totalResults} results`,
    },
    cards: [...cardsProps.cards],
    Sort: {
      option: {
        options: [
          {
            label: {
              textContent: 'Oldest',
            },
          },
          {
            label: {
              textContent: 'Newest',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: 'Name (A - Z)',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: 'Name (Z - A)',
            },
          },
        ],
      },
      dropDownTab: {
        tabText: {
          textContent: 'Sort By',
        },
      },
    },
  }

  return { res, usedTags }
}
