import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { AfsExpertsListingI } from '../../../../components/Cards/CardPeople/AfsExpertsListing/interface'
import { CardPeopleLandscapeRowI } from '../../../../components/Cards/CardPeople/CardPeopleLandscapeRow/interface'
import { RadioButtonD } from '../../../../components/Controls/RadioButton/defaults'
import { getDisplayTagName } from '../utils'

function getPeopleLandscapeRowProps(
  props: unknown,
  usedTags: Map<string, string>,
  locale: string
): CardPeopleLandscapeRowI {
  const cardProps: CardPeopleLandscapeRowI = {
    tags: undefined,
    showButton: true,
    date: {
      textContent: moment(props?.publishDate)
        .locale(locale)
        .format('MMM D, YYYY'),
      enDate: moment(props?.publishDate),
    },
    isLightMode: props?.isLightMode,
    person: {
      fullName: { textContent: props?.fullName || props?.title },
      jobTitle: { textContent: props?.jobTitle || props?.shortTitle },
      companyName: undefined,
      href: props?.slug,
      avatar: {
        src: props?.image?.url || props?.pageThumbnail?.url,
        alt: props?.image?.title || props?.title,
        height: '100%',
        width: '100%',
        objectFit: 'cover',
      },
      bio: {
        data: props?.description || {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: props?.seoDescription,
                },
              ],
            },
          ],
        },
      },
    },
    pageTags: props?.contentfulMetadata?.tags?.map((tag) => {
      const displayTagName = getDisplayTagName(tag.name)
      usedTags.set(tag.id, displayTagName)
      return tag?.id
    }),
  }

  return cardProps
}

export const getAfsOurExpertsListingProps = (props, locale: string) => {
  const usedTags = new Map()

  const cardsProps = {
    cards: props.map((item) =>
      getPeopleLandscapeRowProps(item, usedTags, locale)
    ),
  }
  const res: AfsExpertsListingI = {
    results: {
      textContent: `Found ${props?.length} results`,
    },
    cards: [...cardsProps.cards],
    Sort: {
      option: {
        options: [
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: 'Name (A - Z)',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: 'Name (Z - A)',
            },
          },
        ],
      },
      dropDownTab: {
        tabText: {
          textContent: 'Sort By',
        },
      },
    },
  }

  return { res, usedTags }
}
