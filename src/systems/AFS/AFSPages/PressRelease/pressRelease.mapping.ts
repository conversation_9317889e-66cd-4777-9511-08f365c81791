import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { ArticleReleaseD } from '../../../../components/ArticleLists/ArticleRelease/defaults'
import { RadioButtonD } from '../../../../components/Controls/RadioButton/defaults'
import { useTranslation } from '../../../../globals/utils'

export const getPressReleaseProps = (props, locale) => {
  const usedTags = new Map()

  const foundStr = useTranslation('found', locale)

  const resultStr = useTranslation('results', locale)

  const res = {
    cards: props.map((item) => {
      return {
        ...ArticleReleaseD,
        date: {
          textContent: moment(item.publishDate)
            .locale(locale)
            .format('MMM D, YYYY'),
          enDate: moment(item?.publishDate),
        },
        cta: {
          href: item.slug,
        },
        contextualInformation: {
          showButtons: false,
          heading: {
            textContent: item.title,
          },
          subHeading: {
            // textContent: item.pressReleaseType || item.shortTitle,
            textContent: item.pressReleaseType,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      text: item.seoDescription,
                      type: 'text',
                    },
                  ],
                },
              ],
            },
          },
        },
        pageTags: item.contentfulMetadata.tags.map((tag) => {
          usedTags.set(tag.id, tag.name)
          return tag?.id
        }),
      }
    }),
    results: {
      textContent: `${props.length} ${resultStr} ${foundStr}`,
    },
    Sort: {
      option: {
        options: [
          {
            label: {
              textContent: useTranslation('oldest', locale),
              value: 'Oldest',
            },
          },
          {
            label: {
              textContent: useTranslation('newest', locale),
              value: 'Newest',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: useTranslation('nameAsc', locale),
              value: 'Name (A - Z)',
            },
          },
          {
            ...RadioButtonD,
            label: {
              ...RadioButtonD.label,
              textContent: useTranslation('nameDesc', locale),
              value: 'Name (Z - A)',
            },
          },
        ],
      },
      dropDownTab: {
        tabText: {
          textContent: useTranslation('sortBy', locale),
        },
      },
    },
  }

  return { res, usedTags }
}
