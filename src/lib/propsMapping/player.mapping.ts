import { CalconicI } from '../../components/Players/Calconic/interface'
import { TransistorI } from '../../components/Players/Transistor/interface'
import { YoutubeVideoI } from '../../components/Players/YoutubeVideo/interface'

export function getPlayerProps(props: unknown): YoutubeVideoI {
  const integrationProps: YoutubeVideoI = {
    videoId: props.contentId,
    maxWidth: props.maxWidth,
    ...(props ?? {}),
  }
  return integrationProps
}

export function getTransistorPlayerProps(props: unknown): TransistorI {
  const integrationProps: TransistorI = {
    contentId: props.contentId,
  }
  return integrationProps
}

export function getCalconicPlayerProps(props: unknown): CalconicI {
  const integrationProps: CalconicI = {
    calculatorId: props.calculatorId,
  }
  return integrationProps
}

export async function getEstateMasterData() {
  try {
    const estateMasterData = await fetch(
      'https://clients.estatemaster.com/software/list?softwareName=&onlyNewest=true',
      {
        method: 'GET',
      }
    )
      .then((response) => response.json())
      .then((data) => data)

    return estateMasterData
  } catch (e) {
    // console.error(e)
  }
}

export const getEstateMasterCardOrderArray = (isAllProduct: boolean) => {
  if (isAllProduct) {
    return [
      'Development Feasibility',
      'Development Management',
      'Corporate Consolidation',
      'Property Development Suite',
      'Development Feasibility Lite',
      'Hotel Feasibility',
      'Investment Appraisal',
      'Investment and Valuation Suite',
      'Network Licence Server',
    ]
  } else {
    return [
      'Development Feasibility',
      'Development Management',
      'Corporate Consolidation',
      'Property Development Suite',
    ]
  }
}

export const getEstateMasterFiltersArray = (isAllProduct: boolean) => {
  if (isAllProduct) {
    return ['DF', 'DM', 'CC', 'PDS', 'DFL', 'HF', 'IA', 'IVS', 'NL']
  } else {
    return ['DF', 'DM', 'PDS', 'CC']
  }
}
