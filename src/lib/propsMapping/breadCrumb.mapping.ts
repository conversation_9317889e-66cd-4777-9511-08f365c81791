import { BreadCrumbI } from '../../components/Navs/Breadcrumbs/interface'

export function getFullBreadCrumbProps(props: unknown): BreadCrumbI {
  let links = []
  if (props?.linksCollection) {
    links = props?.linksCollection?.items.slice(-2).map((link: never) => ({
      textContent: link?.text || '',
      href: link?.internalLink?.slug
        ? link?.internalLink?.slug
        : link?.externalLink
          ? link?.externalLink
          : '',
    }))
  }

  const breadCrumbProps: BreadCrumbI = {
    ...props ?? {},
    variant: 'full',
    breadCrumblinks: links,
    isCustomBreadcrumb: links.length > 0 ? true : false,
    endText: props?.endText || '',
    isExperimentation: props?.isExperimentation,
    experimentEvents: props?.experimentEvents,
    isLightMode: props?.isLightMode,
  }
  return breadCrumbProps
}

export function getShortBreadCrumbProps(props: unknown): BreadCrumbI {
  const breadCrumbProps: BreadCrumbI = {
    ...props ?? {},
    variant: 'short',
    endText: props?.endText || '',
    isExperimentation: props?.isExperimentation,
    experimentEvents: props?.experimentEvents,
    isLightMode: props?.isLightMode,
  }
  return breadCrumbProps
}
