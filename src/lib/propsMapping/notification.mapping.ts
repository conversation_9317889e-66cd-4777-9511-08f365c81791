import { BannerCarouselI } from '../../components/Notifications/Banner/BannerCarousel/interface'
import { BannerCompI } from '../../components/Notifications/Banner/BannerMedium/interface'
import { BannerSmallI } from '../../components/Notifications/Banner/BannerSmall/interface'
import { ErrorToastI } from '../../components/Notifications/Toasts/ErrorToast/interface'
import { InfoToastI } from '../../components/Notifications/Toasts/InfoToast/interface'
import { SpinnerToastI } from '../../components/Notifications/Toasts/Spinner Toast/interface'
import { SuccessToastI } from '../../components/Notifications/Toasts/SuccessToast/interface'
import { WarningToastI } from '../../components/Notifications/Toasts/WarningToast/interface'
import { getPrimaryLinkProps } from './link.mapping'

export function getBannerSmallIProps(props: unknown): BannerSmallI {
  const cardProps: BannerSmallI = {
    description: {
      textContent: props?.description,
    },
    cta: props?.cta ? getPrimaryLinkProps(props?.cta) : undefined,
    variant: props?.backgroundColor?.toLowerCase(),
    isGlobal: props?.isGlobal,
    isEnabled: props?.isEnabled,
    SpecificPage: props?.specificPageCollection,
    categoriesPage: props?.categoriesPageCollection,
    isPersistent: props?.isPersistent,
    id: props?.sys?.id,
  }
  return cardProps
}

export function getBannerCompIProps(props: unknown): BannerCompI {
  // debugger
  const cardProps: BannerCompI = {
    heading: {
      as: 'h6',
      displayAs: 'h6',
      textContent: props?.heading,
    },
    description: {
      textContent: props?.description,
    },
    Link: props?.cta ? getPrimaryLinkProps(props?.cta) : undefined,
    variant: props?.backgroundColor?.toLowerCase(),
    isGlobal: props?.isGlobal,
    isEnabled: props?.isEnabled,
    SpecificPage: props?.specificPageCollection,
    categoriesPage: props?.categoriesPageCollection,
    isPersistent: props?.isPersistent,
    id: props?.sys?.id,
  }
  return cardProps
}

export function getBannerCarouselIProps(props: unknown): BannerCarouselI {
  // debugger
  const cardProps: BannerCarouselI = {
    indicatorsPosition: 'Invisible',
    carouselData: props?.carouselDataCollection.items?.map((item: unknown) => {
      return {
        id: item?.sys?.id,
        heading: {
          as: 'h6',
          displayAs: 'h6',
          textContent: item.heading,
        },
        description: {
          textContent: item.description,
        },
        Link: item?.cta ? getPrimaryLinkProps(item.cta) : undefined,
        variant: item?.backgroundColor.toLowerCase(),
        isGlobal: item?.isGlobal,
        isEnabled: item?.isEnabled,
        SpecificPage: item?.specificPageCollection,
        categoriesPage: item?.categoriesPageCollection,
        isPersistent: item?.isPersistent,
      }
    }),
  }
  return cardProps
}

export function getErrorTostIProps(props: unknown): ErrorToastI {
  let autoHideValue: boolean = false
  let timerValue: number | undefined = undefined

  if (props?.autoHide?.toLowerCase() === 'never') {
    autoHideValue = false
  } else {
    autoHideValue = true
    timerValue = parseInt(props?.autoHide, 10) || undefined
  }
  const cardProps: ErrorToastI = {
    simpleParagraph: {
      data: props?.toastMessage || '',
    },
    isPersistent: props?.isPersistent,
    autoHide: autoHideValue,
    timer: timerValue * 1000,
  }
  return cardProps
}

export function getWarningTostIProps(props: unknown): WarningToastI {
  let autoHideValue: boolean = false
  let timerValue: number | undefined = undefined

  if (props?.autoHide?.toLowerCase() === 'never') {
    autoHideValue = false
  } else {
    autoHideValue = true
    timerValue = parseInt(props?.autoHide, 10) || undefined
  }
  const cardProps: ErrorToastI = {
    simpleParagraph: {
      data: props?.toastMessage || '',
    },
    isPersistent: props?.isPersistent,
    autoHide: autoHideValue,
    timer: timerValue * 1000,
  }
  return cardProps
}

export function getSuccessTostIProps(props: unknown): SuccessToastI {
  let autoHideValue: boolean = false
  let timerValue: number | undefined = undefined

  if (props?.autoHide?.toLowerCase() === 'never') {
    autoHideValue = false
  } else {
    autoHideValue = true
    timerValue = parseInt(props?.autoHide, 10) || undefined
  }
  const cardProps: ErrorToastI = {
    simpleParagraph: {
      data: props?.toastMessage || '',
    },
    isPersistent: props?.isPersistent,
    autoHide: autoHideValue,
    timer: timerValue * 1000,
  }
  return cardProps
}

export function getInfoTostIProps(props: unknown): InfoToastI {
  let autoHideValue: boolean = false
  let timerValue: number | undefined = undefined

  if (props?.autoHide?.toLowerCase() === 'never') {
    autoHideValue = false
  } else {
    autoHideValue = true
    timerValue = parseInt(props?.autoHide, 10) || undefined
  }
  const cardProps: ErrorToastI = {
    simpleParagraph: {
      data: props?.toastMessage || '',
    },
    isPersistent: props?.isPersistent,
    autoHide: autoHideValue,
    timer: timerValue * 1000,
  }
  return cardProps
}
export function getSpinnerToastIProps(props: unknown): SpinnerToastI {
  let autoHideValue: boolean = false
  let timerValue: number | undefined = undefined

  if (props?.autoHide?.toLowerCase() === 'never') {
    autoHideValue = false
  } else {
    autoHideValue = true
    timerValue = parseInt(props?.autoHide, 10) || undefined
  }
  const cardProps: ErrorToastI = {
    simpleParagraph: {
      data: props?.toastMessage || '',
    },
    isPersistent: props?.isPersistent,
    autoHide: autoHideValue,
    timer: timerValue * 1000,
  }
  return cardProps
}
