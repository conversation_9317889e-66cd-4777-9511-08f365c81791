import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { DynamicEventWidgetI } from '../../components/ArticleLists/DynamicEventWidget/interface'
import { DynamicPressReleaseI } from '../../components/ArticleLists/DynamicPressRelease/interface'
import { DynamicWidgetFeaturedI } from '../../components/Cards/CardFeatured/DynamicWidgetFeatured/interface'
import { DynamicWidgetInsightsI } from '../../components/Cards/CardGeneric/DynamicWidgetInsights/interface'
import { DynamicWidgetOurTeamI } from '../../components/Cards/CardPeople/DynamicWidgetOurTeam/interface'
import { HeroInsightsCarouselI } from '../../components/Heros/HeroInsigths/@Core/HeroInsightsCarousel/interface'
import { HeroInsightsI } from '../../components/Heros/HeroInsigths/interface'
import {
  convertStringToCamelCase,
  extractValues,
  l,
  useTranslation,
  valueExists,
} from '../../globals/utils'
import { fetchGraphQL } from '../api'
import { getCardFromTagsQuery } from '../queries/card.query'
import {
  getPageFromTagsHeroQuery,
  getPageFromTagsQuery,
} from '../queries/page.query'
import { getPeopleProps } from './card.mapping'
import { getUpdatedHrefFromSlug } from './link.mapping'

export async function getDynamicWidgetInsightsProps(
  props: unknown
): Promise<DynamicWidgetInsightsI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  if (props?.condition !== 'OR' && !tags.includes('afsInsights'))
    tags.unshift(`"afsInsights"`)

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 6,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all',
      props?.locale,
      'afsInsights'
    ),
    true
  )

  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        heading: {
          textContent: componentData?.title || '',
        },
        thumbnail: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        cta: {
          href:
            '/' + getUpdatedHrefFromSlug(componentData?.slug, props.locale) ||
            '',
          textContent: useTranslation('readMore', props?.locale),
          target: '_self',
          variant: 'tertiary',
          isIconPrefixed: false,
          isChevron2Arrow: true,
        },
        date: {
          textContent: moment(componentData?.publishDate?.split('T')[0])
            .locale(props?.locale || 'en')
            .format('MMM D, YYYY'),
        },
        htmlAttr: { className: 'shadow3' },
      }
    }
  )

  const dynamicWidgetInsightsProps: DynamicWidgetInsightsI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    Cards: cards,
    button: props?.button,
    htmlAttr: props?.htmlAttributes,
  }
  return dynamicWidgetInsightsProps
}

export async function getDynamicWidgetFeaturedProps(
  props: unknown
): Promise<DynamicWidgetFeaturedI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    l(`"${element}"`)
    tags.push(`"${element}"`)
  }

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true
  )

  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        contextualInformation: {
          heading: {
            textContent: componentData?.title,
          },
          subHeading: {
            textContent: componentData?.shortTitle,
          },
          excerpt: { data: { type: 'doc' } },
        },
        orientation: 'portrait',
        image: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        cta: {
          href: '/' + componentData?.slug || '',
          textContent: useTranslation('learnMore', props?.locale),
          target: componentData?.target,
          variant: 'tertiary3',
          isIconPrefixed: true,
          isChevron2Arrow: true,
          isLightMode: props?.isLightMode,
        },
      }
    }
  )
  const dynamicWidgetInsightsProps: DynamicWidgetFeaturedI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    cards: cards,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

export async function getDynamicHeroInsightsProps(
  props: unknown
): Promise<HeroInsightsI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let condition =
    props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'

  let response = await fetchGraphQL(
    getPageFromTagsHeroQuery(
      props?.limit || 10,
      props?.orderBy,
      `{
      ${condition}: [${tags}],
      id_contains_none: ["dtsInsightsHeroHighlight"],
      id_contains_all: [
        "${process.env.NEXT_PUBLIC_DOMAIN}",
      ],
    }`
    ),
    true
  )

  let responseHighlight = await fetchGraphQL(
    getPageFromTagsHeroQuery(
      4,
      props?.orderBy,
      `{
      ${condition}: [${tags}],
      id_contains_all: [
        "dtsInsightsHeroHighlight",
        "${process.env.NEXT_PUBLIC_DOMAIN}",
      ],
    }`
    ),
    true
  )

  let hero: HeroInsightsCarouselI[] = []
  let cards: [] = []

  response.data.pageCollection.items =
    response?.data?.pageCollection.items.concat(
      responseHighlight?.data?.pageCollection.items
    )

  for (let i = 0; i < response?.data?.pageCollection.items.length; i++) {
    const componentData = response?.data?.pageCollection.items[i]
    let obj = {
      isLightMode: props?.isLightMode,
      heading: {
        textContent: componentData?.title,
      },
      thumbnail: {
        src: componentData?.pageThumbnail?.url,
        alt: componentData?.pageThumbnail?.name,
        objectFit: 'cover',
        height: '100%',
        width: '100%',
      },
      cta: {
        href: '/' + componentData?.slug || '',
        textContent: useTranslation('readMore', props?.locale),
        target: '_self',
        variant: 'tertiary3',
        isIconPrefixed: true,
        isChevron2Arrow: true,
        isLightMode: props?.isLightMode,
      },
      date: {
        textContent: moment(props?.startTime?.split('T')[0])
          .locale(props?.locale)
          .format('MMM D, YYYY'), // using moment js
      },
      excerpt: {
        data: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  marks: [
                    {
                      type: 'textStyle',
                      attrs: {
                        color: '',
                      },
                    },
                  ],
                  text: componentData?.seoDescription || '',
                },
              ],
            },
          ],
        },
      },
    }

    if (
      componentData?.contentfulMetadata?.tags.some((obj) =>
        valueExists(obj, 'name', 'DTS: Insights Hero Highlight')
      )
    ) {
      if (hero.length < 4) {
        hero.push(obj)
      } else {
        cards.push(obj)
      }
    } else {
      cards.push(obj)
    }
  }

  const heroInsightsProps: HeroInsightsI = {
    isLightMode: props?.isLightMode,
    breadcrumbs: props?.breadCrumb,
    heading: {
      textContent: props?.subHeading || '',
    },
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      showButtons: false,
      excerpt: { data: props?.description },
    },
    cardCarousel: {
      isProgressBar: true,
      isLightMode: props?.isLightMode,
      carouselData: hero,
    },
    cardGeneric: cards,
    button: props?.button,
  }
  return heroInsightsProps
}

export async function getDynamicPressReleaseProps(
  props: unknown
): Promise<DynamicPressReleaseI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true
  )

  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        contextualInformation: {
          heading: {
            textContent: componentData?.title,
          },
          subHeading: {
            textContent: componentData?.shortTitle,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      marks: [
                        {
                          type: 'textStyle',
                          attrs: {
                            color: '',
                          },
                        },
                      ],
                      text: componentData?.seoDescription || '',
                    },
                  ],
                },
              ],
            },
          },
          showButtons: false,
        },
        date: {
          textContent: moment(props?.startTime?.split('T')[0])
            .locale(props?.locale)
            .format('MMM D, YYYY'),
        },
      }
    }
  )

  const dynamicWidgetInsightsProps: DynamicPressReleaseI = {
    isLightMode: props?.isLightMode,
    articleitems: cards,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

export async function getDynamicEventWidgetProps(
  props: unknown
): Promise<DynamicEventWidgetI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  if (props?.condition !== 'OR' && !tags.includes('afsPressRelease'))
    tags.unshift(`"afsPressRelease"`)

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all',
      props?.locale,
      'afsPressRelease'
    ),
    true
  )

  const cards = response?.data?.pageCollection?.items?.map(
    (componentData: any) => {
      let data123 = {
        isLightMode: props?.isLightMode,
        type: props?.shortTitle || 'Event',
        image: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        cta: {
          href: componentData.slug,
        },
        contextualInformation: {
          heading: {
            textContent: componentData?.title,
          },
          subHeading: {
            textContent: componentData?.shortTitle,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      marks: [
                        {
                          type: 'textStyle',
                          attrs: {
                            color: '',
                          },
                        },
                      ],
                      text: componentData?.seoDescription || '',
                    },
                  ],
                },
              ],
            },
          },
          showButtons: false,
        },
        date: {
          textContent: moment(componentData?.publishDate?.split('T')[0])
            .locale(props?.locale)
            .format('MMM D, YYYY'),
        },
      }
      return data123
    }
  )

  const dynamicWidgetInsightsProps: DynamicEventWidgetI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subHeading,
      },
      showButtons: false,
      excerpt: { data: undefined },
    },
    articleitems: cards,
    htmlAttr: props?.htmlAttributes,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

export async function getDynamicMediaHighlightsProps(
  props: unknown
): Promise<DynamicEventWidgetI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let response = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      props?.orderBy,
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true
  )

  const cards = response?.data?.pageCollection.items.map(
    (componentData: any) => {
      return {
        isLightMode: props?.isLightMode,
        image: {
          src: componentData?.pageThumbnail?.url,
          alt: componentData?.pageThumbnail?.name,
          objectFit: 'cover',
          height: '100%',
          width: '100%',
        },
        contextualInformation: {
          heading: {
            textContent: componentData?.title,
          },
          excerpt: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      marks: [
                        {
                          type: 'textStyle',
                          attrs: {
                            color: '',
                          },
                        },
                      ],
                      text: componentData?.seoDescription || '',
                    },
                  ],
                },
              ],
            },
          },
          showButtons: false,
        },
        date: {
          textContent: moment(props?.startTime?.split('T')[0])
            .locale(props?.locale)
            .format('MMM D, YYYY'),
        },
      }
    }
  )

  const dynamicWidgetInsightsProps: DynamicEventWidgetI = {
    isLightMode: props?.isLightMode,
    articleitems: cards,
    button: props?.button,
  }
  return dynamicWidgetInsightsProps
}

export async function getDynamicWidgetOurTeamProps(
  props: unknown
): Promise<DynamicWidgetOurTeamI> {
  let tags: string[] = []

  for (let i = 0; i < props?.tags?.length; i++) {
    const element = convertStringToCamelCase(props?.tags?.at(i))
    tags.push(`"${element}"`)
  }

  let responsePage = await fetchGraphQL(
    getPageFromTagsQuery(
      tags,
      props?.limit || 3,
      'title_ASC',
      props?.condition === 'OR' ? 'id_contains_some' : 'id_contains_all'
    ),
    true
  )
  let responseTeam = await fetchGraphQL(
    getCardFromTagsQuery(
      props?.tags?.map((e: string) => `"${e}"`) || [],
      props?.limit || 3,
      'fullName_ASC',
      props?.condition === 'OR' ? 'tags_contains_some' : 'tags_contains_all'
    ),
    true
  )

  const pages = responsePage?.data?.pageCollection.items.map(
    (componentData: any) => {
      let cardTags = []

      if (
        componentData?.contentfulMetadata?.tags &&
        componentData?.contentfulMetadata?.tags?.length !== 0
      ) {
        cardTags = componentData?.contentfulMetadata?.tags?.map(
          (tag: string) => ({ text: { textContent: extractValues(tag.name) } })
        )
      }
      return {
        isLightMode: props?.isLightMode,
        person: {
          fullName: { textContent: componentData?.title || '' },
          jobTitle: { textContent: componentData?.shortTitle || '' },
          avatar: {
            src: componentData?.pageThumbnail?.url || '',
            alt: componentData?.pageThumbnail?.title || '',
            height: '100%',
            width: '100%',
            objectFit: 'cover',
          },
          bio: {
            data: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  attrs: {
                    textAlign: 'left',
                  },
                  content: [
                    {
                      type: 'text',
                      text: '',
                    },
                  ],
                },
              ],
            },
          },
        },
        tags: cardTags || [],
        orientation: 'portrait',
        showButton: true,
        cta: {
          href: '/' + componentData?.slug || '',
          textContent: 'View Profile',
          target: '_self',
          variant: 'tertiary3',
          isIconPrefixed: true,
          isChevron2Arrow: true,
        },
      }
    }
  )

  const cards = responseTeam?.data?.cardComponentCollection.items.map(
    (componentData: any) => {
      return getPeopleProps({
        ...componentData,
        isLightMode: props?.isLightMode,
        orientation: 'portrait',
        description: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  type: 'text',
                  text: '',
                },
              ],
            },
          ],
        },
      })
    }
  )

  const dynamicWidgetOurTeamProps: DynamicWidgetOurTeamI = {
    isLightMode: props?.isLightMode,
    card: pages.concat(cards),
    button: props?.button,
  }

  return dynamicWidgetOurTeamProps
}
