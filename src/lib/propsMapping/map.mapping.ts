import { MapI } from '../../systems/EchartMap/interface'

export function getMapProps(props): MapI {

    console.log('xxxxx', props)

    const mapProps: MapI = {
        country: props.country,
        htmlAttr: props.htmlAttr,
        mapTooltipCard: props?.mapTooltipCardsCollection.items.map(item => {
            return {
                state: item.altusState,
                richContent: { data: item?.richContent }
            }
        }
        )
    }

    return mapProps
}

interface NodeContent {
    type: string;
    text?: string;
    marks?: any[];
    content?: NodeContent[]; // This could hold an array of child nodes
    attrs?: any;
}

interface ReplacementMap {
    [key: string]: string;
}

// Function to recursively replace placeholders in a JSON object
export function replacePlaceholdersInObject(node: NodeContent, replacements: ReplacementMap): any {
    const data = { ...node }

    // Recursively process if there's content inside the node
    if (data?.content) {
        data.content = data?.content?.map(childNode => {
            return replacePlaceholdersInObject(childNode, replacements)
        });
    }

    // If the node is of type 'text', replace placeholders in the 'text' field
    if (data?.type === 'text' && data?.text) {
        Object.keys(replacements).forEach(placeholder => {
            const regex = new RegExp(`{{${placeholder}}}`, 'g');
            data.text = data.text!.replace(regex, replacements[placeholder]);
        });
    }
    return data;
}
