import { SimpleQuotesI } from '../../components/Quotes/SimpleQuotes/interface'

export function getQuoteProps(props: unknown): SimpleQuotesI {
  const cardProps: SimpleQuotesI = {
    para: {
      data: props?.description || '',
    },
    author: {
      person: {
        fullName: { textContent: props?.quotePeople?.fullName || '' },
        jobTitle: { textContent: props?.quotePeople?.jobTitle || '' },
        avatar: {
          src: props?.quotePeople?.image?.url || '',
          alt: props?.quotePeople?.image?.title || '',
          height: '100%',
          width: '100%',
          objectFit: 'cover',
        },
      },
    },
    isAvatar: props?.showAvatar,
    isAuthor: props?.showAuthor,
    htmlAttr: props?.htmlAttr,
  }
  return cardProps
}
