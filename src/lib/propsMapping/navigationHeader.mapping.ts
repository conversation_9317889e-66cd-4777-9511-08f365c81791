import { SimpleAccordionItemI } from '../../components/Accordions/SimpleAccordion/@Core/SImpleAccordionItem/interface'
import { LinkI } from '../../components/CTAs/Link/interface'
import { IconD } from '../../components/Multimedia/Icons/SysIcon/defaults'
import { SimpleImageD } from '../../components/Multimedia/Images/SimpleImage/defaults'
import { NavigationSecondaryHeaderI } from '../../components/Navs/Navigation/NavigationSecondaryHeader/interface'
import { ReonomyHeaderI } from '../../components/Navs/Navigation/ReonomyHeader/interface'
import { SimpleNavigationI } from '../../components/Navs/Navigation/SimpleNavigation/interface'
import { NavigationCompanyI } from '../../components/Navs/Navigation/SimpleNavigation/NavigationMegamenu/NavigationCompany/interface'
import { NavigationExpertiseI } from '../../components/Navs/Navigation/SimpleNavigation/NavigationMegamenu/NavigationExpertise/interface'
import { NavigationLoginI } from '../../components/Navs/Navigation/SimpleNavigation/NavigationMegamenu/NavigationLogin/interface'
import { NavigationResourcesI } from '../../components/Navs/Navigation/SimpleNavigation/NavigationMegamenu/NavigationResources/interface'
import { NavigationSingleDropdownI } from '../../components/Navs/Navigation/SimpleNavigation/NavigationMegamenu/NavigationSingleDropdown/interface'
import { NavigationWWOfferI } from '../../components/Navs/Navigation/SimpleNavigation/NavigationMegamenu/NavigationWWOffer/interface'
import { l } from '../../globals/utils'
import { getPrimaryLinkProps } from './link.mapping'

const navigationDynamicMapper = (item, props) => {
  switch (item.template) {
    // 1ml-c (Renamed 'Expertise' to 1ml-c)
    case '1ml-c':
      const ExpertiseNavigation: NavigationExpertiseI = {
        //1ml-c (Renamed 'Expertise' to 1ml-c)
        template: '1ml-c',
        // ...NavigationExpertiseD,
        menuList: item?.menuListsCollection?.items?.at(0)
          ? {
              heading: {
                textContent: item?.menuListsCollection?.items?.at(0).heading,
              },
              links: item?.menuListsCollection?.items
                ?.at(0)
                ?.linksCollection?.items?.map((itemLink: unknown) =>
                  getPrimaryLinkProps(itemLink)
                ),
            }
          : undefined,
        cards: item.featuredCardsCollection.items.map((card) => ({
          heading: {
            textContent: card?.heading ?? '',
          },
          date: {
            textContent: card?.startTime ?? null,
          },
          cta: card?.button ? getPrimaryLinkProps(card?.button) : undefined,
          thumbnail: {
            ...SimpleImageD,
            src: card?.image?.url ?? '',
            alt: card?.image?.name ?? '',
            height: '100%',
            width: '100%',
          },
        })),
        cardheadings: [
          {
            textContent: item?.firstCardHeading ?? '',
            colour: 'cp2',
            fontSize: 'fs2',
          },
          {
            textContent: item?.secondCardHeading ?? '',
            colour: 'cp2',
            fontSize: 'fs2',
          },
        ],
      }
      return ExpertiseNavigation
    // 2ml-c (Renamed 'Company' to 2ml-c)
    case '2ml-c':
      const CompanyNavigation: NavigationCompanyI = {
        // 2ml-c (Renamed 'Company' to 2ml-c)
        template: '2ml-c',
        // ...NavigationCompanyD,
        menuLists: item.menuListsCollection.items.map((menuList) => ({
          heading: { textContent: menuList?.heading ?? '' },
          links: menuList?.linksCollection?.items.map(
            (linkItem: { externalLink: string; text: string }): LinkI =>
              getPrimaryLinkProps(linkItem)
          ),
        })),
        cards: item.featuredCardsCollection.items.map((card) => ({
          heading: {
            textContent: card?.heading ?? '',
          },
          date: {
            textContent: card?.startTime ?? null,
          },
          cta: card?.button ? getPrimaryLinkProps(card?.button) : undefined,
          thumbnail: {
            ...SimpleImageD,
            src: card?.image?.url ?? '',
            alt: card?.image?.name ?? '',
            height: '100%',
            width: '100%',
          },
        })),
        cardheadings: [
          {
            textContent: item?.firstCardHeading ?? '',
            colour: 'cp2',
            fontSize: 'fs2',
          },
          {
            textContent: item?.secondCardHeading ?? '',
            colour: 'cp2',
            fontSize: 'fs2',
          },
        ],
        menuList1: {
          heading: {
            textContent: item.menuListsCollection.items?.at(0)?.heading,
          },
          links: item.menuListsCollection.items
            ?.at(0)
            ?.linksCollection.items.map(
              (linkItem: { externalLink: string; text: string }): LinkI =>
                getPrimaryLinkProps(linkItem)
            ),
        },
        menuList2: {
          heading: {
            textContent: item.menuListsCollection.items?.at(1)?.heading ?? '',
          },
          links: item.menuListsCollection.items
            ?.at(1)
            ?.linksCollection.items.map(
              (linkItem: { externalLink: string; text: string }): LinkI =>
                getPrimaryLinkProps(linkItem)
            ),
        },
        p1: {
          textContent: item?.firstCardHeading ?? '',
          colour: 'cp2',
          fontSize: 'fs2',
        },
        p2: {
          textContent: item?.secondCardHeading ?? '',
          colour: 'cp2',
          fontSize: 'fs2',
        },
      }
      return CompanyNavigation
    // 2 x 2ml-c (Renamed 'Resources' to 2 x 2ml-c)
    case '2 x 2ml-c':
      const ResourcesNavigation: NavigationResourcesI = {
        // 2 x 2ml-c (Renamed 'Resources' to 2 x 2ml-c)
        template: '2 x 2ml-c',
        // ...NavigationResourcesD,
        cards: item.featuredCardsCollection.items.map((card) => ({
          heading: {
            textContent: card?.heading ?? '',
          },
          date: {
            textContent: card?.startTime ?? null,
          },
          cta: card?.button ? getPrimaryLinkProps(card?.button) : undefined,
          thumbnail: {
            ...SimpleImageD,
            src: card?.image?.url ?? '',
            alt: card?.image?.name ?? '',
            height: '100%',
            width: '100%',
          },
        })),
        cardheadings: [
          {
            textContent: item?.firstCardHeading ?? '',
            colour: 'cp2',
            fontSize: 'fs2',
          },
          {
            textContent: item?.secondCardHeading ?? '',
            colour: 'cp2',
            fontSize: 'fs2',
          },
        ],
        menuLists: item.menuListsCollection.items.map((menuList) => ({
          heading: {
            textContent: menuList?.heading ?? '',
          },
          links: menuList?.linksCollection?.items.map(
            (linkItem: { externalLink: string; text: string }): LinkI =>
              getPrimaryLinkProps(linkItem)
          ),
        })),
      }
      return ResourcesNavigation
    // 3ml (Renamed 'What we offer' to 3ml)
    case '3ml':
      const WWOfferNavigation: NavigationWWOfferI = {
        // 3ml (Renamed 'What we offer' to 3ml)
        template: '3ml',
        // ...NavigationWWOfferD,
        /*    cards: item.cardListsCollection.items.map((card) => ({
                             heading: {
                               as: 'h6',
                               textContent: card?.heading ?? ""
                             },
                             para: {
                               data: card.description ?? ""
                             }
                           })), */
        menuList1:
          item?.menuListsCollection?.items &&
          item?.menuListsCollection?.items?.length > 0
            ? {
                heading: {
                  textContent:
                    item.menuListsCollection.items?.at(0)?.heading ?? '',
                },
                links: item.menuListsCollection.items
                  ?.at(0)
                  ?.linksCollection.items.map(
                    (linkItem: { externalLink: string; text: string }): LinkI =>
                      getPrimaryLinkProps(linkItem)
                  ),
              }
            : undefined,
        menuList2:
          item?.menuListsCollection?.items &&
          item?.menuListsCollection?.items?.length > 1
            ? {
                heading: {
                  textContent:
                    item.menuListsCollection.items?.at(1)?.heading ?? '',
                },
                links: item.menuListsCollection.items
                  ?.at(1)
                  ?.linksCollection.items.map(
                    (linkItem: { externalLink: string; text: string }): LinkI =>
                      getPrimaryLinkProps(linkItem)
                  ),
              }
            : undefined,
        menuList3:
          item?.menuListsCollection?.items &&
          item?.menuListsCollection?.items?.length > 2
            ? {
                heading: {
                  textContent:
                    item.menuListsCollection.items?.at(2)?.heading ?? '',
                },
                links: item.menuListsCollection.items
                  ?.at(2)
                  ?.linksCollection.items.map(
                    (linkItem: { externalLink: string; text: string }): LinkI =>
                      getPrimaryLinkProps(linkItem)
                  ),
              }
            : undefined,
        /*   heading: item?.firstCardHeading ? {
                            textContent: item.firstCardHeading,
                            colour: 'cp2',
                            fontSize: 'fs3',
                            fontFamily: 'fSansReg'
                          } : undefined, */
        cta: item?.ctaLink ? getPrimaryLinkProps(item?.ctaLink) : undefined,
      }
      return WWOfferNavigation
    case 'Login':
      const LoginNavigation: NavigationLoginI = {
        template: 'Login',
        // ...NavigationLoginD,
        menuList: item.menuListsCollection.items.map((menuList) => ({
          heading: { textContent: menuList.heading ?? '' },
          links: menuList.linksCollection.items.map(
            (linkItem: { externalLink: string; text: string }): LinkI =>
              getPrimaryLinkProps(linkItem)
          ),
        })),
      }

      return LoginNavigation

    case 'Single Dropdown':
      const SingleDropdownMenuList: NavigationSingleDropdownI = {
        template: 'Single Dropdown',
        menuList:
          item?.menuListsCollection?.items &&
          item?.menuListsCollection?.items?.length > 0
            ? {
                heading: {
                  textContent:
                    item.menuListsCollection.items?.at(0)?.heading ?? '',
                },
                links: item.menuListsCollection.items
                  ?.at(0)
                  ?.linksCollection.items.map(
                    (linkItem: { externalLink: string; text: string }): LinkI =>
                      getPrimaryLinkProps(linkItem)
                  ),
              }
            : [],
      }

      return SingleDropdownMenuList

    default:
      return null
  }
}

export function getNavigationHeaderProps(props: unknown): SimpleNavigationI {
  // l("props", props)
  const LoginNavigationData = props?.navigationMenuItemsCollection.items.find(
    (main) => main.template === 'Login'
  )

  const NavigationProps: SimpleNavigationI = {
    ...props,
    //Small navigation
    isLightMode: props?.isLightMode,
    isLoginHidden: props?.isLoginHidden,
    isLanguageHidden: props?.isLanguageHidden,
    isSearchHidden: props?.isSearchHidden,
    NavigationSmall: {
      //navigation accordion
      lightLogo: {
        src: props?.lightLogo?.logo?.url || '',
        height: props?.lightLogo?.maxHeight,
        width: props?.lightLogo?.maxWidth,
        cta: props?.lightLogo?.cta
          ? getPrimaryLinkProps(props?.lightLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },
      darkLogo: {
        src: props?.darkLogo?.logo?.url || '',
        height: props?.darkLogo?.maxHeight,
        width: props?.darkLogo?.maxWidth,
        cta: props?.darkLogo?.cta
          ? getPrimaryLinkProps(props?.darkLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },
      navigationAccordion: {
        // 1ml-c (Renaming expertise to 1ml-c)
        lightLogo: {
          src: props?.lightLogo?.logo?.url || '',
          height: props?.lightLogo?.maxHeight,
          weight: props?.lightLogo?.maxWidth,
          cta: props?.lightLogo?.cta
            ? getPrimaryLinkProps(props?.lightLogo?.cta)
            : undefined,
          isBrandLogo: true,
        },
        darkLogo: {
          src: props?.darkLogo?.logo?.url || '',
          height: props?.darkLogo?.maxHeight,
          width: props?.darkLogo?.maxWidth,
          cta: props?.darkLogo?.cta
            ? getPrimaryLinkProps(props?.darkLogo?.cta)
            : undefined,
          isBrandLogo: true,
        },
        button: props?.ctaButton
          ? getPrimaryLinkProps(props.ctaButton)
          : undefined,
        accordionsMain: props?.navigationMenuItemsCollection?.items?.map(
          (item: unknown): SimpleAccordionItemI => ({
            acrTab: {
              icon: { ...IconD, icon: 'DownChevron' },
              as: 'div',
              heading:
                item.menuLink?.internalLink ||
                item?.menuLink?.externalLink ||
                item?.menuLink?.sectionId
                  ? item?.menuLink
                  : {
                      as: 'h6',
                      textContent: item.menuLink?.text ?? '',
                    },

              isBTop: false,
              isBBottom: true,
              isBLeft: false,
              isBRight: false,
              isRounded: false,
              bColor: 'cn4',
            },
          })
        ),
        navigations: props?.navigationMenuItemsCollection?.items?.map(
          (
            item: unknown
          ):
            | NavigationExpertiseI
            | NavigationWWOfferI
            | NavigationResourcesI
            | NavigationLoginI
            | NavigationCompanyI
            | NavigationSingleDropdownI
            | null => navigationDynamicMapper(item, props)
        ),
      },
    },

    //large navigation
    Navigationlarge: {
      // ...NavigationPrimaryHeaderD,
      lightLogo: {
        src: props?.lightLogo?.logo?.url || '',
        height: props?.lightLogo?.maxHeight,
        width: props?.lightLogo?.maxWidth,
        cta: props?.lightLogo?.cta
          ? getPrimaryLinkProps(props?.lightLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },
      darkLogo: {
        src: props?.darkLogo?.logo?.url || '',
        height: props?.darkLogo?.maxHeight,
        width: props?.darkLogo?.maxWidth,
        cta: props?.darkLogo?.cta
          ? getPrimaryLinkProps(props?.darkLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },
      navigations: props?.navigationMenuItemsCollection?.items
        ?.filter((main) => main.template !== 'Login')
        .map(
          (
            item: unknown
          ):
            | NavigationExpertiseI
            | NavigationWWOfferI
            | NavigationResourcesI
            | NavigationLoginI
            | NavigationCompanyI
            | NavigationSingleDropdownI
            | null => navigationDynamicMapper(item, props)
        ),
      links: props?.navigationMenuItemsCollection?.items
        ?.filter((main) => main.template !== 'Login')
        .map(
          (item: unknown): links => ({
            text: {
              textContent: item?.menuLink?.text ?? '',
            },
            cta: getPrimaryLinkProps(item.menuLink),
          })
        ),
      navigationLogin:
        LoginNavigationData === undefined
          ? undefined
          : {
              login: {
                textContent: LoginNavigationData?.menuLink?.text ?? '',
              },
              ctaLogin:
                LoginNavigationData?.menuLink?.internalLink ||
                LoginNavigationData?.menuLink.externalLink ||
                LoginNavigationData?.menuLink?.sectionId
                  ? getPrimaryLinkProps(LoginNavigationData?.menuLink)
                  : undefined,
              menuList: LoginNavigationData.menuListsCollection.items.map(
                (menuList) => ({
                  heading: { textContent: menuList?.heading ?? '' },
                  links: menuList.linksCollection.items.map(
                    (linkItem: { externalLink: string; text: string }): LinkI =>
                      getPrimaryLinkProps(linkItem)
                  ),
                })
              ),
            },
    },
  }
  return NavigationProps
}

export const getNavigationSecondaryHeaderProps = (
  props: unknown
): NavigationSecondaryHeaderI => {
  l('props', props)
  const NavigationProps: NavigationSecondaryHeaderI = {
    isLightMode: true,
    variant: props?.template,
    elements: props?.navigationMenuItemsCollection?.items?.map((item) => ({
      link: {
        textContent: item?.menuLink?.text ?? '',
      },
      cta:
        item?.menuLink?.internalLink ||
        item?.menuLink?.externalLink ||
        item?.menuLink?.sectionId
          ? getPrimaryLinkProps(item.menuLink)
          : undefined,
      menulist: {
        heading: {
          textContent: '',
        },
        links: item?.menuListsCollection?.items
          ?.at(0)
          ?.linksCollection.items.map(
            (linkItem: { externalLink: string; text: string }): LinkI =>
              getPrimaryLinkProps(linkItem)
          ),
      },
    })),
    contactUsBtn: props?.ctaButton
      ? getPrimaryLinkProps(props.ctaButton)
      : undefined,
  }
  return NavigationProps
}

export const getReonomyHeaderProps = (props: unknown): ReonomyHeaderI => {
  const NavigationProps: ReonomyHeaderI = {
    isLightMode: props?.isLightMode,
    isTranslucent: props?.isTranslucent,
    isNavLightMode: props?.isNavLightMode,
    isLightBgImage: props?.isLightBgImage,
    NavigationSmall: {
      //navigation accordion
      lightLogo: {
        src: props?.lightLogo?.logo?.url || '',
        height: props?.lightLogo?.maxHeight,
        width: props?.lightLogo?.maxWidth,
        cta: props?.lightLogo?.cta
          ? getPrimaryLinkProps(props?.lightLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },
      darkLogo: {
        src: props?.darkLogo?.logo?.url || '',
        height: props?.darkLogo?.maxHeight,
        width: props?.darkLogo?.maxWidth,
        cta: props?.darkLogo?.cta
          ? getPrimaryLinkProps(props?.darkLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },

      accordionsMain: props?.navigationMenuItemsCollection?.items?.map(
        (item: unknown): SimpleAccordionItemI => ({
          acrTab: {
            icon: { ...IconD, icon: 'DownChevron' },
            as: 'div',
            heading:
              item.menuLink?.internalLink ||
              item?.menuLink?.externalLink ||
              item?.menuLink?.sectionId
                ? item?.menuLink
                : {
                    as: 'h6',
                    textContent: item.menuLink?.text ?? '',
                  },

            isBTop: false,
            isBBottom: true,
            isBLeft: false,
            isBRight: false,
            isRounded: false,
            bColor: 'cn4',
          },
        })
      ),
      menuList: props?.navigationMenuItemsCollection?.items
        ?.filter((main) => main.template === 'Single Dropdown')
        .map((item: unknown) =>
          item?.menuListsCollection?.items &&
          item?.menuListsCollection?.items?.length > 0
            ? item?.menuListsCollection?.items.map((list) => {
                return {
                  heading: list?.heading
                    ? {
                        textContent: list?.heading,
                      }
                    : undefined,
                  links: list?.linksCollection.items.map((linkItem) =>
                    getPrimaryLinkProps(linkItem)
                  ),
                }
              })
            : undefined
        ),
      ButtonGroup: { links: props?.buttonGroupCollection?.items },
    },

    Navigationlarge: {
      lightLogo: {
        src: props?.lightLogo?.logo?.url || '',
        height: props?.lightLogo?.maxHeight,
        width: props?.lightLogo?.maxWidth,
        cta: props?.lightLogo?.cta
          ? getPrimaryLinkProps(props?.lightLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },
      darkLogo: {
        src: props?.darkLogo?.logo?.url || '',
        height: props?.darkLogo?.maxHeight,
        width: props?.darkLogo?.maxWidth,
        cta: props?.darkLogo?.cta
          ? getPrimaryLinkProps(props?.darkLogo?.cta)
          : undefined,
        isBrandLogo: true,
      },

      menuList: props?.navigationMenuItemsCollection?.items
        ?.filter((main) => main.template === 'Single Dropdown')
        .map((item: unknown) =>
          item?.menuListsCollection?.items &&
          item?.menuListsCollection?.items?.length > 0
            ? item?.menuListsCollection?.items.map((list) => {
                return {
                  heading: list?.heading
                    ? {
                        textContent: list?.heading,
                      }
                    : undefined,
                  links: list?.linksCollection.items.map((linkItem) =>
                    getPrimaryLinkProps(linkItem)
                  ),
                }
              })
            : undefined
        ),
      links: props?.navigationMenuItemsCollection?.items
        ?.filter((main) => main.template === 'Single Dropdown')
        .map(
          (item: unknown): links => ({
            text: {
              textContent: item?.menuLink?.text ?? '',
            },
            cta:
              item?.menuLink?.internalLink ||
              item?.menuLink?.externalLink ||
              item?.menuLink?.sectionId
                ? getPrimaryLinkProps(item.menuLink)
                : undefined,
          })
        ),

      ButtonGroup: { links: props?.buttonGroupCollection?.items },
    },
  }
  return NavigationProps
}
