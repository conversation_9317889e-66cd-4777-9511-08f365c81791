import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import { FeaturedRowsHomeI } from '../../components/FeaturedRows/FeaturedRowsHome/interface'
import { FeaturedRowsListI } from '../../components/FeaturedRows/FeaturedRowsList/interface'
import { FeaturedRowsPeopleI } from '../../components/FeaturedRows/FeaturedRowsPeople/interface'
import { lowerCaseFirstLetter } from '../../systems/AFS/AFSPages/utils'
import { mapDataToComponent } from './index.mapping'
import { getPrimaryLinkProps } from './link.mapping'

export function getFeaturedRowProps(props: unknown): FeaturedRowsListI {
  const cardProps: FeaturedRowsListI = {
    heading: {
      textContent: props?.heading || '',
    },
    isLightMode: props?.isLightMode,
    CardList: props?.featuredListCollection?.items?.map((item: unknown) => {
      if (item) {
        let obj = item[lowerCaseFirstLetter(item.__typename)] ?? {}
        if (Object.keys(obj).length !== 0) {
          return {
            number: {
              textContent: obj?.number || '',
            },
            date: {
              textContent:
                moment(obj?.startTime)
                  .locale(props?.locale)
                  .format('MMM D, YYYY') || '',
            },
            heading: {
              textContent: obj?.heading || '',
            },
            cta: obj?.button ? getPrimaryLinkProps(obj?.button) : undefined,
          }
        }
      }
    }),
    htmlAttr: props?.htmlAttr,
  }
  return cardProps
}

export function getFeaturedPeopleProps(props: unknown): FeaturedRowsPeopleI {
  // debugger
  const cardProps: FeaturedRowsPeopleI = {
    isLightMode: props?.isLightMode,
    heading: {
      textContent: props?.heading || '',
    },
    CardPeople: props?.featuredListCollection?.items?.map((item) => {
      if (item) {
        let obj = item[lowerCaseFirstLetter(item.__typename)] ?? {}
        if (Object.keys(obj).length !== 0) {
          return mapDataToComponent({ ...obj, isLightMode: props.isLightMode })
        }
      }
    }),

    htmlAttr: props?.htmlAttr,
  }
  return cardProps
}

export function getFeaturedHomeProps(props: unknown): FeaturedRowsHomeI {
  // debugger
  const cardProps: FeaturedRowsHomeI = {
    isLightMode: props?.isLightMode,
    heading: {
      textContent: props?.heading || '',
    },
    button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
    CardHome: props?.featuredListCollection?.items?.map((item: unknown) => {
      if (item) {
        let obj = item[lowerCaseFirstLetter(item.__typename)] ?? {}
        if (Object.keys(obj).length !== 0) {
          return {
            date: {
              textContent:
                moment(obj?.startTime)
                  .locale(props?.locale)
                  .format('MMM D, YYYY') || '',
            },
            heading: {
              textContent: obj?.heading || '',
            },
            cta: obj?.button ? getPrimaryLinkProps(obj?.button) : undefined,
          }
        }
      }
    }),

    htmlAttr: props?.htmlAttr,
  }
  return cardProps
}
