import ComponentError from '../../components/Errors/E404C'
import { urlProcessor } from '../../globals/utils'
import { upperCaseFirstLetter } from '../../systems/AFS/AFSPages/utils'
import { systemAfsQuery } from '../../systems/AFS/AFSQuery/afs.query'
import { fetchGraphQL } from '../api'
import AccordionRouter from '../componentsRouter/AccordionRouter'
import BreadCrumbRouter from '../componentsRouter/BreadCrumbRouter'
import CtaRowRouter from '../componentsRouter/CTARowRouter'
import CalloutRouter from '../componentsRouter/CalloutRouter'
import CardRouter from '../componentsRouter/CardRouter'
import CarouselRouter from '../componentsRouter/CarouselRouter'
import CountdownRouter from '../componentsRouter/CountdownRouter'
import DynamicTaggingRouter from '../componentsRouter/DynamicTaggingRouter'
import ErrorRouter from '../componentsRouter/ErrorRouter'
import FeaturedRowRouter from '../componentsRouter/FeaturedRowRouter'
import FormFieldRouter from '../componentsRouter/FormFieldRouter'
import FormRouter from '../componentsRouter/FormRouter'
import GalleryRouter from '../componentsRouter/GalleryRouter'
import HeroRouter from '../componentsRouter/HeroRouter'
import ImageRouter from '../componentsRouter/ImageRouter'
import LayoutContainerRouter from '../componentsRouter/LayoutContainerRouter'
import LayoutRouter from '../componentsRouter/LayoutRouter'
import LinkRouter from '../componentsRouter/LinkRouter'
import LogoRouter from '../componentsRouter/LogoRouter'
import LogoShowcaseRouter from '../componentsRouter/LogoShowcaseRouter'
import MenuListRouter from '../componentsRouter/MenuListRouter'
import NavigationFooterRouter from '../componentsRouter/NavigationFooterRouter'
import NavigationHeaderRouter from '../componentsRouter/NavigationHeaderRouter'
import NotificationRouter from '../componentsRouter/NotificationRouter'
import OfficesRouter from '../componentsRouter/OfficesRouter'
import PaginationRouter from '../componentsRouter/PaginationRouter'
import PlayerRouter from '../componentsRouter/PlayerRouter'
import PopupRouter from '../componentsRouter/PopupRouter'
import QuoteRouter from '../componentsRouter/QuoteRouter'
import RichtextRouter from '../componentsRouter/RichtextRouter'
import ScrollBoxRouter from '../componentsRouter/ScrollBoxRouter'
import SocialShareRouter from '../componentsRouter/SocialShareRouter'
import TabBoxRouter from '../componentsRouter/TabBoxRouter'
import { accordionItemQuery, accordionQuery } from '../queries/accordion.query'
import { breadCrumbQuery } from '../queries/breadCrumb.query'
import { calloutQuery } from '../queries/callout.query'
import { cardQuery } from '../queries/card.query'
import { carouselQuery } from '../queries/carousel.query'
import { countdownQuery } from '../queries/countdown.query'
import { ctaRowQuery } from '../queries/ctaRow.query'
import { dataVisualizationQuery } from '../queries/dataVisualization.query'
import { dynamicTaggingQuery } from '../queries/dynamicTagging.query'
import { errorQuery } from '../queries/error.query'
import { featuredRowQuery } from '../queries/featuredRow.query'
import { formFieldQuery, formQuery } from '../queries/form.query'
import { galleryQuery } from '../queries/gallery.query'
import { heroQuery } from '../queries/hero.query'
import { imageQuery } from '../queries/image.query'
import { layoutContainerQuery } from '../queries/layoutContainer.query'
import { layoutRowQuery } from '../queries/layoutRow.query'
import { linkQuery } from '../queries/link.query'
import { logoQuery } from '../queries/logo.query'
import { logoshowcaseQuery } from '../queries/logoshowcase.query'
import { mapQuery } from '../queries/map.query'
import { menuListQuery } from '../queries/menuList.query'
import { navigationFooterQuery } from '../queries/navigationFooter.query'
import { navigationHeaderQuery } from '../queries/navigationHeader.query'
import { notificationQuery } from '../queries/notification.query'
import { officesQuery } from '../queries/offices.query'
import { pageLocaleSlugById } from '../queries/page.query'
import { paginationQuery } from '../queries/pagination.query'
import { playerQuery } from '../queries/player.query'
import { popupQuery } from '../queries/popup.query'
import { quoteQuery } from '../queries/quote.query'
import { richtextQuery } from '../queries/richtext.query'
import { scrollBoxQuery } from '../queries/scrollBox.query'
import { socialShareQuery } from '../queries/socialshare.query'
import { tabBoxQuery } from '../queries/tabBox.query'
import { tableQuery } from '../queries/table.query'

// const LinkRouter = dynamic(() => import('../componentsRouter/LinkRouter'), {
//   ssr: false,
// })

// const LayoutRouter = dynamic(() => import('../componentsRouter/LayoutRouter'), {
//   ssr: true,
// })

// const LayoutContainerRouter = dynamic(
//   () => import('../componentsRouter/LayoutContainerRouter'),
//   {
//     ssr: true,
//   }
// )

// const AfsRouter = dynamic(() => import('../componentsRouter/AFSRouter'), {
//   ssr: false,
// })

// const DynamicTaggingRouter = dynamic(
//   () => import('../componentsRouter/DynamicTaggingRouter'),
//   {
//     ssr: false,
//   }
// )

export async function getComponentQueryData({
  id,
  typename,
  locale = 'en-CA',
  revalidateTag,
  visitedIds = [],
  fullUrl = '',
}: {
  id: string | undefined
  typename: string
  locale?: string
  revalidateTag?: string
  visitedIds?: any
  fullUrl?: string
}) {
  const mapTypeNameToQuery = {
    ComponentAccordion: accordionQuery,
    BreadCrumbComponent: breadCrumbQuery,
    ComponentCallout: calloutQuery,
    CardComponent: cardQuery,
    CarouselComponent: carouselQuery,
    ComponentFeaturedRows: featuredRowQuery,
    ComponentGallery: galleryQuery,
    HeroComponent: heroQuery,
    LinkComponent: linkQuery,
    ComponentLogo: logoQuery,
    ComponentLogoShowcase: logoshowcaseQuery,
    ComponentOffices: officesQuery,
    ComponentMenuList: menuListQuery,
    ComponentNavigationHeader: navigationHeaderQuery,
    ComponentNotification: notificationQuery,
    ComponentPagination: paginationQuery,
    ComponentQuote: quoteQuery,
    ComponentCountdown: countdownQuery,
    ComponentSocialShare: socialShareQuery,
    ComponentRichtext: richtextQuery,
    ComponentCtaRow: ctaRowQuery,
    ComponentPopup: popupQuery,
    ComponentNavigationFooter: navigationFooterQuery,
    ComponentTabBox: tabBoxQuery,
    ComponentLayoutRow: layoutRowQuery,
    ComponentLayoutContainer: layoutContainerQuery,
    Player: playerQuery,
    ComponentForm: formQuery,
    ComponentErrorPage: errorQuery,
    ComponentImage: imageQuery,
    ComponentTable: tableQuery,
    ComponentDynamicTagging: dynamicTaggingQuery,
    // ComponentMap: CoreMapQuery,
    FormField: formFieldQuery,
    ComponentScrollBox: scrollBoxQuery,
    SystemAfs: systemAfsQuery,
    ComponentAccordionItem: accordionItemQuery,
    ComponentMap: mapQuery,
    DataVisualization: dataVisualizationQuery,
  }

  if (!id) {
    console.error(`No id found for component type ${typename}`)
    return
  }
  // console.log(visitedIds, 'visitedIds :::::::', visitedIds?.length)
  if (!mapTypeNameToQuery[typename]) {
    // throw new Error(`No query found for component type ${typename}`)
    console.error(`No query found for component type ${typename}`)
    return
  }
  try {
    let childData = await fetchGraphQL(
      mapTypeNameToQuery[typename](id, locale),
      true,
      revalidateTag
    )

    childData = childData?.data

    let data = await findProcessAndPlaceObjects({
      obj: childData,
      locale,
      visitedIds,
    })
    // await findProcessAndPlaceObjects(data, locale)

    const key = Object.keys(childData ?? {})?.at(0)
    const value = Object.values(childData ?? {})?.at(0)

    const finalData = {}
    finalData[key] = { ...value, locale, fullUrl }
    return finalData
  } catch (error) {
    if (`${error}`.includes('Circular found')) {
      throw error
    } else {
      console.error(error)
    }
  }
}

export function mapDataToComponent(componentData: { [k: string]: unknown }) {
  const typeNameToRouterMapping = {
    ComponentAccordion: AccordionRouter,
    BreadCrumbComponent: BreadCrumbRouter,
    ComponentCallout: CalloutRouter,
    CardComponent: CardRouter,
    CarouselComponent: CarouselRouter,
    ComponentCtaRow: CtaRowRouter,
    ComponentFeaturedRows: FeaturedRowRouter,
    ComponentGallery: GalleryRouter,
    HeroComponent: HeroRouter,
    LinkComponent: LinkRouter,
    ComponentLogo: LogoRouter,
    ComponentLogoShowcase: LogoShowcaseRouter,
    ComponentOffices: OfficesRouter,
    ComponentMenuList: MenuListRouter,
    ComponentNavigationHeader: NavigationHeaderRouter,
    ComponentNotification: NotificationRouter,
    ComponentQuote: QuoteRouter,
    MenuListComponent: MenuListRouter,
    ComponentPagination: PaginationRouter,
    ComponentCountdown: CountdownRouter,
    ComponentSocialShare: SocialShareRouter,
    ComponentRichtext: RichtextRouter,
    ComponentPopup: PopupRouter,
    ComponentNavigationFooter: NavigationFooterRouter,
    ComponentTabBox: TabBoxRouter,
    ComponentLayoutRow: LayoutRouter,
    ComponentLayoutContainer: LayoutContainerRouter,
    Player: PlayerRouter,
    ComponentForm: FormRouter,
    ComponentErrorPage: ErrorRouter,
    // ComponentTable: TableRouter, @todo ARCHIVED
    ComponentImage: ImageRouter,
    ComponentDynamicTagging: DynamicTaggingRouter,
    // ComponentMap: CoreMapRouter,
    FormField: FormFieldRouter,
    ComponentScrollBox: ScrollBoxRouter,
    // SystemAfs: AfsRouter,
    // ComponentMap: MapRouter,
    // DataVisualization: DataVisualizationRouter,
  }

  // l('componentData', componentData)

  if (!componentData?.__typename) {
    return (
      <ComponentError
        message={'Oops! Missing query or __typename for some component.'}
      />
    )
    // throw new Error('No __typename found in component data')
  }

  if (!typeNameToRouterMapping[componentData?.__typename]) {
    // throw error in production build
    return (
      <ComponentError
        message={`No router found for component type ${componentData?.__typename}`}
      />
    )
  }

  return typeNameToRouterMapping[
    componentData?.__typename as keyof typeof typeNameToRouterMapping
  ]?.(componentData)
}

// Function to call the API and process the response
export async function callApi({
  data,
  path,
  obj,
  locale,
  visitedIds,
}: {
  data: any
  path: (string | number)[]
  obj: any
  locale: string
  visitedIds: []
}): Promise<any> {
  // Replace this with your actual API call logic
  const id = data.id || data.sys.id

  if (id) {
    if (visitedIds?.filter((el) => el === id)?.length) {
      logBox(
        [
          ' console.warn',

          '⚠️ Circular Dependency Detected ⚠️',
          `Component ID: ${id}`,
          `Circular dependency found for component type - ${id}`,
          `Initial ID in chain: ${visitedIds?.[0]}`,
          `Visited IDs chain so far: ${visitedIds.join(' → ')}`,
          `The component with ID '${id}' has appeared more than twice in the visited list, indicating a circular reference in your component structure.`,
        ],
        console.warn
      )
      throw new Error(
        `Circular found for component type  - ${id} - ${visitedIds?.[0]}`
      )
    }
    visitedIds.push(
      `${data.__typename || upperCaseFirstLetter(data.typeName)}`,
      id
    )
  }
  const apiResponse = await getComponentQueryData({
    id: data.id || data.sys.id,
    typename: data.__typename || upperCaseFirstLetter(data.typeName),
    locale: locale,
    visitedIds,
  })
  if (id) {
    visitedIds.pop()
    visitedIds.pop()
  }

  // Place the data back into the original object at the same location
  let currentObj = obj
  for (let i = 0; i < path.length; i++) {
    if (Object.hasOwn(currentObj, path[i])) {
      currentObj = currentObj[path[i]]
    }
  }
  Object.assign(currentObj, apiResponse)

  // Recursive call to process the newly obtained data
  await findProcessAndPlaceObjects({
    obj: apiResponse,
    locale,
    currentPath: path,
    visitedIds,
  })
}

export async function findProcessAndPlaceObjects({
  obj,
  locale = 'en-CA',
  currentPath = [],
  visitedIds = [],
}: {
  obj: any
  locale?: string
  currentPath?: (string | number)[]
  visitedIds?: any
}): Promise<any> {
  await recursiveSearch({
    innerObj: obj,
    path: currentPath,
    idarray: visitedIds,
    visitedIds: visitedIds,
    locale,
    obj,
  })
}

// Main recursive function
export async function recursiveSearch({
  innerObj,
  path,
  idarray,
  locale,
  obj,
  visitedIds,
}: {
  innerObj: any
  path: (string | number)[]
  idarray: (string | number)[]
  locale: string
  obj: any
  visitedIds: []
}): Promise<any> {
  if (Array.isArray(innerObj)) {
    await processArray(innerObj, path, idarray, locale, obj, visitedIds)
  } else if (typeof innerObj === 'object' && innerObj !== null) {
    await processObject(innerObj, path, idarray, locale, obj, visitedIds)

    if (isRichtextInternalLink(innerObj)) {
      await processRichTextLink(innerObj, locale)
    }
  }
}

function isSamePath(
  path1: (string | number)[],
  path2: (string | number)[]
): boolean {
  return JSON.stringify(path1) === JSON.stringify(path2)
}

// Process Array Function
async function processArray(
  innerObj: any[],
  path: (string | number)[],
  idarray: (string | number)[],
  locale: string,
  obj: any,
  visitedIds: []
) {
  for (let i = 0; i < innerObj.length; i++) {
    const id = innerObj[i]?.sys?.id || innerObj[i]?.id
    await recursiveSearch({
      innerObj: innerObj[i],
      path: [...path, i],
      idarray: [...idarray, id],
      locale,
      obj,
      visitedIds,
    })
  }
}

// Process Object Function
async function processObject(
  innerObj: any,
  path: (string | number)[],
  idarray: (string | number)[],
  locale: string,
  obj: any,
  visitedIds: []
) {
  if (hasDesiredKeys(innerObj)) {
    await callApi({
      data: innerObj,
      path,
      obj,
      locale,
      visitedIds,
    })
  }

  for (const key in innerObj) {
    if (Object.hasOwn(innerObj, key)) {
      const id = innerObj[key]?.sys?.id || innerObj[key]?.id
      await recursiveSearch({
        innerObj: innerObj[key],
        path: [...path, key],
        idarray: [...idarray, id],
        locale,
        obj,
        visitedIds,
      })
    }
  }
}

// Process Rich Text Link Function
async function processRichTextLink(innerObj: any, locale: string) {
  const entryId = innerObj?.attrs?.entity?.sys?.id
  const res = await fetchGraphQL(pageLocaleSlugById(entryId), true)

  innerObj.attrs = {
    ...innerObj.attrs,
    slug: urlProcessor(res?.data?.[locale.split('-')[0]]?.slug ?? '#'),
  }
}

// Function to transform fields (no change needed)
export function transformFields(componentData: any) {
  const transformedFields = {
    ...(componentData ?? {}),
    locale: componentData?.locale,
  }

  componentData?.fieldMapping?.toFieldIds?.map((fieldId: string, i: number) => {
    const fromFieldId = componentData?.fieldMapping?.fromFieldIds?.[i]
    transformedFields[fieldId] = componentData?.existingDataEntry?.[fromFieldId]
  })

  return transformedFields
}

// Function to check if an object has the desired keys
function hasDesiredKeys(innerObj: any) {
  const desiredKeys1 = ['__typename', 'sys']
  const desiredKeys2 = ['id', 'type', 'linkType', 'typeName']
  const objKeys = Object.keys(innerObj)
  return (
    (objKeys.length === 2 &&
      desiredKeys1.every((key) => objKeys.includes(key))) ||
    (objKeys.length === 4 && desiredKeys2.every((key) => objKeys.includes(key)))
  )
}

// Function to check if an object is a rich text internal link
function isRichtextInternalLink(innerObj: any) {
  return (
    innerObj?.['type'] === 'link' &&
    typeof innerObj?.['attrs'] === 'object' &&
    typeof innerObj?.attrs?.entity?.sys?.id === 'string'
  )
}

function logBox(
  data: (string | string[])[],
  printer: Function = console.log
): void {
  const width = process.stdout.columns || 80 // Default to 80 columns if not available
  const topBorder = '┌' + '─'.repeat(width - 2) + '┐'
  const bottomBorder = '└' + '─'.repeat(width - 2) + '┘'

  // Function to center or truncate text
  const centerText = (text: string): string => {
    if (text.length >= width - 4) {
      // Truncate if the text is too long to fit inside the box
      return text.slice(0, width - 4)
    } else {
      const padding = Math.floor((width - text.length) / 2)
      return (
        ' '.repeat(padding) +
        text +
        ' '.repeat(width - text.length - padding - 2)
      )
    }
  }

  // Print the top border
  printer(topBorder)

  // Print each line, whether it's a string or an array (for nested data)
  data.forEach((item) => {
    if (typeof item === 'string') {
      const centered = centerText(item)
      printer(
        '│' +
          centered +
          ' '.repeat(Math.max(0, width - centered.length - 2)) +
          '│'
      )
    } else if (Array.isArray(item)) {
      // If the item is an array, print each element on a new line
      item.forEach((subItem) => {
        const centered = centerText(subItem)
        printer(
          '│' +
            centered +
            ' '.repeat(Math.max(0, width - centered.length - 2)) +
            '│'
        )
      })
    }
  })

  // Print the bottom border
  printer(bottomBorder)
}
