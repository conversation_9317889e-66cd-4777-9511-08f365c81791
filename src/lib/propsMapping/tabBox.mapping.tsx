// Assuming 'use client' is intended to be a comment
// If not, please provide more context
'use client'
import { StatusBarD } from '../../components/Bars/StatusBar/defaults'
import { TabBoxI } from '../../components/Containers/TabBox/interface'
import { TabBoxHeroI } from '../../components/Containers/TabBoxHero/interface'
import { TabBoxHomeI } from '../../components/Containers/TabBoxHome/interface'
import { lowerCaseFirstLetter } from '../../systems/AFS/AFSPages/utils'
import { mapDataToComponent } from './index.mapping'
import { getPrimaryLinkProps } from './link.mapping'

export function getTabBoxProps(props: any): TabBoxI {
  let tabs = []
  for (let i = 0; i < props?.tabItemsCollection?.items?.length; i++) {
    const element = props?.tabItemsCollection?.items?.at(i)

    let allCompDataArray = element?.tabComponentCollection?.items?.map(
      (item) => {
        if (item) {
          let obj = item[lowerCaseFirstLetter(item.__typename)] ?? {}
          if (Object.keys(obj).length !== 0) {
            return (
              <div style={{ marginBottom: 50 }}>{mapDataToComponent(obj)}</div>
            )
          }
        }
      }
    )
    tabs.push({ ...element, tabComponentCollection: allCompDataArray })
  }

  const tabBoxProps: TabBoxI = {
    contexualInfo: {
      subHeading: {
        textContent: props?.subHeading,
      },
      heading: {
        textContent: props?.heading,
      },
      excerpt: {
        data: props?.description,
      },
      showButtons: false,
    },
    tabController: {
      isDropdown: false,
      GenericTAbs: tabs?.map((item: any) => {
        return {
          heading: {
            textContent: item?.tabHeading,
          },
          statusBar: StatusBarD,
          children: item?.tabComponentCollection,
        }
      }),
      activeTab: 0,
    },
    htmlAttr: props?.htmlAttr,
  }

  return tabBoxProps
}

export function getTabBoxHomeProps(props: any): TabBoxHomeI {
  let tabs = []
  for (let i = 0; i < props?.tabItemsCollection?.items?.length; i++) {
    const element = props?.tabItemsCollection?.items?.at(i)

    let allCompDataArray = element?.tabComponentCollection?.items?.map(
      (item) => {
        if (item) {
          let obj = item[lowerCaseFirstLetter(item.__typename)] ?? {}
          if (Object.keys(obj).length !== 0) {
            return mapDataToComponent(obj)
          }
        }
      }
    )
    tabs.push({ ...element, tabComponentCollection: allCompDataArray })
  }

  const tabBoxProps: TabBoxHomeI = {
    heading: {
      textContent: props?.heading,
    },
    button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
    tabController: {
      isDropdown: false,
      GenericTAbs: tabs?.map((item: any) => {
        return {
          heading: {
            textContent: item?.tabHeading,
          },
          statusBar: StatusBarD,
          children: item?.tabComponentCollection,
        }
      }),
      activeTab: 0,
    },
    htmlAttr: props?.htmlAttr,
    isLightMode: true,
  }

  return tabBoxProps
}

export function getTabBoxHeroProps(props: any): TabBoxHeroI {
  let tabs = []
  for (let i = 0; i < props?.tabItemsCollection?.items?.length; i++) {
    const element = props?.tabItemsCollection?.items?.at(i)

    let allCompDataArray = element?.tabComponentCollection?.items?.map(
      (item) => {
        if (item) {
          let obj = item[lowerCaseFirstLetter(item.__typename)] ?? {}
          if (Object.keys(obj).length !== 0) {
            return {
              component: mapDataToComponent(obj),
              componentType: obj?.__typename,
            }
          }
        }
      }
    )
    tabs.push({ ...element, tabComponentCollection: allCompDataArray })
  }

  const tabBoxProps: TabBoxHeroI = {
    GenericTAbs: tabs?.map((item: any) => {
      return {
        heading: {
          textContent: item?.tabHeading,
        },
        statusBar: StatusBarD,
        children: item?.tabComponentCollection,
      }
    }),
    activeTab: 0,
    htmlAttr: props?.htmlAttr,
    isLightMode: true,
  }

  return tabBoxProps
}
