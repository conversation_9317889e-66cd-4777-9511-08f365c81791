import { ReactNode } from 'react'
import ColumnContainer from '../../components/Containers/ColumnContainer'
import { LayoutI } from '../../components/Containers/Layout/interface'
import { l } from '../../globals/utils'
import { mapDataToComponent } from './index.mapping'

// Logically calculate the return the component
export const CalculateComponent = (props): ReactNode => {
  const data = props
  const Comp = mapDataToComponent({
    ...data,
    onChange: props?.onChange,
    values: props?.values,
  })
  return Comp
}

export function getLayoutWithFullBleedProps(props: unknown): LayoutI {
  // getting all the templates in an array
  const itemsArr = props.layoutColumnCollection?.items?.map((item) =>
    item.layoutItemCollection.items.map((layoutItem) => layoutItem.template)
  )
  // Special case for CardGenericMedium | CardFeaturedPortrait | CardGenericSmall component
  const filteredItemsArr = itemsArr
    ? itemsArr?.filter(
      (card) =>
        card.at(0) === 'CardGenericMedium' ||
        card.at(0) === 'CardFeaturedPortrait' ||
        card.at(0) === 'CardGenericSmall'
    )
    : null

  // logic to see if extra col needs to be added
  const addExtraCol = Boolean(
    filteredItemsArr &&
    filteredItemsArr.length === 3 &&
    props.layoutColumnCollection.items.length === 3
  )

  const layoutProps: LayoutI = {
    justifyContent: props.alignItems,
    flexWrap: 'wrap',
    columnGap: props?.columnGap,
    htmlAttr: {
      ...props?.htmlAttr,
      className: `${props?.isFullXBleed ? '' : props?.htmlAttr?.className} defaultLayout`,
    },
    children: (
      <>
        {props?.layoutColumnCollection?.items?.map((item) => (
          <ColumnContainer {...item} columnGap={props?.columnGap} />
        ))}
        {
          // Special case for CardGenericMedium | CardFeaturedPortrait | CardGenericSmall component
          addExtraCol && (
            <div
              className='cardLayout'
              style={{
                width: 'auto',
                minWidth: '296px',
                display: 'flex',
                flex: 1,
              }}
            ></div>
          )
        }
      </>
    ),
  }
  return layoutProps
}

export function getLayoutProps(props: unknown): LayoutI {
  l('fff', props.columnGap)
  // debugger
  // getting all the templates in an array
  const itemsArr = props?.layoutColumnCollection?.items?.map((item) =>
    item.layoutItemCollection.items
      .filter((item) => !!item)
      .map(
        (layoutItem) =>
          layoutItem?.template
      )
  )

  // Special case for CardGenericMedium | CardFeaturedPortrait | CardGenericSmall component
  const filteredItemsArr = itemsArr
    ? itemsArr?.filter(
      (card) =>
        card.at(0) === 'CardGenericMedium' ||
        card.at(0) === 'CardFeaturedPortrait' ||
        card.at(0) === 'CardGenericSmall'
    )
    : null

  // logic to see if extra col needs to be added
  const addExtraCol = Boolean(
    filteredItemsArr &&
    filteredItemsArr.length === 3 &&
    props.layoutColumnCollection.items.length === 3
  )

  const layoutProps: LayoutI = {
    justifyContent: props.alignItems,
    flexWrap: 'wrap',
    columnGap: props?.columnGap,
    htmlAttr: {
      ...props?.htmlAttr,
      className: `${props?.htmlAttr?.className} defaultLayout`,
      style: {
        ...props?.htmlAttr?.style,
        position: props?.bgImage ? 'relative' : 'static',
      },
    },
    children: (
      <>
        {props?.layoutColumnCollection?.items
          ?.filter((item) => !!item)
          ?.map((item) => (
            <ColumnContainer
              {...item}
              onChange={props?.onChange}
              values={props?.values}
              columnGap={props?.columnGap}
            />
          ))}
        {addExtraCol && (
          <div
            className='cardLayout'
            style={{
              width: 'auto',
              minWidth: '296px',
              display: 'flex',
              flex: 1,
            }}
          ></div>
        )}
      </>
    ),
  }
  return layoutProps
}
