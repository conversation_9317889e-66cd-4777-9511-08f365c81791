import { SimpleHeadingD } from '../../components/ContentBlocks/Texts/Headings/SimpleHeading/defaults'
import { SimpleParagraphD } from '../../components/ContentBlocks/Texts/Paragraphs/SimpleParagraph/defaults'
import { CTARowFeaturedI } from '../../components/CTARows/CTARowFeatured/interface'
import { CTARowHomeI } from '../../components/CTARows/CTARowHome/interface'
import { CTARowMultipleI } from '../../components/CTARows/CTARowMultiple/interface'
import { CTARowSingleBackgroundImageI } from '../../components/CTARows/CTARowSingle/CTARowSingleBackgroundImage/interface'
import { CTARowSingleColorFillI } from '../../components/CTARows/CTARowSingle/CTARowSingleColorFill/interface'
import { InsightHomeI } from '../../components/Insights/InsightHome/interface'
import { InsightPrimaryCTAI } from '../../components/Insights/InsightPrimaryCTA/interface'
import { InsightSecondaryCTAI } from '../../components/Insights/InsightSecondaryCTA/interface'
import { BackgroundImageD } from '../../components/Multimedia/Images/BackgroundImage/defaults'
import { SimpleImageD } from '../../components/Multimedia/Images/SimpleImage/defaults'
import { getPrimaryLinkProps } from './link.mapping'

export function getSingleBackroundColorFillProps(
  props: unknown
): CTARowSingleColorFillI {
  const ctaRowProps: CTARowSingleColorFillI = {
    contextualInformation: {
      heading: {
        textContent: props?.heading || '',
      },
      excerpt: { data: props?.description || '' },
      buttons:
        {
          links: props?.buttonGroupCollection?.items,
        } || [],
      showButtons: true,
    },

    htmlAttr: props?.htmlAttr,
    isLightMode: props?.isLightMode,
  }
  return ctaRowProps
}

export function getSingleBackgroundImageProps(
  props: unknown
): CTARowSingleBackgroundImageI {
  const ctaRowProps: CTARowSingleBackgroundImageI = {
    contextualInformation: {
      heading: {
        textContent: props?.heading || '',
      },
      excerpt: { data: props?.description || '' },
      buttons:
        {
          links: props?.buttonGroupCollection?.items,
        } || [],
      showButtons: true,
    },
    backgroundImage: {
      src: props?.backgroundImage?.url || '',
      alt: props?.backgroundImage?.title || '',
    },
    isBlur: props?.isBlur,
    htmlAttr: props?.htmlAttr,
  }
  return ctaRowProps
}

export function getMultipleProps(props: unknown) {
  const cards = props?.calloutInfoCardCollection?.items?.map(
    (item: unknown) => {
      return {
        contextualInformation: {
          heading: {
            textContent: item?.heading,
          },
          excerpt: { data: item?.description },
        },
        media: {
          src: item?.image?.url,
          alt: item?.image?.title,
        },
        size: item?.size,
        alignment: item?.alignment,
        cta: item?.button ? getPrimaryLinkProps(item?.button) : undefined,
      }
    }
  )
  const ctaRowProps: CTARowMultipleI = {
    contextualInformation: {
      heading: {
        textContent: props?.heading || '',
      },
      excerpt: { data: props?.description || '' },
      showButtons: true,
      buttons:
        {
          links: props?.buttonGroupCollection?.items,
        } || [],
    },
    cards: cards || [],
    isLightMode: props?.isLightMode,
    htmlAttr: props?.htmlAttr,
  }
  return ctaRowProps
}

export function getInsightSecondaryProps(props: unknown) {
  const InsightSecondaryProps: InsightSecondaryCTAI = {
    isLightMode: props?.isLightMode,
    image: {
      ...SimpleImageD,
      src: props?.image?.url,
      alt: props?.image?.title,
    },
    heading: {
      ...SimpleHeadingD,
      textContent: props?.heading,
      as: 'h4',
      colour: 'cs2',
    },
    para: {
      ...SimpleParagraphD,
      data: props?.description || '',
    },
    btn: props?.link ? getPrimaryLinkProps(props?.link) : undefined,
    htmlAttr: props?.htmlAttr,
  }
  return InsightSecondaryProps
}

export function getInsightPrimaryProps(props: unknown) {
  const InsightPrimaryProps: InsightPrimaryCTAI = {
    isLightMode: props?.isLightMode,
    bgImage: props?.backgroundImage
      ? {
          ...BackgroundImageD,
          src: props?.backgroundImage?.url,
          alt: props?.backgroundImage?.title,
        }
      : undefined,
    variant: props?.backgroundImage
      ? 'Background'
      : props?.isLightMode
        ? 'Light'
        : 'Dark',

    heading: {
      ...SimpleHeadingD,
      textContent: props?.heading,
      as: 'h2',
      colour: 'cs2',
    },
    para: {
      ...SimpleParagraphD,
      data: props?.description || '',
    },
    btn: props?.link ? getPrimaryLinkProps(props?.link) : undefined,
    htmlAttr: props?.htmlAttr,
  }
  return InsightPrimaryProps
}

export function getCTARowHomeProps(props: unknown): CTARowHomeI {
  const ctaRowProps: CTARowHomeI = {
    backgroundImage: {
      src: props?.backgroundImage?.url || '',
      alt: props?.backgroundImage?.title || '',
    },
    contextualInformation: {
      subHeading: {
        textContent: props?.subheading || '',
      },
      heading: {
        textContent: props?.heading || '',
      },
      excerpt: { data: props?.description || '' },
      showButtons: false,
    },
    cta: props?.link ? getPrimaryLinkProps(props.link) : undefined,
    htmlAttr: props?.htmlAttr,
    isLightMode: props?.isLightMode,
  }
  return ctaRowProps
}

export function getInsightHomeProps(props: unknown) {
  const InsightHomeProps: InsightHomeI = {
    isLightMode: props?.isLightMode,
    heading: {
      ...SimpleHeadingD,
      textContent: props?.heading,
    },
    para: {
      ...SimpleParagraphD,
      data: props?.description || '',
    },
    btn: props?.link ? getPrimaryLinkProps(props?.link) : undefined,
    htmlAttr: props?.htmlAttr,
  }
  return InsightHomeProps
}

export function getCTARowFeaturedProps(props: unknown): CTARowFeaturedI {
  const ctaRowProps: CTARowFeaturedI = {
    isLightMode: props?.isLightMode,
    contextualInformation: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subheading,
      },
      excerpt: { data: props?.description },
      buttons: {
        links: props?.buttonGroupCollection?.items,
      },
      showButtons: true,
    },
    backgroundImage: {
      src: props?.backgroundImage?.url,
      alt: props?.backgroundImage?.title,
    },
    isBlur: props?.isBlur,
    htmlAttr: props?.htmlAttr,
  }
  return ctaRowProps
}
