import { SimpleImageI } from '../../components/Multimedia/Images/SimpleImage/interface'

export const getVerticalAlignment = (verticalAlignment: string) => {
  if (verticalAlignment === 'top') return 'top'
  // if (verticalAlignment === "center") return "50%"
  if (verticalAlignment === 'bottom') return 'bottom'
  return ''
}
export const getHorizontalAlignment = (horizontalAlignment: string) => {
  if (horizontalAlignment === 'left') return 'left'
  // if (horizontalAlignment === "center") return "50%"
  if (horizontalAlignment === 'right') return 'right'
  return ''
}

export function getImageProps(props: unknown): SimpleImageI {
  const imageProps: SimpleImageI = {
    src: props?.imageFile?.url,
    alt: props?.altText,
    height: '100%',
    width: '100%',
    objectFit: props?.objectFit ?? 'cover',
    isZoomAble: props?.isZoomAble,
    title: props?.title,
    description: props?.description,
    zoomAspectRatio: `${props?.imageFile?.width} / ${props?.imageFile?.height}`,
    originalHeight: props?.imageFile?.height,
    originalWidth: props?.imageFile?.width,
    objectPosition: `${getVerticalAlignment(props?.verticalAlignment)} ${getHorizontalAlignment(props?.horizontalAlignment)}`,
    htmlAttr: {
      ...props?.htmlAttr,
      style: {
        ...props?.htmlAttr?.style,
        maxWidth: props?.maxWidth,
        maxHeight: props?.ratio ? '500px' : props?.maxHeight,
        height: '100%',
        width: '100%',
        aspectRatio: props?.ratio
          ? props?.ratio
          : props?.imageFile?.width && props?.imageFile?.height
            ? `${props?.imageFile?.width} / ${props?.imageFile?.height}`
            : '1 / 1',
      },
    },
  }
  return imageProps
}
