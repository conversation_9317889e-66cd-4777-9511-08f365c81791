import { LinkI } from '../../components/CTAs/Link/interface'
import { SimpleCTAsI } from '../../components/CTAs/SimpleCTAs/interface'
import { isBrowser } from '../../globals/utils'

export function getPrimaryLinkProps(props: LinkI): SimpleCTAsI {
  let updatedHref =
    props?.externalLink ||
    props?.sectionId ||
    props?.asset?.url ||
    props?.href ||
    '#'

  if (isBrowser() && !props?.sectionId && updatedHref !== '#') {
    updatedHref = getUpdatedHrefFromSlug(updatedHref)
  }

  if (props?.internalLink?.slug) {
    updatedHref =
      (props?.internalLink?.slug === '/' ? '' : '/') + props?.internalLink?.slug
  }

  const linkProps: SimpleCTAsI = {
    ...props ?? {},
    href: updatedHref,
    isIconPrefixed: props?.iconPlacement === 'Prefix' ? true : false,
    /**
     * if isChevron is true then it shows the icon else shows cheveronToArrow
     */
    isLightMode: props?.isLightMode,
    isExperimentation: props?.isExperimentation,
    experimentEvents: props?.experimentEvents,
    isChevron2Arrow: props?.isChevron2Arrow,
    icon: props?.icon,
    textContent: props?.text,
    variant: props?.template?.toLowerCase(),
    type: props?.type,
    target: props?.openInNewTab ? '_blank' : '_self',
    actionContent: props?.actionContent,
    htmlAttr: props?.htmlAttr,
  }

  return linkProps
}

export function getUpdatedHrefFromSlug(href: string, locale: string = 'en-CA') {
  if (!isBrowser()) {
    if (locale !== 'en-CA') {
      return href + '?lang=' + locale?.split('-')?.[0]
    } else {
      return href
    }
  }
  const urlParams = new URLSearchParams(window.location.search)
  const lang = urlParams.get('lang')

  if (lang) return href + '?lang=' + lang?.split('-')?.[0]

  return href
}
