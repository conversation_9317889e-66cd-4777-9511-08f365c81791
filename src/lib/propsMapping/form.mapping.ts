import { l } from '../../globals/utils'
import { FormI } from '../../systems/AltusForms/Form/interface'
import { FormFloatingI } from '../../systems/AltusForms/FormFloating/interface'

// type GetCountdownProps = {
//     template: string
//     targetDate: string
//     heading: string
//     description: string
// }
function processHiddenFields(fields: unknown[]) {
  return fields?.map((field) => field.formField)
}
export function getFromsProps(props: unknown): FormI {
  l('props', props)
  const formProps: FormI = {
    data: props,
    hiddenFields: processHiddenFields(props?.hiddenFieldsCollection?.items),
  }
  return formProps
}

export function getFloatingFormProps(props: unknown): FormFloatingI {
  l('floating form props', props)
  const formProps: FormFloatingI = {
    data: props,
    hiddenFields: processHiddenFields(props?.hiddenFieldsCollection?.items),
  }
  return formProps
}
