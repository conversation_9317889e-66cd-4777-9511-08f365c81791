import { AccordionBlockDetailedI } from '../../components/Accordions/AccordionBlock/AccordionBlockDetailed/interface'
import { AccordionBlockGenericI } from '../../components/Accordions/AccordionBlock/AccordionBlockGeneric/interface'
import { AccordionCardI } from '../../components/Accordions/AccordionCard/interface'
import { AccordionContentD } from '../../components/Accordions/AccordionGeneric/@Core/defaults'
import { AccordionGenericI } from '../../components/Accordions/AccordionGeneric/interface'
import { SimpleAccordionItemD } from '../../components/Accordions/SimpleAccordion/@Core/SImpleAccordionItem/defaults'
import { getPrimaryLinkProps } from './link.mapping'

export function getAccordionGenericIProps(props: unknown): AccordionGenericI {
  const accordionsMain = props?.accordionItemsCollection?.items?.map((item) => {
    // debugger
    return {
      accordionItem: {
        ...SimpleAccordionItemD,
        acrTab: {
          heading: {
            // ...SimpleAccordionItemD.acrTab?.heading,
            as: 'h3',
            colour: 'cp1',

            textContent: item?.accordionItemHeading,
          },
        },
      },
      accordionContent: {
        ...AccordionContentD,
        video: item.accordionItemPlayerCollection.items[0],

        media: {
          ...AccordionContentD.media,
          src: item?.accordionItemImage?.url,
          alt: item?.accordionItemImage?.title,
        },
        text: {
          data: item?.content,
        },
        cta: item?.itemLink ? getPrimaryLinkProps(item?.itemLink) : undefined,
        /*{
          href: item?.itemLink?.internalLink?.slug
            ? item?.itemLink?.internalLink?.slug
            : item?.itemLink?.externalLink || '#',
          icon: item?.itemLink?.icon,
          isIconPrefixed:
            item?.itemLink?.iconPlacement === 'Prefix' ? false : true,
          textContent: item?.itemLink?.text,
          variant: item?.itemLink?.template.toLowerCase(),
          isLightMode: item?.itemLink?.isLightMode,
          isChevron2Arrow: item?.itemLink?.isChevron2Arrow,
          target: item?.itemLink?.openInNewTab ? '_blank' : '_self',
        }*/
      },
    }
  })
  const cardProps: AccordionGenericI = {
    htmlAttr: props.htmlAttr,

    contextualInfo: {
      heading: {
        textContent: props?.heading,
      },
      subHeading: {
        textContent: props?.subheading,
      },
      showButtons: false,
      excerpt: {
        data: props?.description,
      },
    },
    isOpen: props?.isOpen,
    isFaq: props?.isFaq,
    headingAlignment: props?.headingAlignment,
    accordionsMain,
  }

  return cardProps
}

export function getAccordionCardIProps(props: unknown): AccordionCardI {
  const accordionsMain = props?.accordionItemsCollection?.items?.map((item) => {
    // debugger
    return {
      accordionItem: {
        ...SimpleAccordionItemD,
        acrTab: {
          ...SimpleAccordionItemD.acrTab,
          heading: {
            ...SimpleAccordionItemD.acrTab?.heading,
            as: 'h3',
            // colour: 'cp1',
            textContent: item?.accordionItemHeading,
          },
          isBTop: false,
          isBBottom: false,
          isBLeft: false,
          isBRight: false,
          isRounded: false,
          currentIndex: 0,
        },
      },
      accordionContent: {
        ...AccordionContentD,
        video: item.accordionItemPlayerCollection.items[0],
        media: {
          ...AccordionContentD.media,
          src: item?.accordionItemImage?.url,
          alt: item?.accordionItemImage?.title,
        },
        text: {
          data: item?.content,
        },
        cta: item?.itemLink
          ? getPrimaryLinkProps(item?.itemLink)
          : undefined /*{
          href: item?.itemLink?.internalLink?.slug
            ? item?.itemLink?.internalLink?.slug
            : item?.itemLink?.externalLink || '#',
          icon: item?.itemLink?.icon,
          isIconPrefixed:
            item?.itemLink?.iconPlacement === 'Prefix' ? false : true,
          textContent: item?.itemLink?.text,
          variant: item?.itemLink?.template.toLowerCase(),
          isLightMode: item?.itemLink?.isLightMode,
          isChevron2Arrow: item?.itemLink?.isChevron2Arrow,
          target: item?.itemLink?.openInNewTab ? '_blank' : '_self',
        } */,
      },
    }
  })
  const cardProps: AccordionCardI = {
    htmlAttr: props.htmlAttr,
    isFaq: props?.isFaq,
    contextualInfo: {
      heading: {
        textContent: props.heading,
      },
      subHeading: {
        textContent: props.subheading,
      },
      showButtons: false,
      excerpt: {
        data: props.description,
      },
    },
    headingAlignment: props?.headingAlignment,
    accordionsMain,
  }
  return cardProps
}

export function getAccordionBlockDetailedProps(
  props: unknown
): AccordionBlockDetailedI {
  const accordionsMain = props?.accordionItemsCollection?.items?.map((item) => {
    const accordionContentDetailed = item?.itemBlocksCollection?.items.map(
      (item) => {
        const linkList = item?.linkListCollection?.items?.map((item) => {
          return {
            ...getPrimaryLinkProps(item),
            isChevron2Arrow: false,
            variant: item?.template,
            textContent: item?.text,
            icon: item?.icon,
            isIconPrefixed: false,
            href:
              (item?.internalLink?.slug
                ? '/' + item?.internalLink.slug
                : item?.externalLink) ||
              item?.sectionId ||
              item?.asset?.url ||
              '#',
          }
        })

        return {
          // item?.richContent?.content
          heading: {
            textContent: '',
            as: 'h3',
            displayAs: 'h4',
          },
          subHeading: {
            data: item?.richContent || '',
          },
          reportList: {
            linkList: linkList || [],
          },
          banner: {
            img: {
              src: item?.image?.url,
              alt: item?.image?.title,
            },
            title: {
              textContent: item?.heading,
            },
          },
        }
      }
    )

    return {
      accordionItem: {
        ...SimpleAccordionItemD,
        acrTab: {
          ...SimpleAccordionItemD.acrTab,
          isBTop: false,
          isBLeft: false,
          isBRight: false,
          isBBottom: false,
          heading: {
            ...SimpleAccordionItemD.acrTab?.heading,
            as: 'h2',
            displayAs: 'h3',
            fontFamily: 'fSerif',
            textContent: item?.accordionItemHeading,
          },
        },
      },
      accordionContentDetailed,
    }
  })
  const cardProps: AccordionBlockDetailedI = {
    accordionsMain,
    isAutoClose: true,
    isCloseable: true,
    isOpen: props?.isOpen,
    isFaq: props?.isFaq,
    htmlAttr: props.htmlAttr,
  }
  return cardProps
}

export function getAccordionBlockGenericIProps(
  props: unknown
): AccordionBlockGenericI {
  const BlockGenericProps: AccordionBlockGenericI = {
    accordionsMain: props?.accordionItemsCollection?.items?.map(
      (item: unknown) => {
        return {
          accordionItem: {
            ...SimpleAccordionItemD,
            acrTab: {
              ...SimpleAccordionItemD?.acrTab,
              isBTop: false,
              isBLeft: false,
              isBRight: false,
              isBBottom: false,
              heading: {
                ...SimpleAccordionItemD?.acrTab?.heading,
                as: 'h5',
                displayAs: 'h5',
                fontFamily: 'fSansBld ',

                textContent: item?.accordionItemHeading,
              },
            },
          },
          accordionContentGeneric: {
            linkList: item?.linkListCollection?.items?.map((link: unknown) => {
              return {
                ...getPrimaryLinkProps(link),
                isChevron2Arrow: false,
                variant: link?.template,
                textContent: link?.text,
                href:
                  (link?.internalLink?.slug
                    ? '/' + link?.internalLink.slug
                    : link?.externalLink) ||
                  link?.sectionId ||
                  link?.asset?.url ||
                  '#',
                icon: link?.icon,
                isIconPrefixed: false,
              }
            }),
          },
        }
      }
    ),
    isOpen: props?.isOpen,
    isFaq: props?.isFaq,
    htmlAttr: props.htmlAttr,
  }

  return BlockGenericProps
}
