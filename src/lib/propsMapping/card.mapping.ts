import moment from 'moment'
import 'moment/locale/de'
import 'moment/locale/fr'
import {ArticleItemI} from '../../components/ArticleLists/ArticleItem/interface'
import {CardComparisonI} from '../../components/Cards/CardComparisons/CardComparison/interface'
import {CardComparisonIconI} from '../../components/Cards/CardComparisons/CardComparisonIcon/interface'
import {LiveEventI} from '../../components/Cards/CardEvents/LiveEvent/interface'
import {OnDemandI} from '../../components/Cards/CardEvents/OnDemand/interface'
import {CardFeaturedLandscapeI} from '../../components/Cards/CardFeatured/CardFeaturedLandscape/interface'
import {CardFeaturedRowsListI} from '../../components/Cards/CardFeaturedRowsList/interface'
import {CardGenericFeaturedListI} from '../../components/Cards/CardGeneric/CardGenericFeaturedList/interface'
import {CardGenericHeadlineI} from '../../components/Cards/CardGeneric/CardGenericHeadline/interface'
import {CardGenericMegamenuI} from '../../components/Cards/CardGeneric/CardGenericMegamenu/interface'
import {CardGenericNoImageI} from '../../components/Cards/CardGeneric/CardGenericNoImage/interface'
import {CardGenericSearchI} from '../../components/Cards/CardGeneric/CardGenericSearch/interface'
import {CardGenericSearchFeaturedI} from '../../components/Cards/CardGeneric/CardGenericSearchFeatured/interface'
import {CardGenericSmallI} from '../../components/Cards/CardGeneric/CardGenericSmall/interface'
import {CardLandscapeHomeI} from '../../components/Cards/CardGeneric/CardLandscapeHome/interface'
import {SimpleCardGenericI} from '../../components/Cards/CardGeneric/SimpleCardGeneric/interface'
import {CardIconI} from '../../components/Cards/CardIconLogo/CardIcon/interface'
import {CardImageHomeI} from '../../components/Cards/CardIconLogo/CardImageHome/interface'
import {CardLogoI} from '../../components/Cards/CardIconLogo/CardLogo/interface'
import {CardMegamenuI} from '../../components/Cards/CardMegamenu/interface'
import {CardPeopleLandscapeRowI} from '../../components/Cards/CardPeople/CardPeopleLandscapeRow/interface'
import {CardPeoplePortraitI} from '../../components/Cards/CardPeople/CardPeoplePortrait/interface'
import {getPrimaryLinkProps} from './link.mapping'

export function getEventCardProps(props: unknown): LiveEventI {
    var address = ''
    if(props?.address) {
        address = Object.entries(props?.address)
            .filter(([key, val]) => key !== 'altusUrl' && props?.address[key])
            .map(([_, val]) => val)
            .join(', ')
    }

    const cardProps: LiveEventI = {
        isLightMode: props?.isLightMode,
        contextualInfo: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            showButtons: false,
            excerpt: {data: undefined}
        },
        eventBanner: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            width: '333px',
            height: '280px'
        },
        liveEventDetail: {
            date: {
                textContent: `${moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY')} - ${moment(props?.endTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY')}` // using moment js
            },
            time: {
                textContent:
                    moment(props?.startTime).locale(props?.locale).format('LT') +
                    (props?.timeZone ? ` (${props?.timeZone})` : '') // using moment js
            },
            venue: {textContent: address},
            mapLink: props?.address && {
                href: props?.address ? props?.address?.altusUrl : undefined
            }
        },
        eventText: {data: props?.description},
        cta: {links: props?.buttonGroupCollection?.items}
    }
    return cardProps
}

export function getOnDemandWebinarProps(props: unknown): OnDemandI {
    const cardProps: OnDemandI = {
        isLightMode: props?.isLightMode,
        contextualInfo: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            }
        },
        eventBanner: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            width: '333px',
            height: '280px'
        },
        EventTypeText: {
            textContent: props?.eventType
        },
        eventText: {data: props?.description},
        cta: {links: props?.buttonGroupCollection?.items}
    }
    return cardProps
}

export function getComparisonCardProps(props: unknown): CardComparisonI {
    const cardProps: CardComparisonI = {
        isLightMode: props?.isLightMode,
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            excerpt: {data: props?.description}
        },
        span: props?.checklist?.map((span: string) => ({
            textContent: span
        })),
        button: props?.button ? getPrimaryLinkProps(props?.button) : undefined
    }
    return cardProps
}

export function getComparisonCardIconProps(
    props: unknown
): CardComparisonIconI {
    const cardProps: CardComparisonIconI = {
        isLightMode: props?.isLightMode,
        image: {
            src: props?.image?.url,
            alt: props?.image?.title
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            excerpt: {data: props?.description}
        },
        span: props?.checklist?.map((span: string) => ({
            textContent: span
        })),
        orientation: props?.orientation,
        size: props?.size,
        button: props?.button ? getPrimaryLinkProps(props?.button) : undefined
    }
    return cardProps
}

export function getGenericCardProps(props: unknown): SimpleCardGenericI {
    const cardProps: SimpleCardGenericI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        date: props?.startTime
            ? {
                textContent: moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY') // using moment js
            }
            : undefined,
        excerpt: {data: props?.description},
        htmlAttr: props?.htmlAttributes
    }
    return cardProps
}

export function getGenericCardSmallProps(props: unknown): CardGenericSmallI {
    const cardProps: CardGenericSmallI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        date: props?.startTime
            ? {
                textContent:
                    props?.startTime && props?.locale
                        ? moment(props?.startTime)
                            .locale(props?.locale)
                            .format('MMM D, YYYY') // using moment js
                        : ''
            }
            : undefined,
        htmlAttr: props?.htmlAttributes
    }
    return cardProps
}

export function getCardGenericHeadlineProps(
    props: unknown
): CardGenericHeadlineI {
    const cardProps: CardGenericHeadlineI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        date: props?.startTime
            ? {
                textContent: moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY') // using moment js
            }
            : undefined,
        cta: props?.button && {
            href: props?.button?.internalLink?.slug
                ? props?.button?.internalLink?.slug
                : props?.button?.externalLink || sectionId || '',
            textContent: undefined
        },
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getCardGenericNoImageProps(
    props: unknown
): CardGenericNoImageI {
    const cardProps: CardGenericNoImageI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        date: props?.startTime
            ? {
                textContent: moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY') // using moment js
            }
            : undefined,
        excerpt: {data: props?.description},
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getCardGenericFeaturedListProps(
    props: unknown
): CardGenericFeaturedListI {
    const cardProps: CardGenericFeaturedListI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        date: props?.startTime
            ? {
                textContent: moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY') // using moment js
            }
            : undefined,
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getCardGenericMegamenuProps(
    props: unknown
): CardGenericMegamenuI {
    const cardProps: CardGenericMegamenuI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        date: props?.startTime
            ? {
                textContent: moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY') // using moment js
            }
            : undefined,
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getCardGenericSearchProps(props: unknown): CardGenericSearchI {
    const cardProps: CardGenericSearchI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        excerpt: {data: props?.description},
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getCardGenericSearchFeaturedProps(
    props: unknown
): CardGenericSearchFeaturedI {
    const cardProps: CardGenericSearchFeaturedI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        excerpt: {data: props?.description},
        date: props?.startTime
            ? {
                textContent: moment(props?.startTime)
                    .locale(props?.locale)
                    .format('MMM D, YYYY') // using moment js
            }
            : undefined,
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getFeaturedProps(props: unknown): CardFeaturedLandscapeI {
    const cardProps: CardFeaturedLandscapeI = {
        isLightMode: props?.isLightMode,
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            excerpt: {data: {type: 'doc'}}
        },
        orientation: props?.orientation,
        image: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        richText: props?.description && {data: props?.description}
    }

    return cardProps
}

export function getCardIconProps(props: unknown): CardIconI {
    const cardProps: CardIconI = {
        isLightMode: props?.isLightMode,
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            excerpt: {data: props?.description}
        },
        media: {
            icon: props?.icon,
            size: props?.size
        },
        htmlAttr: props?.htmlAttributes
    }

    return cardProps
}

export function getCardLogoProps(props: unknown): CardLogoI {
    const cardProps: CardLogoI = {
        isLightMode: props?.isLightMode,
        media: {
            src: props?.image?.url,
            alt: props?.image?.title
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            excerpt: {data: props?.description}
        },
        size: props?.size,
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined /*  {
      href: props?.button?.internalLink?.slug
        ? props?.button?.internalLink?.slug
        : props?.button?.externalLink || '#',
      icon: props?.button?.icon,
      isIconPrefixed: props?.button?.iconPlacement === 'Prefix' ? false : true,
      textContent: props?.button?.text,
      variant: props?.button?.template.toLowerCase(),
      isLightMode: props?.button?.isLightMode,
      isChevron2Arrow: props?.button?.isChevron2Arrow,
      target: props?.button?.openInNewTab ? '_blank' : '_self',
    } */,
        alignment: props?.alignment,
        htmlAttr: props?.htmlAttributes,
        orientation: props?.orientation
    }
    // debugger
    return cardProps
}

export function getPeopleProps(props: unknown): CardPeoplePortraitI {
    let tags = []
    if(props?.tags && props?.tags?.length !== 0) {
        tags = props?.tags?.map((tag: string) => ({text: {textContent: tag}}))
    }

    const cardProps: CardPeoplePortraitI = {
        isLightMode: props?.isLightMode,
        htmlAttr: props?.htmlAttributes,
        person: {
            fullName: {textContent: props?.fullName},
            jobTitle: {textContent: props?.jobTitle},
            avatar: {
                src: props?.image?.url,
                alt: props?.image?.title,
                height: '100%',
                width: '100%',
                objectFit: 'cover'
            },
            href: 'https://altusgroup.com',
            companyName: {textContent: props?.companyName},
            bio:
                props?.description &&
                props?.description?.content?.length > 0 &&
                props?.description?.content[0]?.content
                    ? {data: props?.description}
                    : undefined
        },
        tags: tags,
        orientation: props?.orientation,
        showButton: true,
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined
    }

    return cardProps
}

export function getCardMegaMenuProps(props: unknown): CardMegamenuI {
    const cardProps: CardMegamenuI = {
        isLightMode: props?.isLightMode,
        heading: {textContent: props?.heading},
        para: {data: props?.description}
    }

    return cardProps
}

export function getCardFeaturedRowsListProps(
    props: unknown
): CardFeaturedRowsListI {
    const cardProps: CardFeaturedRowsListI = {
        isLightMode: props?.isLightMode,
        number: {textContent: props?.number},
        date: {
            textContent: moment(props?.startTime)
                .locale(props?.locale)
                .format('MMM D, YYYY')
        },
        heading: {textContent: props?.heading},
        cta: props?.button
            ? getPrimaryLinkProps(props?.button)
            : undefined /* props?.button && {
      href: props?.button?.internalLink?.slug
        ? props?.button?.internalLink?.slug
        : props?.button?.externalLink || '',
      icon: props?.button?.icon,
      isIconPrefixed: props?.button?.iconPlacement === 'Prefix' ? false : true,
      textContent: props?.button?.text,
      variant: props?.button?.template.toLowerCase(),
      isLightMode: props?.button?.isLightMode,
      isChevron2Arrow: props?.button?.isChevron2Arrow,
      target: props?.button?.openInNewTab ? '_blank' : '_self',
    } */
    }

    return cardProps
}

export function getPeopleLandscapeRowProps(
    props: unknown
): CardPeopleLandscapeRowI {
    const cardProps: CardPeopleLandscapeRowI = {
        isLightMode: props?.isLightMode,
        person: {
            fullName: {textContent: props?.fullName},
            jobTitle: {textContent: props?.jobTitle},
            avatar: {
                src: props?.image?.url,
                alt: props?.image?.title,
                height: '100%',
                width: '100%',
                objectFit: 'cover'
            },
            companyName: {textContent: props?.companyName},
            bio:
                props?.description &&
                props?.description?.content?.length > 0 &&
                props?.description?.content[0]?.content
                    ? {data: props?.description}
                    : undefined
        },
        tags: props?.tags?.map((tag: string) => ({
            text: {
                textContent: tag
            }
        })),
        htmlAttr: props?.htmlAttributes,
        cta: props?.button ? getPrimaryLinkProps(props?.button) : undefined
        /*  showButton: false */
    }

    return cardProps
}

export function getArticleListItemIProps(props: unknown): ArticleItemI {
    const cardProps: ArticleItemI = {
        isLightMode: props?.isLightMode,
        date: {
            textContent: moment(props?.publishDate)
                .locale(props?.locale)
                .format('MMM D, YYYY') // using moment js
        },
        image: {
            src: props?.image?.url,
            alt: props?.image?.title,
            height: '100%',
            width: '100%',
            objectFit: 'cover'
        },
        cta: props?.button
            ? getPrimaryLinkProps(props?.button)
            : props?.externalLink
                ? {
                    href: props.externalLink,
                    target: props?.openInNewTab ? '_blank' : '_self'
                }
                : undefined,
        /*  props?.button &&{
          href:
            props?.button?.externalLink || props?.button?.sectionId || props?.button?.internalLink || '',
        } */ subCta: props?.subheadingExternalLink
            ? getPrimaryLinkProps(props?.subheadingExternalLink)
            : undefined /*  {
      href:
        props?.subheadingExternalLink ||
        props?.subheadingExternalLink ||
        props?.subheadingExternalLink ||
        '',
    } */,
        contextualInformation: {
            showButtons: false,
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading
            },
            excerpt: {
                data: props?.description
            }
        }
    }
    return cardProps
}

export function getCardImageHomeProps(props: unknown): CardImageHomeI {
    const cardProps: CardImageHomeI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        cta: props?.button && {
            href: props?.button?.internalLink?.slug
                ? props?.button?.internalLink?.slug
                : props?.button?.externalLink || props?.button?.sectionId || '',
            textContent: undefined
        },
        media: {
            src: props?.image?.url,
            alt: props?.image?.title
        },
        size: props?.size
    }

    return cardProps
}

export function getLandscapeHomeProps(props: unknown): CardLandscapeHomeI {
    const cardProps: CardLandscapeHomeI = {
        isLightMode: props?.isLightMode,
        heading: {
            textContent: props?.heading
        },
        thumbnail: {
            src: props?.image?.url,
            alt: props?.image?.name,
            objectFit: 'cover',
            height: '100%',
            width: '100%'
        },
        cards: props?.cardsCollection?.items?.map((item: unknown) =>
            getCardImageHomeProps(item)
        ),
        excerpt: {data: props?.description},
        htmlAttr: props?.htmlAttributes
    }
    return cardProps
}