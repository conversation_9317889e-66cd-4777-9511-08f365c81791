import { ReactNode } from 'react'
import SCSSVars from '../../brands/altus/styles/_nextSCSSVars.module.scss'
import ColContainer from '../../components/Containers/ColContainer'
import { RowContainerI } from '../../components/Containers/RowContainer/interface'
import { SimpleContainerI } from '../../components/Containers/SimpleContainer/interface'
import { isBrowser } from '../../globals/utils'
import { useWindowSize } from '../../hooks/useWindowSize'
import { mapDataToComponent } from './index.mapping'

const CalculateComponent = (props: any): ReactNode => {
  // Extract data based on the '__typename' property
  const data = props

  // Map data to a component using the mapping function
  const Comp = mapDataToComponent({
    ...data,
    onChange: props?.onChange,
    values: props?.values,
  })

  return Comp
}

// Define a function named 'calculate' that takes three parameters:
// - 'gap': The gap between grid items, defaulting to 30 if not provided.
// - 'column': The number of columns in the grid.
// - 'width': The total width of the grid container.
const calculate = (gap: number = 30, column: number, width: number) => {
  // Retrieve the minimum width from a SCSS variable named 'minW'.
  const minWidth = SCSSVars.minW

  // Calculate the total gap width by multiplying the gap with the number of columns minus one.
  const gapCount = column - 1
  const gapWidth = gapCount * gap

  // Calculate the maximum width of each grid item by considering the gap and total width.
  let gridItemMaxWidth =
    Math.floor((100 - (gapWidth / width) * 100) / column) + '%'

  // Generate a CSS grid template columns string using 'auto-fill' and 'minmax'.
  const gridTemplateColumns = `repeat(auto-fill, minmax(max(${minWidth}, ${gridItemMaxWidth}), 1fr))`

  // Return the generated grid template columns string.
  return gridTemplateColumns
}

/**
 * Function to retrieve properties for a layout container.
 *
 * @param props - Properties passed to the function.
 * @returns Layout container properties conforming to RowContainerI interface.
 */
export function getLayoutContainerProps(props: any): RowContainerI {
  // Getting the window size and width using a custom hook named 'useWindowSize'.
  let size: string | null = 'large'
  let width: number | null = 1080

  if (isBrowser()) {
    size = useWindowSize().size
    width = useWindowSize().width
  }

  // Default properties for the layout container.
  const layoutContainerProps: SimpleContainerI = {
    htmlAttr: {
      // Spread the existing html attributes, if any.
      ...props?.htmlAttr,
      // Add or overwrite the class name to include 'defaultLayout'.
      className: `defaultLayout ${props?.isFullXBleed ? '' : props?.htmlAttr?.className}`,
      // Add or overwrite the style, setting it to display as a grid.
      style: {
        ...props?.htmlAttr?.style,
        display: 'grid',
        rowGap: props?.rowGap,
        columnGap: props?.columnGap,
        // Set the grid template columns using the 'calculate' function based on conditions.
        gridTemplateColumns: calculate(
          props?.columnGap
            ? parseInt(props?.columnGap)
            : size === 'small'
              ? 30
              : 60, // Gap size.
          Number(props?.numberOfColumns) ?? 4, // Number of columns, defaulting to 4 if not provided.
          (width as number) > 1450 ? 1450 : (width as number) // Ensure width doesn't exceed 1450.
        ),
      },
    },
    children: (
      <>
        {/* Iterate through items and create ColContainer components */}
        {props?.layoutContainerItemsCollection?.items?.map((item: any) => (
          // Create a column container for each item.
          <ColContainer
            // Mapping the contentfull data to component
            key={item?.sys?.id}
            flexDirection='column'
            justifyContent={props?.verticalContentAlignment}
            alignItems={props?.horizontalContentAlignment}
            minWidth={SCSSVars.minW}
            width={'100%'}
            htmlAttr={item?.htmlAttr}
          >
            {/* Create a calculated component based on the 'CalculateComponent' */}
            <CalculateComponent
              key={item?.sys?.id}
              {...item}
              componentId={item?.sys?.id}
              componentTypeName={item.__typename}
              onChange={item?.onChange}
            />
          </ColContainer>
        ))}
      </>
    ),
  }

  // Return the layout container properties.
  return layoutContainerProps
}
