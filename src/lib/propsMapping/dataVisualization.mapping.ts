import { AreaChartI } from '../../components/DataVisualizers/AreaChart/interface'
import { BarChartI } from '../../components/DataVisualizers/BarChart/interface'
import { CandlestickChartI } from '../../components/DataVisualizers/CandlestickChart/interface'
import { ComboChartI } from '../../components/DataVisualizers/ComboChart/interface'
import { DoughnutChartI } from '../../components/DataVisualizers/DoughnutChart/interface'
import { LineChartI } from '../../components/DataVisualizers/LineChart/interface'
import { PieChartI } from '../../components/DataVisualizers/PieChart/interface'
import { TreemapChartI } from '../../components/DataVisualizers/TreemapChart/interface'
import { WaterfallChartI } from '../../components/DataVisualizers/WaterfallChart/interface'
import {
  defaults,
  fetchGlobalConfigData,
  getParsedData
} from '../../components/DataVisualizers/utils'

export async function getLineChartProps(props: unknown): Promise<LineChartI> {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const axesPreference = {
    ...defaultData?.axes,
    ...(dvConfiguration?.axes ?? {}),
  }

  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const seriesX = {
    ...defaultData?.chart?.['x-axis'],
    ...(dvConfiguration?.chart?.['x-axis'] ?? {}),
  }
  const seriesY = {
    ...defaultData?.chart?.['y-axis'],
    ...(dvConfiguration?.chart?.['y-axis'] ?? {}),
  }
  const markPreference = {
    ...defaultData?.mark,
    ...(dvConfiguration?.mark ?? {}),
  }

  const data = (await getParsedData(props?.source?.url)) ?? []

  const preferences = {
    generalPreference,
    stylePreference,
    gridPreference,
    axesPreference,
    colors,
    seriesX,
    seriesY,
    markPreference,

  }

  const chartProps: LineChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export async function getAreaChartProps(props: unknown): Promise<AreaChartI> {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const axesPreference = {
    ...defaultData?.axes,
    ...(dvConfiguration?.axes ?? {}),
  }

  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const seriesX = {
    ...defaultData?.chart?.['x-axis'],
    ...(dvConfiguration?.chart?.['x-axis'] ?? {}),
  }
  const seriesY = {
    ...defaultData?.chart?.['y-axis'],
    ...(dvConfiguration?.chart?.['y-axis'] ?? {}),
  }
  const markPreference = {
    ...defaultData?.mark,
    ...(dvConfiguration?.mark ?? {}),
  }

  const data = (await getParsedData(props?.source?.url)) ?? []

  const preferences = {
    generalPreference,
    stylePreference,
    axesPreference,
    gridPreference,
    colors,
    seriesX,
    seriesY,
    markPreference,
  }

  const chartProps: AreaChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export async function getBarChartProps(props: unknown): Promise<BarChartI> {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const axesPreference = {
    ...defaultData?.axes,
    ...(dvConfiguration?.axes ?? {}),
  }

  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const seriesX = {
    ...defaultData?.chart?.['x-axis'],
    ...(dvConfiguration?.chart?.['x-axis'] ?? {}),
  }
  const seriesY = {
    ...defaultData?.chart?.['y-axis'],
    ...(dvConfiguration?.chart?.['y-axis'] ?? {}),
  }
  const markPreference = {
    ...defaultData?.mark,
    ...(dvConfiguration?.mark ?? {}),
  }

  const data = (await getParsedData(props?.source?.url)) ?? []

  const preferences = {
    generalPreference,
    stylePreference,
    axesPreference,
    gridPreference,
    colors,
    seriesX,
    seriesY,
    markPreference
  }

  const chartProps: BarChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export async function getPieChartProps(props: unknown): Promise<PieChartI> {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }

  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }

  const data = (await getParsedData(props?.source?.url)) ?? []
  const preferences = {
    generalPreference,
    stylePreference,
    gridPreference,
    colors,
  }

  const chartProps: PieChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export async function getComboChartProps(props: unknown): Promise<ComboChartI> {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const axesPreference = {
    ...defaultData?.axes,
    ...(dvConfiguration?.axes ?? {}),
  }
  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const chartType = {
    ...defaultData?.chart?.chartType,
    ...(dvConfiguration?.chart?.chartType ?? {}),
  }
  const seriesX = {
    ...defaultData?.chart?.['x-axis'],
    ...(dvConfiguration?.chart?.['x-axis'] ?? {}),
  }
  const seriesY = {
    ...defaultData?.chart?.['y-axis'],
    ...(dvConfiguration?.chart?.['y-axis'] ?? {}),
  }
  const markPreference = {
    ...defaultData?.mark,
    ...(dvConfiguration?.mark ?? {}),
  }

  const data = (await getParsedData(props?.source?.url)) ?? []

  const preferences = {
    generalPreference,
    stylePreference,
    axesPreference,
    gridPreference,
    colors,
    chartType,
    seriesX,
    seriesY,
    markPreference
  }

  const chartProps: ComboChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export async function getDoughnutChartProps(
  props: unknown
): Promise<DoughnutChartI> {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const data = (await getParsedData(props?.source?.url)) ?? []
  const preferences = {
    generalPreference,
    stylePreference,
    gridPreference,
    colors,
  }

  const chartProps: DoughnutChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}
export const getWaterfallChartProps = async (
  props: any
): Promise<WaterfallChartI> => {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }

  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const axesPreference = {
    ...defaultData?.axes,
    ...(dvConfiguration?.axes ?? {}),
  }
  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const seriesX = {
    ...defaultData?.chart?.['x-axis'],
    ...(dvConfiguration?.chart?.['x-axis'] ?? {}),
  }
  const seriesY = {
    ...defaultData?.chart?.['y-axis'],
    ...(dvConfiguration?.chart?.['y-axis'] ?? {}),
  }
  const markPreference = {
    ...defaultData?.mark,
    ...(dvConfiguration?.mark ?? {}),
  }
  const data = (await getParsedData(props?.source?.url)) ?? []
  const preferences = {
    generalPreference,
    stylePreference,
    axesPreference,
    gridPreference,
    colors,
    seriesX,
    seriesY,
    markPreference
  }

  const chartProps: WaterfallChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export const getCandlestickChartProps = async (
  props: any
): Promise<CandlestickChartI> => {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const axesPreference = {
    ...defaultData?.axes,
    ...(dvConfiguration?.axes ?? {}),
  }
  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }
  const seriesX = {
    ...defaultData?.chart?.['x-axis'],
    ...(dvConfiguration?.chart?.['x-axis'] ?? {}),
  }
  const seriesY = {
    ...defaultData?.chart?.['y-axis'],
    ...(dvConfiguration?.chart?.['y-axis'] ?? {}),
  }
  const markPreference = {
    ...defaultData?.mark,
    ...(dvConfiguration?.mark ?? {}),
  }
  const data = (await getParsedData(props?.source?.url)) ?? []


  const preferences = {
    generalPreference,
    stylePreference,
    axesPreference,
    gridPreference,
    colors,
    seriesX,
    seriesY,
    markPreference
  }

  const chartProps: CandlestickChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
    locale: props?.locale,
  }
  return chartProps
}

export const getTreemapChartProps = async (
  props: any
): Promise<TreemapChartI> => {
  const res = await fetchGlobalConfigData()
  const template = props?.template
  const dvData = props?.data?.json?.content?.[0]?.content?.[0]?.value ?? '{}'
  const dvConfiguration = JSON.parse(dvData)
  const defaultData =
    res?.data?.DataVisualization?.[template] ?? defaults?.[template]
  const generalPreference = {
    ...defaultData?.general,
    ...(dvConfiguration?.general ?? {}),
  }
  const stylePreference = {
    ...defaultData?.styles,
    ...(dvConfiguration?.styles ?? {}),
  }
  const gridPreference = {
    ...defaultData?.grid,
    ...(dvConfiguration?.grid ?? {}),
  }
  const colors = {
    ...defaultData?.chart?.colors,
    ...(dvConfiguration?.chart?.colors ?? {}),
  }

  const data = (await getParsedData(props?.source?.url)) ?? []

  const preferences = {
    generalPreference,
    stylePreference,
    gridPreference,
    colors,
  }

  const chartProps: TreemapChartI = {
    id: props?.sys?.id,
    preferences: preferences,
    fileData: data,
  }

  return chartProps
}
