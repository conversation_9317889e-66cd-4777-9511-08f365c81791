import { HeroCustomerStoriesI } from '../../components/Heros/HeroCustomerStories/interface'
import { SimpleHeroDropdownI } from '../../components/Heros/HeroDropdown/SimpleHeroDropdown/interface'
import { HeroInsightsArticleI } from '../../components/Heros/HeroInsightsArticle/interface'
import { HeroInsightsI } from '../../components/Heros/HeroInsigths/interface'
import { HeroPersonI } from '../../components/Heros/HeroPerson/interface'
import { HeroArgusI } from '../../components/Heros/HeroSingles/HeroArgus/interface'
import { HeroHomeI } from '../../components/Heros/HeroSingles/HeroHome/interface'
import { HeroSingleBgColourI } from '../../components/Heros/HeroSingles/HeroSingleBgColour/interface'
import { HeroSingleDisplayI } from '../../components/Heros/HeroSingles/HeroSingleDisplay/interface'
import { HeroSingleDynamicI } from '../../components/Heros/HeroSingles/HeroSingleDynamic/interface'
import { HeroSingleGenericI } from '../../components/Heros/HeroSingles/HeroSingleGeneric/interface'
import { HeroSingleHomeTemplateI } from '../../components/Heros/HeroSingles/HeroSingleHomeTemplate/interface'
import { HeroSingleLowerI } from '../../components/Heros/HeroSingles/HeroSingleLower/interface'
import { HeroSoftwareI } from '../../components/Heros/HeroSingles/HeroSoftware/interface'
import { HeroTwoColumnI } from '../../components/Heros/HeroTwoColumns/HeroTwoColumn/interface'
import { SimpleHeroI } from '../../components/Heros/SimpleHero/interface'
import { lowerCaseFirstLetter } from '../../systems/AFS/AFSPages/utils'
import { getImageProps } from './image.mapping'
import { mapDataToComponent } from './index.mapping'
import { getPrimaryLinkProps } from './link.mapping'

export function getHeroSingleDisplayProps(props: unknown): HeroSingleDisplayI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    const heroProps: HeroSingleDisplayI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList?.heading,
            htmlAttr: props?.htmlAttr
        },
        showDropdown: props?.showDropdown,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription || '' },
            buttons: {
                links: props?.buttonsCollection?.items || ''
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        isBgImageEffect: props?.isBgImageEffect,
        bgImage: {
            ...getImageProps(props?.bgImage),
            width: '333px',
            height: '280px'
        },
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        htmlAttr: props?.htmlAttr
    }

    return heroProps
}

export function getHeroSingleGenericProps(props: unknown): HeroSingleGenericI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    const heroProps: HeroSingleGenericI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        showDropdown: props?.showDropdown,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription || '' },
            buttons: {
                links: props?.buttonsCollection?.items || ''
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        bgImage: getImageProps(props?.bgImage),
        isBgImageEffect: props?.isBgImageEffect,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        htmlAttr: props?.htmlAttr,
        template: props?.template
    }
    return heroProps
}

export function getHeroSingleBGImageIProps(
    props: unknown
): HeroSingleBgColourI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    const heroProps: HeroSingleBgColourI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        showDropdown: props?.showDropdown,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription },
            buttons: {
                links: props?.buttonsCollection?.items || ''
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        bgColor: props?.htmlAttr?.className,
        isBgImageEffect: props?.isBgImageEffect,
        htmlAttr: props?.htmlAttr
    }
    return heroProps
}

export function getHeroCustomerStoriesProps(
    props: unknown
): HeroCustomerStoriesI {
    let meanulist = []
    if (props?.menuListCollection?.items) {
        meanulist = props?.menuListCollection?.items.map((link: any) => ({
            textContent: link?.heading || '',
            links: link?.linksCollection.items.map((item: unknown) => {
                return {
                    icon: item?.icon || '',
                    textContent: item?.text || '',
                    href: item?.internalLink?.slug
                        ? item?.internalLink.slug
                        : item?.externalLink || '#'
                }
            })
        }))
    }

    const heroProps: HeroCustomerStoriesI = {
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription },
            buttons: {
                links: props?.buttonsCollection.items
            }
        },
        isLightMode: props?.isLightMode,
        height: props?.height,
        logo: {
            src: props?.logo?.url || '',
            alt: props?.logo?.title || ''
        },
        menuList: meanulist,
        bgColor: props?.color || 'bp1',
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        htmlAttr: props?.htmlAttr
    }
    return heroProps
}


export function getHeroSingleLowerProps(props: unknown): HeroSingleLowerI {

    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    const heroProps: HeroSingleLowerI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        showDropdown: props?.showDropdown,
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            excerpt: { data: props?.heroDescription },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            buttons: {
                links: props?.buttonsCollection?.items
            },
            showButtons: props?.buttonsCollection?.items?.length === 0 ? false : true
        },
        isLightMode: props?.isLightMode,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        isBgImageEffect: props?.isBgImageEffect,
        bgImage: {
            ...getImageProps(props?.bgImage),
            objectFit: 'cover'
        },
        htmlAttr: props?.htmlAttr,
        isHeroOverlay: props?.isHeroOverlay,
        template: props?.template
    }
    return heroProps
}

export function getHeroSingleDynamicProps(props: unknown): HeroSingleDynamicI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    let images = []
    if (props?.dynamicImagesCollection?.items !== 0) {
        images = props?.dynamicImagesCollection?.items.map(
            (e: unknown) => e?.url || ''
        )
    }
    let dynamicText = []
    if (props?.dynamicRichTextCollection?.items !== 0) {
        dynamicText = props?.dynamicRichTextCollection.items.map(
            (e: unknown) => e?.content || htmlAttr || isFullXBleed || ''
        )
    }

    const heroProps: HeroSingleDynamicI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        showDropdown: props?.showDropdown,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            // excerpt: { data:dynamicText },
            buttons: {
                links: props?.buttonsCollection?.items
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        images: images,
        dynamicText: dynamicText,
        isBgImageEffect: props?.isBgImageEffect,
        htmlAttr: props?.htmlAttr
    }
    return heroProps
}

export function getHeroTwoColumnProps(props: unknown): HeroTwoColumnI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: props?.isLightMode ? 'cn2' : 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    let links = []
    if (props?.breadCrumb?.linksCollection?.items) {
        links = props?.breadCrumb?.linksCollection?.items.map((link: any) => ({
            textContent: link?.text || '',
            href: link?.externalLink || ''
        }))
    }
    let dynamicImage: any //for two column carousel
    if (props?.dynamicImagesCollection?.items) {
        dynamicImage = props?.dynamicImagesCollection?.items[0]
    }
    let logoImage: any
    if (props?.logo) {
        logoImage = props?.logo
    }

    const heroProps: HeroTwoColumnI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        bgColor: props?.htmlAttr?.className,
        isHeroFirstChild: props?.isHeroFirstChild,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        showDropdown: props?.showDropdown,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cp1'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription },
            buttons: {
                links: props?.buttonsCollection?.items
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        bgImage: {
            ...getImageProps(props?.bgImage)
        },
        isImageLeft: props?.heroTwoColumnType === 'Secondary' ? true : false,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        image: dynamicImage,
        logo: logoImage,
        htmlAttr: props?.htmlAttr
    }

    return heroProps
}

export function getHeroTwoColumnLowerProps(props: unknown): HeroTwoColumnI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text,
                    colour: props?.isLightMode ? 'cn2' : 'cs2'
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: props?.isLightMode ? 'cn2' : 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    const heroProps: HeroTwoColumnI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        bgColor: props?.htmlAttr?.className,
        isHeroFirstChild: props?.isHeroFirstChild,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        showDropdown: props?.showDropdown,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cp1'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription },
            buttons: {
                links: props?.buttonsCollection?.items
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        bgImage: getImageProps(props?.bgImage),
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        htmlAttr: props?.htmlAttr
    }

    return heroProps
}

export function getHeroInsightArticleProps(
    props: unknown
): HeroInsightsArticleI {
    const heroProps: HeroInsightsArticleI = {
        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription || '' },
            buttons: {
                links: props?.buttonsCollection?.items || ''
            }
        },
        // isLightMode: props?.isLightMode,
        height: props?.height,
        bgImage: getImageProps(props?.bgImage),
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        htmlAttr: props?.htmlAttr
    }
    return heroProps
}

export function getHeroInsightsProps(props: unknown): HeroInsightsI {
    const heroProps: HeroInsightsI = {
        htmlAttr: props?.htmlAttr,

        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            subHeading: {
                textContent: undefined
            },
            excerpt: { data: props?.heroDescription || '' }
        },
        // isLightMode: props?.isLightMode,
        height: props?.height,
        bgImage: getImageProps(props?.bgImage),
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        cardCarousel: {
            carouselData: props?.insightsCarouselDataCollection?.items.map(
                (item: unknown) => {
                    return {
                        indicatorsPosition: 'Bottom',
                        indicatorActiveColour: 'bs1s3h',

                        isProgressBar: true,
                        heading: {
                            textContent: item?.heading
                        },
                        excerpt: {
                            data: item?.description
                        },
                        thumbnail: {
                            src: item?.image?.url || ''
                        },
                        date: {
                            textContent: item?.startTime
                        },
                        cta: {
                            href: item?.button?.externalLink || '',
                            textContent: item?.button?.text,
                            isChevron2Arrow: true,
                            variant: 'tertiary',
                            icon: item?.button?.icon,
                            isIconPrefixed:
                                item?.button?.iconPlacement === 'Prefix' ? false : true
                        }
                    }
                }
            )
        },
        cardGeneric: props?.insightsCardCollection?.items.map((item: unknown) => {
            return {
                // cta: {
                //   href: 'https://alt',
                // },
                thumbnail: {
                    src: item?.image?.url || '',

                    height: '100%',
                    width: '100%'
                },
                heading: {
                    as: 'h5',
                    textContent: item?.subHeading || ''
                },
                date: {
                    textContent: item?.startTime || ''
                }
            }
        }),
        heading: {
            textContent: props?.subHeading || ''
        }
    }
    return heroProps
}

export function getHeroPersonProps(props: unknown): HeroPersonI {
    const heroProps: HeroPersonI = {
        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription || '' },
            buttons: {
                links: props?.buttonsCollection?.items || ''
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        height: props?.height,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        avatar: getImageProps(props?.bgImage),
        bgColor: props?.htmlAttr?.className,
        textColor: props?.htmlAttr?.colour,
        htmlAttr: props?.htmlAttr
    }
    return heroProps
}

export function getHeroDropdownProps(props: any): SimpleHeroDropdownI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }

    // Include the default option from the 0 index
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text
        },
        route: defaultOption?.internalLink?.slug
    }

    const heroProps: SimpleHeroDropdownI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList.heading
        },
        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription || '' },
            buttons: {
                links: props?.buttonsCollection?.items || ''
            },
            showButtons: true
        },
        bgImage: {
            ...getImageProps(props?.bgImage),
            objectFit: 'cover',
            width: '333px',
            height: '280px'
        },
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        htmlAttr: props?.htmlAttr
    }
    return heroProps
}

/**
 * show any one field based on this priority order: breadcrumbs > subheading > dropdown
 */
export function showAnyOneField(updatedProps: SimpleHeroI) {

    if (updatedProps.showBreadcrumb) {
        // debugger
        updatedProps.contextualInformation = {
            ...updatedProps.contextualInformation,
            subHeading: undefined
        }

        updatedProps.showDropdown = false
    } else if (updatedProps.showDropdown) {
        updatedProps.contextualInformation = {
            ...updatedProps.contextualInformation,
            subHeading: undefined
        }

        updatedProps.showBreadcrumb = false
    }
}

export function getHeroHomeProps(props: unknown): HeroHomeI {
    let options = []
    if (props?.dropdownList?.linksCollection?.items) {
        options = props.dropdownList.linksCollection.items
            .slice(1)
            .map((option: any) => ({
                image: option?.image?.url,
                label: {
                    textContent: option?.text
                },
                route: option?.internalLink?.slug
            }))
    }
    const defaultOption = props?.dropdownList?.linksCollection?.items?.[0]
    const defaultOptionObj = {
        image: defaultOption?.image?.url,
        label: {
            textContent: defaultOption?.text,
            colour: 'cs2'
        },
        route: defaultOption?.internalLink?.slug
    }
    const heroProps: HeroHomeI = {
        ...props ?? {},
        options: options,
        defaultOption: defaultOptionObj,
        isLightMode: props?.isLightMode,
        height: props?.height,
        heading: {
            textContent: props?.dropdownList?.heading
        },
        icon1: {
            icon: 'DownChevron',
            iconColour: 'cs2'
        },
        icon2: {
            icon: 'UpChevron',
            iconColour: 'cp1'
        },
        showDropdown: props?.showDropdown,
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            subHeading: {
                textContent: props?.subHeading || ''
            },
            excerpt: { data: props?.heroDescription },
            buttons: {
                links: props?.buttonsCollection?.items
            },
            showButtons: true
        },
        isLightMode: props?.isLightMode,
        breadcrumbs: { ...props?.breadCrumb, fullUrl: props?.fullUrl, locale: props?.locale },
        showBreadcrumb: props?.showBreadcrumbs,
        isBgImageEffect: props?.isBgImageEffect,
        bgImage: {
            ...getImageProps(props?.bgImage),
            objectFit: 'cover'
        },
        htmlAttr: props?.htmlAttr,
        template: props?.template
    }
    return heroProps
}

export function getHeroHomePageProps(props: unknown): HeroSingleHomeTemplateI {

    const heroHomePageProps: HeroSingleHomeTemplateI = {

        contextualInformation: {
            heading: {
                textContent: props?.heading || ''
            },
            excerpt: props?.description || '',
            showButtons: false
        },
        button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        htmlAttr: props?.htmlAttr,
        height: props?.height,
        template: props?.template
    }
    return heroHomePageProps
}


export function getHeroSoftwareProps(props: unknown): HeroSoftwareI {
    let logos, imageTabs, form
    let imageTabsObj = props?.imageTabs ? props?.imageTabs[lowerCaseFirstLetter(props?.imageTabs?.__typename)] : undefined
    imageTabs = imageTabsObj && Object.keys(imageTabsObj).length !== 0 && mapDataToComponent(imageTabsObj)

    let logosObj = props?.logoShowcase ? props?.logoShowcase[lowerCaseFirstLetter(props?.logoShowcase?.__typename)] : undefined
    logos = logosObj && Object.keys(logosObj).length !== 0 && mapDataToComponent(logosObj)


    let formObj = props?.form ? props?.form[lowerCaseFirstLetter(props?.form?.__typename)] : undefined
    form = formObj && Object.keys(formObj).length !== 0 ? mapDataToComponent(formObj) : undefined

    const HeroSoftwareProps: HeroSoftwareI = {
        contextualInformation: {
            heading: {
                textContent: props?.heading
            },
            excerpt: { data: props?.heroDescription },
            buttons: props?.buttonsCollection?.items?.length !== 0 ? {
                links: props?.buttonsCollection?.items
            } : undefined,
            showButtons: false
        },
        button: props?.buttonsCollection?.items?.length !== 0 ? props?.buttonsCollection?.items[0] : undefined,
        bgColor: props?.htmlAttr?.className,
        isLightMode: props?.isLightMode,
        htmlAttr: props?.htmlAttr,
        height: props?.height,
        logos: logos,
        form: form,
        imageTabs: imageTabs,


    }
    return HeroSoftwareProps
}
export function getHeroArgusProps(props: unknown): HeroArgusI {
    let afsObj = props?.afs ? props?.afs[lowerCaseFirstLetter(props?.afs?.__typename)] : undefined
    const afs = afsObj && Object.keys(afsObj).length !== 0 && mapDataToComponent(afsObj)

    let videoObj = props?.videoPlayer ? props?.videoPlayer[lowerCaseFirstLetter(props?.videoPlayer?.__typename)] : undefined

    const heroArgusProps: HeroArgusI = {
        RichText: { data: props?.heroDescription },
        button: props?.button ? getPrimaryLinkProps(props?.button) : undefined,
        image: props?.bgImage ? getImageProps(props?.bgImage) : undefined,
        htmlAttr: props?.htmlAttr,
        height: props?.height,
        afs: afs,
        videoId: videoObj?.contentId
    }
    return heroArgusProps
}