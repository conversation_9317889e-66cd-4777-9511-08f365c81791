// src/FormRenderer.scss

$form-border-color: #d9d9d9; // Border color for inputs
$form-focus-color: #40a9ff; // Focus color for inputs
$form-background-color: #ffffff; // Background color for inputs
$form-border-radius: 4px; // Border radius for inputs

.form-group {
  margin-bottom: 1.5rem; // Space between form groups
}

.form-label {
  display: block; // Block display for labels
  margin-bottom: 0.5rem; // Space below the label
  font-weight: 500; // Medium font weight for labels
  color: #333; // Darker text color
}

input.form-control,
select.form-control,
textarea.form-control {
  width: 100%; // Full width for inputs
  padding: 0.8rem 1rem; // Padding for inputs
  border: 1px solid $form-border-color; // Border color
  border-radius: $form-border-radius; // Border radius
  background-color: $form-background-color; // Background color
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out; // Transition effects

  // Placeholder styling
  &::placeholder {
    color: #aaa; // Placeholder color
  }

  &:focus {
    border-color: $form-focus-color; // Focus border color
    outline: none; // Remove default outline
    box-shadow: 0 0 5px rgba($form-focus-color, 0.5); // Shadow effect on focus
  }

  &.error {
    border-color: red; // Error border color
  }
}

input[type='range'] {
  -webkit-appearance: none; // Remove default appearance
  appearance: none; // Remove default appearance
  width: 100%; // Full width
  height: 8px; // Height of the slider
  border-radius: $form-border-radius; // Border radius
  background: #ddd; // Background color

  &::-webkit-slider-thumb {
    -webkit-appearance: none; // Remove default appearance
    appearance: none; // Remove default appearance
    width: 20px; // Width of the thumb
    height: 20px; // Height of the thumb
    border-radius: 50%; // Make it circular
    background: $form-focus-color; // Color of the thumb
    cursor: pointer; // Cursor pointer on hover
  }

  &::-moz-range-thumb {
    width: 20px; // Width of the thumb for Firefox
    height: 20px; // Height of the thumb for Firefox
    border-radius: 50%; // Make it circular
    background: $form-focus-color; // Color of the thumb
    cursor: pointer; // Cursor pointer on hover
  }
}

button {
  padding: 0.6rem 1.2rem; // Button padding
  border-radius: $form-border-radius; // Button border radius
  border: none; // Remove border
  background-color: $form-focus-color; // Button background color
  color: white; // Button text color
  font-weight: bold; // Bold text for buttons
  transition: background-color 0.2s, transform 0.2s; // Transition effects

  &:hover {
    background-color: darken($form-focus-color, 10%); // Darker color on hover
    transform: translateY(-2px); // Lift effect on hover
  }

  &:active {
    transform: translateY(0); // Reset lift effect on active
  }

  &.primary {
    background-color: $form-focus-color; // Primary button color
  }

  &.secondary {
    background-color: #6c757d; // Secondary button color
  }

  &.link {
    background-color: transparent; // Transparent for link button
    color: $form-focus-color; // Link color
    text-decoration: underline; // Underline for link
  }
}

.error {
  color: red; // Error text color
  font-size: 0.875rem; // Font size for error messages
  margin-top: 0.25rem; // Margin above error messages
}
