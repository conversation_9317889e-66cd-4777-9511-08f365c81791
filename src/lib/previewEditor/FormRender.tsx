import React, { FC, useState } from 'react'
import Tiptap from '../../components/Preview/Tiptap'
import './formrender.scss'
interface FormField {
  id: string
  type: 'input' | 'select' | 'textarea' | 'number' | 'range' | 'autocomplete' | 'richtext' // Supported types
  inputType?: string // For input fields (text, email, url, etc.)
  label: string
  placeholder?: string
  required?: boolean
  options?: string[] // For select fields
  width?: number // For custom width
  pattern?: string // Regex pattern for validation
  errorMessage?: string // Custom error message
  min?: number // Min value for number and range types
  max?: number // Max value for number and range types
  autocompleteOptions?: string[] // Options for autocomplete fields
}

interface FormRendererProps {
  fields: FormField[]
  data?: Record<string, any>
  onSubmit: (formData: Record<string, any>) => void
}

export const FormRenderer: FC<FormRendererProps> = ({
  fields,
  data = {},
  onSubmit,
}) => {
  const [formData, setFormData] = useState<Record<string, any>>(data)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (id: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }))

    // Clear error when the user starts typing
    if (errors[id]) {
      setErrors((prev) => ({ ...prev, [id]: '' }))
    }
  }

  const validateField = (field: FormField) => {
    const { id, required, pattern, errorMessage } = field
    const value = formData[id]

    if (required && !value) {
      return 'This field is required.'
    }

    if (pattern && value && !new RegExp(pattern).test(value)) {
      return errorMessage || 'Invalid format.'
    }

    return ''
  }

  const renderField = (field: FormField) => {
    const {
      id,
      type,
      inputType,
      label,
      placeholder,
      required,
      options,
      width,
      min,
      max,
      autocompleteOptions,
    } = field

    const style = {
      width: width ? `${width}%` : '100%',
    }

    switch (type) {
      case 'input':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <input
              type={inputType}
              id={id}
              placeholder={placeholder}
              required={required}
              value={formData[id] || ''}
              onChange={(e) => handleInputChange(id, e.target.value)}
            />
          </div>
        )
      case 'select':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <select
              id={id}
              required={required}
              value={formData[id] || ''}
              onChange={(e) => handleInputChange(id, e.target.value)}
            >
              <option value=''>Select...</option>
              {options?.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        )
      case 'textarea':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <textarea
              id={id}
              placeholder={placeholder}
              required={required}
              value={formData[id] || ''}
              onChange={(e) => handleInputChange(id, e.target.value)}
            />
          </div>
        )
      case 'number':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <input
              type='number'
              id={id}
              required={required}
              min={min}
              max={max}
              value={formData[id] || ''}
              onChange={(e) => handleInputChange(id, e.target.value)}
            />
          </div>
        )
      case 'range':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <input
              type='range'
              id={id}
              min={min}
              max={max}
              value={formData[id] || min}
              onChange={(e) => handleInputChange(id, e.target.value)}
            />
          </div>
        )
      case 'autocomplete':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <input
              type='text'
              id={id}
              placeholder={placeholder}
              required={required}
              value={formData[id] || ''}
              onChange={(e) => handleInputChange(id, e.target.value)}
              list={`${id}-options`}
            />
            <datalist id={`${id}-options`}>
              {autocompleteOptions?.map((option) => (
                <option key={option} value={option} />
              ))}
            </datalist>
          </div>
        )

      case 'richtext':
        return (
          <div key={id} className='form-group' style={style}>
            <label htmlFor={id}>{label}</label>
            <div>
              <Tiptap
                onUpdate={({ editor }) => {
                  console.log('editor.getJSON()', editor.getJSON());

                  handleInputChange(id, editor.getJSON())
                }}
                // extensions={extensions}
                content={formData[id] || {}}
              />
            </div>
          </div>
        )
      default:
        return null
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('formData', formData);
    const newErrors: Record<string, string> = {}

    fields.forEach((field) => {
      const error = validateField(field)
      if (error) {
        newErrors[field.id] = error
      }
    })

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors) // Set the errors to state
      return // Prevent form submission
    }

    onSubmit(formData) // Call onSubmit if there are no errors
  }

  return (
    <form onSubmit={handleSubmit}>
      {fields.map(renderField)}
      <button type='submit'>Submit</button>
    </form>
  )
}

// // FormRenderer.tsx
// import React, { FC, useState } from 'react'
// import './formrender.scss'
// interface FormRendererProps {
//   config: any
//   data?: any
//   onSubmit: (formData: any) => void
// }

// export const FormRenderer: FC<FormRendererProps> = ({
//   config,
//   data = {},
//   onSubmit,
// }) => {
//   const [formData, setFormData] = useState(data)

//   const handleInputChange = (name: string, value: any) => {
//     setFormData({
//       ...formData,
//       [name]: value,
//     })
//   }

//   const handleNestedChange = (name: string, nestedData: any) => {
//     setFormData({
//       ...formData,
//       [name]: nestedData,
//     })
//   }

//   const renderField = (field: any) => {
//     switch (field.type) {
//       case 'text':
//         return (
//           <div key={field.name} className='form-group'>
//             <label>{field.label}</label>
//             <input
//               type='text'
//               name={field.name}
//               value={formData[field.name] || ''}
//               onChange={(e) => handleInputChange(field.name, e.target.value)}
//             />
//           </div>
//         )
//       case 'select':
//         return (
//           <div key={field.name} className='form-group'>
//             <label>{field.label}</label>
//             <select
//               name={field.name}
//               value={formData[field.name] || ''}
//               onChange={(e) => handleInputChange(field.name, e.target.value)}
//             >
//               {field.options.map((option: any) => (
//                 <option key={option.value} value={option.value}>
//                   {option.label}
//                 </option>
//               ))}
//             </select>
//           </div>
//         )
//       case 'checkbox':
//         return (
//           <div key={field.name} className='form-group'>
//             <label>
//               <input
//                 type='checkbox'
//                 name={field.name}
//                 checked={!!formData[field.name]}
//                 onChange={(e) =>
//                   handleInputChange(field.name, e.target.checked)
//                 }
//               />
//               {field.label}
//             </label>
//           </div>
//         )
//       case 'nested':
//         return (
//           <div key={field.name} className='form-group'>
//             <label>{field.label}</label>
//             <FormRenderer
//               config={field.fields}
//               data={formData[field.name] || {}}
//               onSubmit={(nestedData) =>
//                 handleNestedChange(field.name, nestedData)
//               }
//             />
//           </div>
//         )
//       default:
//         return null
//     }
//   }

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault()
//     onSubmit(formData)
//   }

//   return (
//     <form onSubmit={handleSubmit}>
//       {config.map((field: any) => renderField(field))}
//       <button type='submit'>Submit</button>
//     </form>
//   )
// }

// FormRenderer.tsx
