import ComponentError from '../../components/Errors/E404C'
import BreadCrumb from '../../components/Navs/Breadcrumbs'
import {
  getFullBreadCrumbProps,
  getShortBreadCrumbProps,
} from '../propsMapping/breadCrumb.mapping'
import { transformFields } from '../propsMapping/index.mapping'

function BreadCrumbRouter(props: any) {
  let breadCrumbProps = null

  if (props?.isSymLink) {
    // fetch the data from the referenced entry
    breadCrumbProps = transformFields(props)
  }
  // if (props?.isExperimentation) {
  //     cl(props?.isExperimentation, props, 'Identifier : CTA :Click')
  // }
  switch (props.template) {
    case 'Short':
      breadCrumbProps = getShortBreadCrumbProps(props)
      return <BreadCrumb {...breadCrumbProps} />
    case 'Full':
      breadCrumbProps = getFullBreadCrumbProps(props)
      return <BreadCrumb {...breadCrumbProps} />
    default:
      return <ComponentError message={`Error: Breadcrumb Template not found`} />
  }
}

export default BreadCrumbRouter
