import GenericTemplate from '../../templates/GenericTemplate'
import HomeTemplate from '../../templates/HomeTemplate'
import InsightsTemplate from '../../templates/InsightsTemplate'
import PressReleaseTemplate from '../../templates/PressReleaseTemplate'

function PageRouter(props: unknown) {
  const template = props?.pageData?.template

  let updatedProps = {
    ...props,
    pageData: {
      ...props?.pageData,
      publishDate: props?.pageData?.publishDate?.split('T')[0] || null,
    },
  }

  switch (template) {
    case 'Generic':
      return <GenericTemplate {...updatedProps} />
    case 'Insight Article':
      return <InsightsTemplate {...updatedProps} />
    case 'Press Release':
      return <PressReleaseTemplate {...updatedProps} />
    case 'Home':
      return <HomeTemplate {...updatedProps} />
    default:
      return <>Not Found</>
    // return notFound()
  }
}

export default PageRouter
