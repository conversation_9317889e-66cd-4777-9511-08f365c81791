import TabBox from '../../components/Containers/TabBox'
import TabBoxHero from '../../components/Containers/TabBoxHero'
import TabBoxHome from '../../components/Containers/TabBoxHome'
import ComponentError from '../../components/Errors/E404C'
import { getTabBoxHeroProps } from '../propsMapping/tabBox.mapping'

function TabBoxRouter(props: any) {
  switch (props.template ?? 'TabBox') {
    case 'TabBox':
      return <TabBox {...props} />

    case 'TabBox - Home':
      return <TabBoxHome {...props} />

    case 'TabBox - Hero':
      const updatedprops = getTabBoxHeroProps(props)
      return <TabBoxHero {...updatedprops} />
    default:
      return <ComponentError message={`Error: Menu List Template not found`} />
  }
}

export default TabBoxRouter
