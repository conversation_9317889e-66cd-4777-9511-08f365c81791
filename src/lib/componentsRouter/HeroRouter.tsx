import ComponentError from '../../components/Errors/E404C'
import HeroCustomerStories from '../../components/Heros/HeroCustomerStories'
import SimpleHeroDropdown from '../../components/Heros/HeroDropdown/SimpleHeroDropdown'
import HeroInsightsArticle from '../../components/Heros/HeroInsightsArticle'
import <PERSON><PERSON><PERSON> from '../../components/Heros/HeroPerson'
import HeroArgus from '../../components/Heros/HeroSingles/HeroArgus'
import HeroHome from '../../components/Heros/HeroSingles/HeroHome'
import HeroSingleBgColour from '../../components/Heros/HeroSingles/HeroSingleBgColour'
import HeroSingleDisplay from '../../components/Heros/HeroSingles/HeroSingleDisplay'
import HeroSingleDynamic from '../../components/Heros/HeroSingles/HeroSingleDynamic'
import HeroSingleGeneric from '../../components/Heros/HeroSingles/HeroSingleGeneric'
import HeroSingleHomeTemplate from '../../components/Heros/HeroSingles/HeroSingleHomeTemplate'
import HeroSingleLower from '../../components/Heros/HeroSingles/HeroSingleLower'
import HeroSoftware from '../../components/Heros/HeroSingles/HeroSoftware'
import HeroTwoColumn from '../../components/Heros/HeroTwoColumns/HeroTwoColumn'
import HeroTwoColumnLower from '../../components/Heros/HeroTwoColumns/HeroTwoColumnLower'
import {
    getHeroArgusProps,
    getHeroCustomerStoriesProps,
    getHeroDropdownProps,
    getHeroHomePageProps,
    getHeroHomeProps,
    getHeroInsightArticleProps,
    getHeroPersonProps,
    getHeroSingleBGImageIProps,
    getHeroSingleDisplayProps,
    getHeroSingleDynamicProps,
    getHeroSingleGenericProps,
    getHeroSingleLowerProps,
    getHeroSoftwareProps,
    getHeroTwoColumnLowerProps,
    getHeroTwoColumnProps
} from '../propsMapping/hero.mapping'
import { transformFields } from '../propsMapping/index.mapping'

function HeroRouter(props: any) {
    let heroProps = null
    if (props.isSymLink) {
        // fetch the data from the referenced entry
        heroProps = transformFields(props)
    }

    // if (props.isHeroOverlay === null) {
    //     props.isHeroOverlay = true
    // }

    switch (props.template) {
        case 'SingleDisplay':
            heroProps = getHeroSingleDisplayProps(props)
            return <HeroSingleDisplay {...heroProps} />
        case 'SingleGeneric':
            heroProps = getHeroSingleGenericProps(props)
            return <HeroSingleGeneric {...heroProps} />
        case 'SingleBGColour':
            heroProps = getHeroSingleBGImageIProps(props)
            return <HeroSingleBgColour {...heroProps} />
        case 'SingleLower':
            heroProps = getHeroSingleLowerProps(props)
            return <HeroSingleLower {...heroProps} />
        case 'HeroHome':
            heroProps = getHeroHomeProps(props)
            return <HeroHome {...heroProps} />
        case 'HeroHomePage':
            heroProps = getHeroHomePageProps(props)
            return <HeroSingleHomeTemplate {...heroProps} />
        case 'SingleDynamic':
            heroProps = getHeroSingleDynamicProps(props)
            return <HeroSingleDynamic {...heroProps} />
        case 'Twocolumn - Primary':
            heroProps = getHeroTwoColumnProps({
                ...props,
                heroTwoColumnType: 'Primary'
            })
            return <HeroTwoColumn {...heroProps} />
        case 'Twocolumn - Secondary':
            heroProps = getHeroTwoColumnProps({
                ...props,
                isImageLeft: true,
                heroTwoColumnType: 'Secondary'
            })
            return <HeroTwoColumn {...heroProps} />
        case 'Twocolumn - Tertiary':
            heroProps = getHeroTwoColumnLowerProps(props)
            return <HeroTwoColumnLower {...heroProps} />
        case 'CustomerStories':
            heroProps = getHeroCustomerStoriesProps(props)
            return <HeroCustomerStories {...heroProps} />
        case 'InsightArticle':
            heroProps = getHeroInsightArticleProps(props)
            return <HeroInsightsArticle {...heroProps} />
        case 'Person':
            heroProps = getHeroPersonProps(props)
            return <HeroPerson {...heroProps} />
        case 'Software - Form':
            heroProps = getHeroSoftwareProps(props)
            return <HeroSoftware {...heroProps} />
        case 'Software - Button Group':
            heroProps = getHeroSoftwareProps(props)
            return <HeroSoftware {...heroProps} />
        case 'Dropdown':
            heroProps = getHeroDropdownProps(props)
            return <SimpleHeroDropdown {...heroProps} />
        case 'HeroArgus':
            heroProps = getHeroArgusProps(props)
            return <HeroArgus {...heroProps} />
        // case 'InsightsGeneric':
        //     heroProps = getHeroInsightsProps(props)
        //     return <HeroInsights {...heroProps} />
        default:
            return <ComponentError message={`Error: Hero Template not found`} />
    }
}

export default HeroRouter