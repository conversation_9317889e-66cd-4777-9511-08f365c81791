import AreaChart from '../../components/DataVisualizers/Area<PERSON><PERSON>'
import <PERSON><PERSON><PERSON> from '../../components/DataVisualizers/BarChart'
import CandlestickChart from '../../components/DataVisualizers/CandlestickChart'
import Combo<PERSON>hart from '../../components/DataVisualizers/Combo<PERSON><PERSON>'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../components/DataVisualizers/Doug<PERSON>ut<PERSON><PERSON>'
import Line<PERSON>hart from '../../components/DataVisualizers/LineChart'
import <PERSON><PERSON><PERSON> from '../../components/DataVisualizers/Pie<PERSON><PERSON>'
import TreemapChart from '../../components/DataVisualizers/Treemap<PERSON>hart'
import WaterfallChart from '../../components/DataVisualizers/WaterfallChart'
import ComponentError from '../../components/Errors/E404C'
import {
  getAreaChartProps,
  getBarChartProps,
  getCandlestickChartProps,
  getComboChartProps,
  getDoughnutChartProps,
  getLineChartProps,
  getPieChartProps,
  getTreemapChartProps,
  getWaterfallChartProps,
} from '../propsMapping/dataVisualization.mapping'

async function DataVisualizationRouter(props: any) {
  let dataVisualizationProps = null

  switch (props.template) {
    case 'Line chart':
      dataVisualizationProps = await getLineChartProps(props)
      return <LineChart {...dataVisualizationProps} />
    case 'Bar chart':
      dataVisualizationProps = await getBarChartProps(props)
      return <BarChart {...dataVisualizationProps} />
    case 'Area chart':
      dataVisualizationProps = await getAreaChartProps(props)
      return <AreaChart {...dataVisualizationProps} />
    case 'Pie chart':
      dataVisualizationProps = await getPieChartProps(props)
      return <PieChart {...dataVisualizationProps} />
    case 'Combo chart':
      dataVisualizationProps = await getComboChartProps(props)
      return <ComboChart {...dataVisualizationProps} />
    case 'Doughnut chart':
      dataVisualizationProps = await getDoughnutChartProps(props)
      return <DoughnutChart {...dataVisualizationProps} />
    case 'Waterfall chart':
      dataVisualizationProps = await getWaterfallChartProps(props)
      return <WaterfallChart {...dataVisualizationProps} />
    case 'Candlestick chart':
      dataVisualizationProps = await getCandlestickChartProps(props)
      return <CandlestickChart {...dataVisualizationProps} />
    case 'Treemap chart':
      dataVisualizationProps = await getTreemapChartProps(props)
      return <TreemapChart {...dataVisualizationProps} />
    default:
      return <ComponentError message={`Error: Featured Template not found`} />
  }
}

export default DataVisualizationRouter
