import CTARowFeatured from '../../components/CTARows/CTARowFeatured'
import CTARowHome from '../../components/CTARows/CTARowHome'
import CTARowMultiple from '../../components/CTARows/CTARowMultiple'
import CTARowSingleBackgroundImage from '../../components/CTARows/CTARowSingle/CTARowSingleBackgroundImage'
import CTARowSingleColorFill from '../../components/CTARows/CTARowSingle/CTARowSingleColorFill'
import ComponentError from '../../components/Errors/E404C'
import InsightHome from '../../components/Insights/InsightHome'
import InsightPrimaryCTA from '../../components/Insights/InsightPrimaryCTA'
import InsightSecondaryCTA from '../../components/Insights/InsightSecondaryCTA'
import InsightSingleCol from '../../components/Insights/InsightSingleCol'
import {
  getCTARowFeaturedProps,
  getCTARowHomeProps,
  getInsightHomeProps,
  getInsightPrimaryProps,
  getInsightSecondaryProps,
  getMultipleProps,
  getSingleBackgroundImageProps,
  getSingleBackroundColorFillProps,
} from '../propsMapping/ctaRow.mapping'
import { transformFields } from '../propsMapping/index.mapping'

function CtaRowRouter(props: any) {
  let ctaRowProps = null
  if (props.isSymLink) {
    // fetch the data from the referenced entry
    ctaRowProps = transformFields(props)
  }

  switch (props.template) {
    case 'Single - Color Fill':
      ctaRowProps = getSingleBackroundColorFillProps(props)
      return <CTARowSingleColorFill {...ctaRowProps} />

    case 'Single - Background Image':
      ctaRowProps = getSingleBackgroundImageProps(props)
      return <CTARowSingleBackgroundImage {...ctaRowProps} />

    case 'Multiple':
      ctaRowProps = getMultipleProps(props)
      return <CTARowMultiple {...ctaRowProps} />

    case 'Insight - Secondary':
      ctaRowProps = getInsightSecondaryProps(props)
      return <InsightSecondaryCTA {...ctaRowProps} />

    case 'Insight - Primary':
      ctaRowProps = getInsightPrimaryProps(props)
      return <InsightPrimaryCTA {...ctaRowProps} />
    case 'Insight - Single Col':
      ctaRowProps = getInsightPrimaryProps(props)
      return <InsightSingleCol {...ctaRowProps} />
    case 'CTA Row - Home':
      ctaRowProps = getCTARowHomeProps(props)
      return <CTARowHome {...ctaRowProps} />
    case 'Insight - Home':
      ctaRowProps = getInsightHomeProps(props)
      return <InsightHome {...ctaRowProps} />
    case 'CTARow - Featured':
      ctaRowProps = getCTARowFeaturedProps(props)
      return <CTARowFeatured {...ctaRowProps} />
    default:
      return <ComponentError message={`Error: CTA Row Template not found`} />
  }
}

export default CtaRowRouter
