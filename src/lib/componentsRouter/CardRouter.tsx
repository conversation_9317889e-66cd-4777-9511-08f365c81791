import ArticleItem from '../../components/ArticleLists/ArticleItem'
import CardComparison from '../../components/Cards/CardComparisons/CardComparison'
import CardComparisonIcon from '../../components/Cards/CardComparisons/CardComparisonIcon'
import LiveEvent from '../../components/Cards/CardEvents/LiveEvent'
import OnDemand from '../../components/Cards/CardEvents/OnDemand'
import ColumnCardFeaturedLandscape from '../../components/Cards/CardFeatured/CardFeaturedLandscape'
import CardFeaturedPortrait from '../../components/Cards/CardFeatured/CardFeaturedPortrait'
import CardFeaturedPortraitDetailed from '../../components/Cards/CardFeatured/CardFeturedPortraitDetailed'
import CardFeaturedRowsList from '../../components/Cards/CardFeaturedRowsList'
import CardGenericFeaturedList from '../../components/Cards/CardGeneric/CardGenericFeaturedList'
import CardGenericHeadline from '../../components/Cards/CardGeneric/CardGenericHeadline'
import CardGenericLandscape from '../../components/Cards/CardGeneric/CardGenericLandscape'
import CardGenericMegamenu from '../../components/Cards/CardGeneric/CardGenericMegamenu'
import CardGenericNoImage from '../../components/Cards/CardGeneric/CardGenericNoImage'
import CardGenericPortrait from '../../components/Cards/CardGeneric/CardGenericPortrait'
import CardGenericSearch from '../../components/Cards/CardGeneric/CardGenericSearch'
import CardGenericSearchFeatured from '../../components/Cards/CardGeneric/CardGenericSearchFeatured'
import CardGenericSmall from '../../components/Cards/CardGeneric/CardGenericSmall'
import CardLandscapeHome from '../../components/Cards/CardGeneric/CardLandscapeHome'
import CardIcon from '../../components/Cards/CardIconLogo/CardIcon'
import CardImageHome from '../../components/Cards/CardIconLogo/CardImageHome'
import CardLogo from '../../components/Cards/CardIconLogo/CardLogo'
import CardLogoPortrait from '../../components/Cards/CardIconLogo/CardLogoPortrait'
import CardMegamenu from '../../components/Cards/CardMegamenu'
import CardPeopleLandscape from '../../components/Cards/CardPeople/CardPeopleLandscape'
import CardPeopleLandscapeRow from '../../components/Cards/CardPeople/CardPeopleLandscapeRow'
import CardPeoplePortrait from '../../components/Cards/CardPeople/CardPeoplePortrait'
import CardPeopleSmall from '../../components/Cards/CardPeople/CardPeopleSmall'
import ComponentError from '../../components/Errors/E404C'
import { l } from '../../globals/utils'
import {
  getArticleListItemIProps,
  getCardFeaturedRowsListProps,
  getCardGenericFeaturedListProps,
  getCardGenericHeadlineProps,
  getCardGenericMegamenuProps,
  getCardGenericNoImageProps,
  getCardGenericSearchFeaturedProps,
  getCardGenericSearchProps,
  getCardIconProps,
  getCardImageHomeProps,
  getCardLogoProps,
  getCardMegaMenuProps,
  getComparisonCardIconProps,
  getComparisonCardProps,
  getEventCardProps,
  getFeaturedProps,
  getGenericCardProps,
  getGenericCardSmallProps,
  getLandscapeHomeProps,
  getOnDemandWebinarProps,
  getPeopleLandscapeRowProps,
  getPeopleProps,
} from '../propsMapping/card.mapping'
import { transformFields } from '../propsMapping/index.mapping'

function CardRouter(props: any) {
  let cardProps = null

  if (props.isSymLink) {
    // fetch the data from the referenced entry
    cardProps = transformFields(props)
  }

  switch (props.template) {
    case 'CardGenericLarge':
      cardProps = getGenericCardProps(props)
      return <CardGenericLandscape {...cardProps} locale={props.locale} />
    case 'CardLandscapeHome':
      cardProps = getLandscapeHomeProps(props)
      return <CardLandscapeHome {...cardProps} locale={props.locale} />
    case 'CardGenericMedium':
      cardProps = getGenericCardProps(props)
      return <CardGenericPortrait {...cardProps} locale={props.locale} />
    case 'CardGenericSmall':
      cardProps = getGenericCardSmallProps(props)
      return <CardGenericSmall {...cardProps} locale={props.locale} />
    case 'CardGenericNoImage':
      cardProps = getCardGenericNoImageProps(props)
      return <CardGenericNoImage {...cardProps} locale={props.locale} />
    case 'CardGenericHeadline':
      cardProps = getCardGenericHeadlineProps(props)
      return <CardGenericHeadline {...cardProps} locale={props.locale} />
    case 'CardGenericFeaturedList':
      cardProps = getCardGenericFeaturedListProps(props)
      return <CardGenericFeaturedList {...cardProps} locale={props.locale} />
    case 'CardGenericMegamenu':
      cardProps = getCardGenericMegamenuProps(props)
      return <CardGenericMegamenu {...cardProps} locale={props.locale} />
    case 'CardGenericSearch':
      cardProps = getCardGenericSearchProps(props)
      return <CardGenericSearch {...cardProps} locale={props.locale} />
    case 'CardGenericSearchFeatured':
      cardProps = getCardGenericSearchFeaturedProps(props)
      return <CardGenericSearchFeatured {...cardProps} locale={props.locale} />
    case 'CardFeaturedPortrait':
      cardProps = getFeaturedProps({ ...props, orientation: 'portrait' })
      return <CardFeaturedPortrait {...cardProps} locale={props.locale} />
    case 'CardFeaturedPortraitDetailed':
      cardProps = getFeaturedProps({
        ...props,
        orientation: 'portrait-detailed',
      })
      return (
        <CardFeaturedPortraitDetailed {...cardProps} locale={props.locale} />
      )
    case 'CardFeaturedLandscape':
      cardProps = getFeaturedProps({ ...props, orientation: 'landscape' })
      return (
        <ColumnCardFeaturedLandscape {...cardProps} locale={props.locale} />
      )
    case 'CardComparison':
      cardProps = getComparisonCardProps(props)
      return <CardComparison {...cardProps} locale={props.locale} />
    case 'CardComparisonIcon':
      cardProps = getComparisonCardIconProps(props)
      return <CardComparisonIcon {...cardProps} locale={props.locale} />
    case 'CardLiveEventWebinar':
      cardProps = getEventCardProps(props)
      return <LiveEvent {...cardProps} locale={props.locale} />
    case 'CardOnDemandWebinar':
      cardProps = getOnDemandWebinarProps(props)
      l(cardProps)
      return <OnDemand {...cardProps} locale={props.locale} />
    case 'CardIcon':
      cardProps = getCardIconProps(props)
      return <CardIcon {...cardProps} locale={props.locale} />
    case 'CardImage':
      cardProps = getCardLogoProps(props)
      return <CardLogo {...cardProps} locale={props.locale} />
    case 'CardImageHome':
      cardProps = getCardImageHomeProps(props)
      return <CardImageHome {...cardProps} />
    case 'CardLogoPortrait':
      cardProps = getCardLogoProps({ ...props, orientation: true })
      return <CardLogoPortrait {...cardProps} locale={props.locale} />
    case 'CardPeoplePortrait':
      cardProps = getPeopleProps({ ...props, orientation: 'portrait' })
      return <CardPeoplePortrait {...cardProps} locale={props.locale} />
    case 'CardPeopleLandscape':
      cardProps = getPeopleProps({ ...props, orientation: 'landscape' })
      return <CardPeopleLandscape {...cardProps} locale={props.locale} />
    case 'CardPeopleSmall':
      cardProps = getPeopleProps(props)
      return <CardPeopleSmall {...cardProps} locale={props.locale} />
    case 'CardPeopleLandscapeRow':
      cardProps = getPeopleLandscapeRowProps({
        ...props,
        orientation: 'landscape',
      })
      return <CardPeopleLandscapeRow {...cardProps} locale={props.locale} />
    case 'CardMegaMenu':
      cardProps = getCardMegaMenuProps(props)
      return <CardMegamenu {...cardProps} locale={props.locale} />
    case 'CardFeaturedRowsList':
      cardProps = getCardFeaturedRowsListProps(props)
      return <CardFeaturedRowsList {...cardProps} locale={props.locale} />
    case 'ArticleListItem':
      cardProps = getArticleListItemIProps(props)
      return <ArticleItem {...cardProps} locale={props.locale} />
    default:
      return <ComponentError message={`Error: Card Template not found`} />
  }
}

export default CardRouter
