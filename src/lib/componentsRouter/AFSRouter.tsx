import ComponentError from '../../components/Errors/E404C'
import AfsMap from '../../systems/AFS/AFSMap'
import {
  collectRichtextIds,
  getAfsCsvData,
  getCitiesWithCoordinatesAndBounds,
} from '../../systems/AFS/AFSMap/afsMap.mapping'
import AFSCustomerEvents from '../../systems/AFS/AFSPages/CustomerStories'
import EventsAndWebinars from '../../systems/AFS/AFSPages/EventsAndWebinars'
import AFSInsights from '../../systems/AFS/AFSPages/Insights'
import OurExperts from '../../systems/AFS/AFSPages/OurExperts'
import PressRelease from '../../systems/AFS/AFSPages/PressRelease'
import AFSProjectWorks from '../../systems/AFS/AFSPages/ProjectWork'
import {
  afsCardCollectionQuery,
  pageCollectionQuery,
} from '../../systems/AFS/AFSQuery/afs.query'
import { fetchGraphQL } from '../api'
import { findProcessAndPlaceObjects } from '../propsMapping/index.mapping'
import { richtextQueryMultipleId } from '../queries/richtext.query'

async function fetchData(query, isCardData = false) {
  const response = await fetchGraphQL(query, true)

  return response?.data?.[
    isCardData ? 'cardComponentCollection' : 'pageCollection'
  ]
}

function getTagNameFromTemplate(template: string) {
  switch (template) {
    case 'Insights':
      return 'afsInsights'
    case 'Customer Stories':
      return 'customerStories'
    case 'Project Work':
      return 'projectWork'
    case 'Experts':
      return 'ourExperts'
    case 'Press Release':
      return 'afsPressRelease'
    case 'Events':
      return 'afsEvents'
    case 'Map':
      return 'afsMap'
    default:
      return 'insights'
  }
}

async function AfsRouter(props: unknown) {
  let locale = props.locale || 'en-CA'
  const tag = getTagNameFromTemplate(props.template)

  let afsPageData = []
  let cardData = []
  let csvData
  let richtextData
  let citiesAndCountries
  let total = 0
  let skip = 0

  if (['Events', 'Experts'].includes(props.template)) {
    const data = await fetchData(afsCardCollectionQuery(tag, locale), true)

    cardData = data?.items || []
    total = data?.total
    skip = data?.skip
  } else if (props.template === 'Map') {
    csvData = await getAfsCsvData(props?.dataFile?.url)
    console.log('csvData', csvData)

    if (csvData) {
      const richtextIds = collectRichtextIds(csvData)
      console.log('richtextIds', richtextIds)

      richtextData = await fetchGraphQL(
        richtextQueryMultipleId(richtextIds, locale)
      )
      await findProcessAndPlaceObjects({
        obj: richtextData,
        locale,
        visitedIds: ['afs'],
      })
      citiesAndCountries = await getCitiesWithCoordinatesAndBounds(csvData)
      console.log('citiesAndCountries', citiesAndCountries)
      console.log('richtextData', richtextData)
    }
  } else {
    afsPageData = await fetchAllDataInParallel(props, tag)
  }

  async function fetchAllDataInParallel(props: any, tag: any, chunkSize = 100) {
    let afsPageData = []

    // Fetch initial data to determine total, if not already known
    let initialData = await fetchData(pageCollectionQuery(tag, 0, locale))
    afsPageData = initialData?.items || []
    const total = initialData?.total
    let promises = []

    // Calculate how many additional fetches are needed
    const numberOfFetches = Math.ceil((total - afsPageData.length) / chunkSize)

    // Generate all promises for fetching data in parallel
    for (let i = 1; i <= numberOfFetches; i++) {
      const skip = i * chunkSize
      const promise = fetchData(pageCollectionQuery(tag, skip, locale))
      promises.push(promise)
    }

    // Wait for all promises to resolve
    const results = await Promise.all(promises)

    // Combine all fetched data into afsPageData
    for (const result of results) {
      afsPageData = [...afsPageData, ...(result?.items || [])]
    }

    return afsPageData
  }

  let TheAfs = <></>

  switch (props?.template) {
    case 'Insights':
      TheAfs = (
        <AFSInsights
          afsPageData={afsPageData}
          afsFilterData={props}
          locale={locale}
        />
      )
      break
    case 'Customer Stories':
      TheAfs = (
        <AFSCustomerEvents
          afsPageData={afsPageData}
          afsFilterData={props}
          locale={locale}
        />
      )
      break
    case 'Project Work':
      TheAfs = (
        <AFSProjectWorks
          afsPageData={afsPageData}
          afsFilterData={props}
          locale={locale}
        />
      )
      break
    case 'Experts':
      TheAfs = (
        <OurExperts
          afsPageData={[...afsPageData, ...cardData]}
          afsFilterData={props}
          locale={locale}
        />
      )
      break
    case 'Press Release':
      TheAfs = (
        <PressRelease
          afsPageData={afsPageData}
          afsFilterData={props}
          locale={locale}
        />
      )
      break
    case 'Events':
      TheAfs = (
        <EventsAndWebinars
          afsPageData={[...afsPageData, ...cardData]}
          afsFilterData={props}
          locale={locale}
        />
      )
      break
    case 'Map':
      TheAfs = (
        <AfsMap
          csvData={csvData}
          afsFilterData={props}
          citiesAndCountries={citiesAndCountries}
          locale={locale}
          richtextData={richtextData}
        />
      )
      break
    default:
      TheAfs = <ComponentError message={`Error: AFS Template not found`} />
  }

  return TheAfs
}

export default AfsRouter
