import ComponentError from '../../components/Errors/E404C'
import NavigationSecondaryHeader from '../../components/Navs/Navigation/NavigationSecondaryHeader'
import ReonomyHeader from '../../components/Navs/Navigation/ReonomyHeader'
import SimpleNavigation from '../../components/Navs/Navigation/SimpleNavigation'
import {
  getNavigationHeaderProps,
  getNavigationSecondaryHeaderProps,
  getReonomyHeaderProps,
} from '../propsMapping/navigationHeader.mapping'

function NavigationHeaderRouter(props: any) {
  let NavigationHeaderProps = null

  switch (props.template) {
    case 'PrimaryHeader':
      NavigationHeaderProps = getNavigationHeaderProps(props)
      return (
        <SimpleNavigation
          locale={props.locale}
          langLocalization={props.langLocalization}
          {...NavigationHeaderProps}
        />
      )
    case 'ReonomyHeader':
      NavigationHeaderProps = getReonomyHeaderProps(props)
      return <ReonomyHeader {...NavigationHeaderProps} />
    case 'Dropdown':
      NavigationHeaderProps = getNavigationSecondaryHeaderProps(props)
      return <NavigationSecondaryHeader {...NavigationHeaderProps} />
    case 'Sticky':
      NavigationHeaderProps = getNavigationSecondaryHeaderProps(props)
      return <NavigationSecondaryHeader {...NavigationHeaderProps} />
    case 'Non-Sticky':
      NavigationHeaderProps = getNavigationSecondaryHeaderProps(props)
      return <NavigationSecondaryHeader {...NavigationHeaderProps} />
    default:
      return <ComponentError message={`Error: Navigation Template not found`} />
  }
  return

  /* switch (props.template) {
      case 'Primary Header':
           NavigationHeaderProps= getNavigationHeaderProps(props)
        return <SimpleNavigation {...NavigationHeaderProps} />
      default:
        return (
          <code style={{ color: 'red' }}>
            Error: Menu List Template not found
          </code>
        )
    }  */
}

export default NavigationHeaderRouter
