import LayoutContainer from '../../components/Containers/LayoutContainer'
import SimpleContainer from '../../components/Containers/SimpleContainer'
import { getLayoutContainerProps } from '../propsMapping/layoutContainer.mapping'

function LayoutContainerRouter(props: any) {
  // Get properties for RowContainer
  let layoutProps = getLayoutContainerProps(props)
  if (props?.isFullXBleed) {
    return (
      <LayoutContainer
        isFullXBleed
        htmlAttr={{ ...props?.htmlAttr, style: { position: 'relative' } }}
      >
        <SimpleContainer {...layoutProps} />
      </LayoutContainer>
    )
  }
  // Render RowContainer with the generated properties
  // return <RowContainer {...mapProps} />;
  return <SimpleContainer {...layoutProps} />
}

export default LayoutContainerRouter
