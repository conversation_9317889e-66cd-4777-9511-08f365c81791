import dynamic from 'next/dynamic'
import ComponentError from '../../components/Errors/E404C'
// import Qualtrics from '../../components/Players/Qualtrics'
import ApplePodCastPlayer from '../../components/Players/ApplePodCastPlayer'
import Calconic from '../../components/Players/Calconic'
import MicroSoftMyBooking from '../../components/Players/MircosoftMyBookings'
import Transistor from '../../components/Players/Transistor'
import WistiaPlayer from '../../components/Players/Wistia'
import YoutubeVideo from '../../components/Players/YoutubeVideo'
import EstateMasterProduct from '../../components/Widgets/EstateMasterProduct'
import {
  getCalconicPlayerProps,
  getPlayerProps,
  getTransistorPlayerProps,
} from '../propsMapping/player.mapping'

const TrinityPlayer = dynamic(
  () => import('../../components/Players/Trinity'),
  {
    ssr: false,
  }
)

const Qualtrics = dynamic(() => import('../../components/Players/Qualtrics'), {
  ssr: false,
})

function PlayerRouter(props: any) {
  let PlayerProps = null

  switch (props.template) {
    case 'YouTube':
      PlayerProps = getPlayerProps(props)
      return <YoutubeVideo {...PlayerProps} />

    case 'Transistor':
      PlayerProps = getTransistorPlayerProps(props)
      return <Transistor {...PlayerProps} />

    case 'Calconic - Development feasibility':
      PlayerProps = getCalconicPlayerProps(props)
      return (
        <Calconic
          {...PlayerProps}
          calc='developmentFeasibility'
          id={props.contentId}
        />
      )
    case 'Calconic - Property Tax':
      PlayerProps = getCalconicPlayerProps(props)
      return (
        <Calconic {...PlayerProps} calc='propertyTax' id={props.contentId} />
      )

    case 'Cohost':
      // https://player.cohostpodcasting.com/
      return (
        <iframe
          src={`https://player.cohostpodcasting.com/${props.contentId}`}
          title={props.heading ?? 'Cohost podcast player'}
          style={{
            border: '1px solid #e5e7eb',
            borderRadius: '0.5rem',
            height: '100%',
            width: '100%',
          }}
        />
      )

    case 'Iframe':
      return (
        <iframe
          src={props.contentId}
          title={props.heading}
          style={{
            width: props.maxWidth ?? '100%',
            height: props.height ?? '100%',
          }}
        />
      )

    case 'Wistia':
      return <WistiaPlayer {...getPlayerProps(props)} />

    case 'Trinity':
      return <TrinityPlayer contentId={props?.contentId} />

    case 'Qualtrics':
      return <Qualtrics contentId={props?.contentId} />

    case 'EstateMaster':
      return <EstateMasterProduct locale={props.locale} isAllProduct={false} />

    case 'ARGUS EstateMaster - All products':
      return <EstateMasterProduct locale={props.locale} isAllProduct={true} />

    case 'Microsoft My Bookings':
      return <MicroSoftMyBooking contentId={props?.contentId} />

    case 'Apple Podcast Player':
      return <ApplePodCastPlayer {...props} />

    default:
      return <ComponentError message={`Error: Player Template not found`} />
  }

  /*let PlayerProps = getPlayerProps(props)
            return <YoutubeVideo {...PlayerProps} />
          */
  /* switch (props.template) {
              case 'Primary Header':s
                   NavigationHeaderProps= getNavigationHeaderProps(props)
                return <SimpleNavigation {...NavigationHeaderProps} />
              default:
                return (
                  <code style={{ color: 'red' }}>
                    Error: Menu List Template not found
                  </code>
                )
            }  */
}

export default PlayerRouter