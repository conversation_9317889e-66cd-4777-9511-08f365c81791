'use client'
import FlexContainer from '../../components/Containers/FlexContainer'
import ComponentError from '../../components/Errors/E404C'
import { changeFormValueMain, setEmailValidationMsg } from '../../redux/slices/formSlice'
import { useAppDispatch, useAppSelector } from '../../redux/store'
import { countries } from '../../systems/AltusForms/Form/country'
import styles from '../../systems/AltusForms/Form/index.module.scss'
import FormInputType from '../../systems/AltusForms/Form/inputType'
import { generateEmailValidatioMsg } from '../../systems/AltusForms/Form/utils'
import { getPreviewProps } from '../../utils/previewUtils'

function FormFieldRouter(props: any) {
  // const altusDataFormatToType = {
  //   Number: 'number',
  //   Text: 'text',
  //   Email: 'email',
  // }
  // const altusDataFormatToPattern = {
  //   Number: '[0-9]',
  //   Text: null,
  //   Email: null,
  // }

  const dispatch = useAppDispatch()

  const emailErrorMsg = useAppSelector((state) => state.form.emailValidationMsg)
  const values = useAppSelector((state) => state.form.values)

  switch (props.altusFieldType) {
    case 'Text':
      // formFieldProps = getImageMasonryProps(props)
      return (
        <div
          {...getPreviewProps(props)}
          style={{
            width: '100%',
            marginBottom: '30px',
          }}
        >
          <label htmlFor={props?.altusFieldName} className='form-label'>
            {props?.altusLabel}
          </label>
          <input
            type={props.altusDataFormat === 'Phone' ? 'number' : 'text'}
            name={props?.altusFieldName}
            className='form-control'
            id={props?.altusFieldName}
            required={props?.altusIsRequired}
            placeholder={props?.placeholderText}
            onChange={(event) =>
              props?.onChange(event.target.value, props?.altusFieldName)
            }
          />
          {props?.helperText && (
            <small id={props?.altusFieldName} className='form-text'>
              {props?.helperText}
            </small>
          )}
          {props?.validationErrorMessage && (
            <div className='invalid-feedback'>
              {props?.validationErrorMessage ??
                props.altusLabel + ' is a required field'}
            </div>
          )}
        </div>
      )

    case 'Password':
      // formFieldProps = getImageMasonryProps(props)
      return (
        <div
          style={{
            width: '100%',
            marginBottom: '30px',
          }}
        >
          <label htmlFor={props?.altusFieldName} className='form-label'>
            {props?.altusLabel}
          </label>
          <input
            type='password'
            minLength={8}
            name={props?.altusFieldName}
            className='form-control'
            id={props?.altusFieldName}
            required={props?.altusIsRequired}
            placeholder={props?.placeholderText}
            onChange={(event) =>
              props?.onChange(event.target.value, props?.altusFieldName)
            }
          />
          {props?.helperText && (
            <small id={props?.altusFieldName} className='form-text'>
              {props?.helperText}
            </small>
          )}
          {props?.validationErrorMessage && (
            <div className='invalid-feedback'>
              {props?.validationErrorMessage ??
                props.altusLabel + ' is a required field'}
            </div>
          )}
        </div>
      )

    case 'Date':
      // formFieldProps = getImageMasonryProps(props)
      return (
        <div
          style={{
            width: '100%',
            marginBottom: '30px',
          }}
        >
          <label htmlFor={props?.altusFieldName} className='form-label'>
            {props?.altusLabel}
          </label>
          <input
            type='date'
            name={props?.altusFieldName}
            className='form-control'
            id={props?.altusFieldName}
            required={props?.altusIsRequired}
            placeholder={props?.placeholderText}
            onChange={(event) =>
              props?.onChange(event.target.value, props?.altusFieldName)
            }
          />
          {props?.helperText && (
            <small id={props?.altusFieldName} className='form-text'>
              {props?.helperText}
            </small>
          )}
          {props?.validationErrorMessage && (
            <div className='invalid-feedback'>
              {props?.validationErrorMessage ??
                props.altusLabel + ' is a required field'}
            </div>
          )}
        </div>
      )
    case 'Hidden':
      return (
        <input
          type='hidden'
          name={props?.altusFieldName}
          value={props?.altusFieldValues?.at(0) ?? ''}
        />
      )

    case 'Email':
      const sessionEmailValue = getSessionStorageItem('Email')

      return (
        <div
          style={{
            width: '100%',
            marginBottom: '30px',
          }}
        >
          <label htmlFor={props?.altusFieldName} className='form-label'>
            {props?.altusLabel}
          </label>
          <input
            type='email'
            name={props?.altusFieldName}
            className={`form-control ${emailErrorMsg && 'invalid'}`}
            id={props?.altusFieldName}
            required={props?.altusIsRequired}
            defaultValue={sessionEmailValue ?? ''}
            // pattern={generateEmailValidatioRegex(notIncludeEmails)}
            placeholder={props?.placeholderText}
            onBlur={(event) => {
              props?.onChange(
                event.target.value,
                props?.altusFieldName,
                'email'
              )
              dispatch(
                setEmailValidationMsg(
                  generateEmailValidatioMsg(
                    event.target.value,
                    props?.validationErrorMessage,
                    props?.isAltusEmailAllowed,
                    props?.isGenericEmailAllowed
                  )
                )
              )
            }}
            onChange={(event) => {
              props?.onChange(
                event.target.value,
                props?.altusFieldName,
                'email'
              )
              dispatch(setEmailValidationMsg(''))
            }}
          />
          {!emailErrorMsg && props?.helperText && (
            <small id={props?.altusFieldName} className='form-text'>
              {props?.helperText}
            </small>
          )}
          {emailErrorMsg && (
            <div className={`invalid-feedback d-block`}>{emailErrorMsg}</div>
          )}
          {!emailErrorMsg && props?.validationErrorMessage && (
            <div className='invalid-feedback'>
              {props?.validationErrorMessage ??
                props.altusLabel + ' is a required field'}
            </div>
          )}
          {/* <Input
                    helperText={{ textContent: props?.helperText ?? '' }}
                    onChange=(event) => props?.onChange(event, props?.altusFieldName)
                    htmlAttr={{
                        ...props.htmlAttr,
                        name: props?.altusFieldName,
                        type: 'email',
                        require: props?.altusIsRequired,
                        pattern: generateEmailValidatioRegex(notIncludeEmails),
                        title: props?.validationErrorMessage
                    }}
                    // htmlAttr={{ style: { width: '100%', padding: '0px', marginBottom: '1rem' } }}
                    isIcon={false}
                    inputText={{textContent: props.altusLabel}}
                /> */}
        </div>
      )
    case 'Text Area':
      return (
        <div
          style={{
            width: '100%',
            marginBottom: '30px',
          }}
        >
          <label htmlFor={props?.altusFieldName} className='form-label'>
            {props?.altusLabel}
          </label>
          <textarea
            name={props?.altusFieldName}
            className='form-control'
            id={props?.altusFieldName}
            required={props?.altusIsRequired}
            placeholder={props?.placeholderText}
            onChange={(event) =>
              props?.onChange(event.target.value, props?.altusFieldName)
            }
          ></textarea>
          {props?.helperText && (
            <small id={props?.altusFieldName} className='form-text'>
              {props?.helperText}
            </small>
          )}
          {props?.validationErrorMessage && (
            <div className='invalid-feedback'>
              {props?.validationErrorMessage ??
                props.altusLabel + ' is a required field'}
            </div>
          )}
        </div>
      )
    case 'Radio':
      return (
        <div style={{ marginBottom: '20px' }}>
          <label htmlFor={props?.altusFieldName} className='form-label'>
            {props?.altusLabel}
          </label>
          <div className={styles.radioDiv}>
            {props?.altusFieldValues?.map((item: string) => {
              return (
                <div key={item}>
                  <input
                    type='radio'
                    id={item}
                    name='radioGroup'
                    required={props?.altusIsRequired}
                    onChange={(event) =>
                      props?.onChange(event.target.value, props?.altusFieldName)
                    }
                  />
                  <label htmlFor={item}>{item}</label>
                </div>
              )
            })}
          </div>
        </div>
      )
    case 'Dropdown':
      // const dependantNames = getDependantFieldNames(props)

      return (
        <>
          <div
            style={{
              marginBottom: '30px',
            }}
            className={'dropdownRoot'}
          >
            <label htmlFor={props?.altusFieldName} className='form-label'>
              {props?.altusLabel}
            </label>
            <div className='select'>
              <select
                onChange={(event) => {
                  // const updatedValues = deleteDependantFields(values, dependantNames)
                  // dispatch(assignFormValues(updatedValues))
                  props?.onChange(event.target.value, props?.altusFieldName)
                }}
                placeholder={props.placeholderText}
                className='form-control dropdownDiv'
                name={props?.altusFieldName}
                id={props?.altusFieldName}
                required={props?.altusIsRequired}
              >
                <option value={''}>{props?.placeholderText}</option>
                {props?.altusFieldValues?.map((item: string) => (
                  <option key={item} value={item}>
                    {item}
                  </option>
                ))}
              </select>
            </div>
            {props?.helperText && (
              <small id={props?.altusFieldName} className='form-text'>
                {props?.helperText}
              </small>
            )}
            {props?.validationErrorMessage && (
              <div className='invalid-feedback'>
                {props?.validationErrorMessage ??
                  props.altusLabel + ' is a required field'}
              </div>
            )}
          </div>
          {props.altusDependentsCollection &&
            props.altusDependentsCollection.items.map(
              (inputItem: any, index: number) => {
                if (
                  values?.hasOwnProperty(props?.altusFieldName) &&
                  inputItem?.dependentValue === values[props?.altusFieldName] &&
                  values[props?.altusFieldName] !== ''
                )
                  return inputItem?.dependentFieldsCollection?.items.map(
                    (dependentItem: any) => (
                      <FormInputType
                        key={index}
                        values={values}
                        input={dependentItem}
                        mainHandleChange={props?.onChange}
                      />
                    )
                  )
              }
            )}
        </>
      )
    case 'CheckBox':
      return (
        <div style={{ marginBottom: '30px' }}>
          <FlexContainer
            key={props.altusFieldName}
            htmlAttr={{ className: styles.checkboxDiv }}
            width='100%'
            minWidth='190px'
            gap='8px'
            alignItems='center'
          >
            <input
              type='checkbox'
              id={props.altusFieldName}
              name={props.altusFieldName}
              required={props?.altusIsRequired}
              onChange={(event) => {
                props?.onChange(
                  event.target.checked
                    ? (props?.altusFieldValues?.at(0) ?? 'Yes')
                    : 'No',
                  props.altusFieldName
                )
              }}
            />
            <label
              style={{ padding: '10px 0', width: '100%' }}
              htmlFor={props.altusFieldName}
            >
              {props.altusLabel}
            </label>
          </FlexContainer>
        </div>
      )
    case 'Country':
      return (
        <>
          <div style={{ marginBottom: '30px' }} className={'dropdownRoot'}>
            <label htmlFor={props?.altusFieldName} className='form-label'>
              {props?.altusLabel}
            </label>
            <div className='select'>
              <select
                onChange={(event) => {
                  dispatch(changeFormValueMain({ state: undefined }))
                  props?.onChange(event.target.value, props?.altusFieldName)
                }}
                required={props?.altusIsRequired}
                placeholder={props.placeholderText}
                className='form-control dropdownDiv'
                name={props?.altusFieldName}
                id={props?.altusFieldName}
              >
                <option value={''}>{props?.placeholderText}</option>
                {countries?.map((item: { country: string }) => (
                  <option value={item.country}>{item.country}</option>
                ))}
              </select>
            </div>
            {props?.helperText && (
              <small id={props?.altusFieldName} className='form-text'>
                {props?.helperText}
              </small>
            )}
            {props?.validationErrorMessage && (
              <div className='invalid-feedback'>
                {props?.validationErrorMessage ??
                  props.altusLabel + ' is a required field'}
              </div>
            )}
          </div>
          {props.altusDependentsCollection &&
            props.altusDependentsCollection.items.map(
              (inputItem: any, index: number) => {
                if (
                  values?.hasOwnProperty(props?.altusFieldName) &&
                  inputItem?.dependentValue === values[props?.altusFieldName] &&
                  values[props?.altusFieldName] !== ''
                )
                  return inputItem?.dependentFieldsCollection?.items.map(
                    (dependentItem: any) => (
                      <FormInputType
                        key={index}
                        values={values}
                        input={dependentItem}
                        mainHandleChange={props?.onChange}
                      />
                    )
                  )
              }
            )}
        </>
      )
    default:
      return (
        <ComponentError message={`Error: Input field Template not found`} />
      )
  }
}

export default FormFieldRouter
