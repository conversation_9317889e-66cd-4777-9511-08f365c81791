import CarouselTabBox from '../../components/Carousels/CarouseTabBox'
import CarouselGeneric from '../../components/Carousels/CarouselGeneric'
import CarouselGeneric2 from '../../components/Carousels/CarouselGeneric2'
import CarouselHeroHorizontal from '../../components/Carousels/CarouselHero/CarouselHeroHorizontal'
import CarouselHeroVertical from '../../components/Carousels/CarouselHero/CarouselHeroVertical'
import CarouselHome from '../../components/Carousels/CarouselHome'
import CarouselImageVideo from '../../components/Carousels/CarouselImageVideo'
import CarouselTabs from '../../components/Carousels/CarouselTabs'
import CarouselTwoColumnGeneric from '../../components/Carousels/CarouselTwoColumn/CarouselTwoColumnGeneric'
import CarouselTwoColumnLogo from '../../components/Carousels/CarouselTwoColumn/CarouselTwoColumnLogo'
import ComponentError from '../../components/Errors/E404C'
import {
  getCarouselGeneric2Props,
  getCarouselGenericProps,
  getCarouselHeroHorizontalProps,
  getCarouselHeroVerticalProps,
  getCarouselHomeProps,
  getCarouselImageVideoProps,
  getCarouselTabBoxProps,
  getCarouselTabsProps,
  getCarouselTwoColGenericProps,
  getCarouselTwoColLogoProps,
} from '../propsMapping/carousel.mapping'
import { transformFields } from '../propsMapping/index.mapping'

function CarouselRouter(props: any) {
  let carouselProps = null

  if (props.isSymLink) {
    // fetch the data from the referenced entry
    carouselProps = transformFields(props)
  }

  switch (props.template) {
    case 'CarouselTwoColLogo':
      carouselProps = getCarouselTwoColLogoProps(props)
      return <CarouselTwoColumnLogo {...carouselProps} />

    case 'CarouselTwoColGeneric':
      carouselProps = getCarouselTwoColGenericProps(props)
      return <CarouselTwoColumnGeneric {...carouselProps} />

    case 'CarouselHeroVertical':
      carouselProps = getCarouselHeroVerticalProps(props)
      return <CarouselHeroVertical {...carouselProps} />

    case 'CarouselHeroHorizontal':
      carouselProps = getCarouselHeroHorizontalProps(props)
      return <CarouselHeroHorizontal {...carouselProps} />

    case 'CarouselGeneric':
      carouselProps = getCarouselGenericProps(props)
      return <CarouselGeneric {...carouselProps} />
    case 'CarouselGeneric2':
      carouselProps = getCarouselGeneric2Props(props)
      return <CarouselGeneric2 {...carouselProps} />
    case 'CarouselHome':
      carouselProps = getCarouselHomeProps(props)
      return <CarouselHome {...carouselProps} />
    case 'CarouselTabBox':
      carouselProps = getCarouselTabBoxProps(props)
      return <CarouselTabBox {...carouselProps} />
    case 'CarouselImageVideo':
      carouselProps = getCarouselImageVideoProps(props)
      return <CarouselImageVideo {...carouselProps} />
    case 'CarouselTabs':
      carouselProps = getCarouselTabsProps(props)
      return <CarouselTabs {...carouselProps} />
    default:
      return <ComponentError message={`Error: Carousel Template not found`} />
  }
}

export default CarouselRouter
