{"components": [{"id": "HeroComponent", "type": "form", "label": "Hero Section", "fields": [{"id": "heading", "type": "input", "inputType": "text", "label": "Heading", "placeholder": "Enter heading", "required": true, "validation": {"pattern": "^.+$", "errorMessage": "Heading is required."}, "helperText": "Provide the main heading for the investor relations section."}, {"id": "subHeading", "type": "input", "inputType": "text", "label": "Subheading", "placeholder": "Enter subheading", "required": false, "validation": null, "helperText": "Optional: Provide a subheading."}, {"id": "description", "type": "textarea", "label": "Description", "placeholder": "Enter description", "required": false, "validation": null, "helperText": "Provide a description for the investor relations section."}, {"id": "logoShowcase", "type": "input", "inputType": "text", "label": "Logo Showcase URL", "placeholder": "Enter logo URL", "required": false, "validation": {"pattern": "^(http|https)://.*$", "errorMessage": "Please enter a valid URL."}, "helperText": "Optional: Enter the URL for the logo showcase."}, {"id": "imageTabs", "type": "input", "inputType": "text", "label": "Image Tabs", "placeholder": "Enter image tabs", "required": false, "validation": null, "helperText": "Optional: Enter image tab details."}, {"id": "isHeroOverlay", "type": "checkbox", "label": "<PERSON> Overlay", "required": false}, {"id": "isLightMode", "type": "checkbox", "label": "Is Light Mode", "required": false}, {"id": "showDropdown", "type": "checkbox", "label": "Show Dropdown", "required": false}, {"id": "showBreadcrumbs", "type": "checkbox", "label": "Show Breadcrumbs", "required": false}, {"id": "isBgImageEffect", "type": "checkbox", "label": "Is Background Image Effect", "required": false}, {"id": "submitButton", "type": "button", "label": "Submit", "action": "submit"}]}, {"id": "button", "type": "form", "label": "<PERSON><PERSON>", "fields": [{"id": "buttonText", "type": "input", "inputType": "text", "label": "Button Text", "placeholder": "Enter button text", "required": true, "width": 12}, {"id": "buttonType", "type": "select", "label": "Button Type", "options": ["Primary", "Secondary", "Link"], "required": true, "width": 12}, {"id": "buttonLink", "type": "input", "inputType": "url", "label": "Button Link URL", "placeholder": "Enter button URL", "required": true, "width": 24}, {"id": "internalName", "type": "input", "inputType": "text", "label": "Internal Name", "placeholder": "Enter internal name", "required": true, "width": 12}]}, {"id": "callout", "type": "form", "label": "Callout Section", "fields": [{"id": "calloutTitle", "type": "input", "inputType": "text", "label": "Callout Title", "placeholder": "Enter callout title", "required": true, "width": 24}, {"id": "calloutDescription", "type": "textarea", "label": "Callout Description", "placeholder": "Enter callout description", "required": true, "width": 24}, {"id": "callout<PERSON><PERSON><PERSON>", "type": "select", "label": "Callout <PERSON>", "options": ["Primary", "Secondary", "Link"], "required": true, "width": 12}, {"id": "calloutButtonText", "type": "input", "inputType": "text", "label": "Callout <PERSON><PERSON>", "placeholder": "Enter callout button text", "required": true, "width": 12}, {"id": "calloutButtonLink", "type": "input", "inputType": "url", "label": "Callout Button Link URL", "placeholder": "Enter callout button link URL", "required": true, "width": 24}]}, {"id": "FormField", "type": "FormField", "label": "FormField Form", "fields": [{"id": "altus<PERSON><PERSON><PERSON>", "type": "input", "inputType": "text", "label": "Altus Label", "placeholder": "<PERSON><PERSON>", "required": true, "validation": {"pattern": "^.+$", "errorMessage": "Altus Label is required."}, "helperText": "Provide the Altus Label for the Form Creation."}, {"id": "altusFieldName", "type": "input", "inputType": "text", "label": "Field Name", "placeholder": "Enter field name", "required": true, "validation": {"pattern": "^[a-zA-Z0-9_]+$", "errorMessage": "Field Name must be alphanumeric."}, "helperText": "Provide the unique field name for identification."}, {"id": "altusFieldType", "type": "select", "label": "Field Type", "options": ["Text", "Email", "Number", "Select", "Checkbox"], "required": true, "helperText": "Select the type of field."}, {"id": "altusIsRequired", "type": "checkbox", "label": "Is Required?", "required": false, "helperText": "Check if this field is mandatory."}, {"id": "validationErrorMessage", "type": "input", "inputType": "text", "label": "Validation Error Message", "placeholder": "Enter error message", "required": false, "helperText": "Message displayed when validation fails."}, {"id": "placeholderText", "type": "input", "inputType": "text", "label": "Placeholder Text", "placeholder": "Enter placeholder text", "required": false, "helperText": "Text displayed in the input field before entry."}, {"id": "helperText", "type": "input", "inputType": "text", "label": "Helper Text", "placeholder": "Enter helper text", "required": false, "helperText": "Provide additional guidance for users."}]}, {"id": "LinkComponent", "type": "LinkComponent", "label": "LinkComponent Form", "fields": [{"id": "internalName", "type": "input", "inputType": "text", "label": "Internal Name", "placeholder": "Enter internal name", "required": true, "validation": {"pattern": "^.+$", "errorMessage": "Internal Name is required."}, "helperText": "Provide a unique internal name for the form."}, {"id": "template", "type": "select", "label": "Template", "options": ["Primary", "Secondary", "Tertiary"], "required": true, "helperText": "Select the template for the form."}, {"id": "text", "type": "input", "inputType": "text", "label": "Button Text", "placeholder": "Enter button text", "required": true, "validation": {"pattern": "^.+$", "errorMessage": "Button Text is required."}, "helperText": "Text displayed on the button."}, {"id": "externalLink", "type": "input", "inputType": "url", "label": "External Link", "placeholder": "Enter external URL", "required": false, "helperText": "Optional link to an external resource."}, {"id": "icon", "type": "input", "inputType": "text", "label": "Icon", "placeholder": "Enter icon name (e.g., RightChevron)", "required": true, "validation": {"pattern": "^.+$", "errorMessage": "Icon is required."}, "helperText": "Specify the icon to be displayed."}, {"id": "iconPlacement", "type": "select", "label": "Icon Placement", "options": ["Suffix", "Prefix"], "required": true, "helperText": "Select where to place the icon."}, {"id": "isLightMode", "type": "checkbox", "label": "Is Light Mode?", "required": false, "helperText": "Check if the button should use light mode styling."}, {"id": "isChevron2Arrow", "type": "checkbox", "label": "Is Chevron 2 Arrow?", "required": false, "helperText": "Check if chevron should be styled as an arrow."}, {"id": "openInNewTab", "type": "checkbox", "label": "Open in New Tab?", "required": false, "helperText": "Check if the link should open in a new tab."}, {"id": "internalLink", "type": "input", "inputType": "url", "label": "Internal Link", "placeholder": "Enter internal URL", "required": false, "helperText": "Optional link to an internal resource."}, {"id": "image", "type": "input", "inputType": "url", "label": "Image URL", "placeholder": "Enter image URL", "required": false, "helperText": "Optional image to display with the form."}]}, {"id": "form", "type": "form", "label": "Contact Form", "fields": [{"id": "firstName", "type": "input", "inputType": "text", "label": "First Name", "placeholder": "Enter your first name", "required": true, "width": 12}, {"id": "lastName", "type": "input", "inputType": "text", "label": "Last Name", "placeholder": "Enter your last name", "required": true, "width": 12}, {"id": "email", "type": "input", "inputType": "email", "label": "Email", "placeholder": "Enter your email", "required": true, "width": 24}, {"id": "message", "type": "textarea", "label": "Message", "placeholder": "Enter your message", "required": true, "width": 24}]}]}