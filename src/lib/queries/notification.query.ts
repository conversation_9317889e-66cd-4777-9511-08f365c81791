import { linkFragment } from './link.query'

export const notificationQuery = (id: string, locale: string) => `
{
  componentNotification(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${NotificationFragment}
  }
}`

export const NotificationFragment = `
    ... on ComponentNotification {
    sys {
        id
     }
    __typename
    template
    heading
    description
    backgroundColor
    isGlobal
    autoHide
    isEnabled
    categories
      excludedPagesFromCategoryCollection{
        items{
          slug
        }
      }
    specificPageCollection{
      items{
        slug
      }
    }
    categoriesPageCollection{
      items{
        ... on  Page{
          slug
        }
      }
    }
    cta{
      ...on LinkComponent{
        ${linkFragment}
        }
    }
    formFloating{
      __typename
      sys{
        id
      }
    }
    carouselDataCollection(limit: 10) {
      items{
        sys{
          id
        }
          heading
          template
          description
          backgroundColor
          cta{
              text
              externalLink
          }
          isGlobal
          isEnabled
          specificPageCollection{
            items{
              slug
            }
          }
          categoriesPageCollection{
            items{
              ... on  Page{
                slug
              }
            }
          }
          isPersistent
      }
  }
  toastMessage
  isPersistent
  }
`
