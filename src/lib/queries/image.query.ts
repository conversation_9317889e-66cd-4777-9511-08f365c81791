export const imageQuery = (id: string, locale: string) => `
{
  componentImage(preview:${process.env.NEXT_PUBLIC_PREVIEW},id: "${id}",locale: "${locale}") {
    ${imageFragment}
  }
}
`

export const imageFragment = `
    ... on ComponentImage {
      sys {
        id
      }
      isZoomAble
      __typename
      internalName
      htmlAttr
      ratio
      verticalAlignment
      horizontalAlignment
      imageFile {
        url
        height
        width
      }
      title
      description
      altText
      maxWidth
      maxHeight
      objectFit
    }
    `
