export const quoteQuery = (id: string, locale: string) => `
{
  componentQuote(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${quoteFragment}
  }
}
`

export const quoteFragment = `
    ... on ComponentQuote{
        sys{
            id
          }
          __typename
          htmlAttr
          internalName
          description
          showAvatar
          showAuthor
          quotePeople{
            jobTitle
            image{
              url
              title
            }
            fullName
          }
        }
    `
