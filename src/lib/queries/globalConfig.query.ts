export const getConfigurationsCollectionQuery = () => {
  return `{
      configurationsCollection(preview:true){
      items{
        sys{
          id
        }
        internalName
        type
      }
    }
      }`
}

export const getConfigEnginItem = (id: string) => {
  return `
   {
     configurations(id: "${id}", preview: true) {
       internalName
       type
       data {
         json
       }
     }
   }
 `
}
