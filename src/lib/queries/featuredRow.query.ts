import { linkFragment } from './link.query'

export const featuredRowQuery = (id: string, locale: string) => `
{
  componentFeaturedRows(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${featuredRowFragment}
  }
}
`

export const featuredRowFragment = `
    ... on ComponentFeaturedRows{
        sys{
            id
          }
          __typename
          heading
          htmlAttr
          template
          isLightMode
          button {
          ...on LinkComponent{
          ${linkFragment}
          }
        }
    featuredListCollection(limit:10){
      items{
        ... on CardComponent{
          __typename    
            sys{
              id
            }
        }
      }
    }
  }


      
    `
