import { linkFragment } from './link.query'

export const carouselQuery = (id: string, locale: string) => `
{
  carouselComponent(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${carouselFragment}
  }
}
`

export const carouselFragment = `
  ... on CarouselComponent {
    sys {
      id
    }
    __typename
      template
      isLightMode
      htmlAttr
      heading
      subheading
      description
      isFullBleed
      timer
      tabHeadings
      button{
        ...on LinkComponent{
          ${linkFragment}
          }
      }
      carouselItemsCollection(limit: 10) {
        __typename
        items {
          ... on HeroComponent{
            __typename    
              sys{
                id
              }
          }
          ... on Player{
            __typename    
            sys{
              id
            }
          }
          ... on CardComponent{
            __typename    
              sys{
                id
              }
          }
          ... on ComponentImage {
            __typename  
            sys {
              id
            }
          }
          ...on ComponentRichtext {
            __typename  
            sys {
              id
            }
          }
           ...on ComponentLogo {
            __typename  
            sys {
              id
            }
          }
        }
      }
  }
`
