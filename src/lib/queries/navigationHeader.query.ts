import { linkFragment } from './link.query'
import { LogoFragment } from './logo.query'

export const navigationPrimaryHeaderQuery = (locale: string) => {
  return `
{
  componentNavigationHeaderCollection(
    preview:${process.env.NEXT_PUBLIC_PREVIEW}
    limit: 1
    locale: "${locale}"
    where: {
      contentfulMetadata: {
        tags: { id_contains_all:"${process.env.NEXT_PUBLIC_DOMAIN}" }
      }
      template_in:["PrimaryHeader","ReonomyHeader"]
    }
    ) {
    items {
      ${NavigationHeaderFragment}
    }
  }
}`
}

export const navigationHeaderQuery = (id: string, locale: string) => `
{
  componentNavigationHeader(
    id:"${id}"
    preview:${process.env.NEXT_PUBLIC_PREVIEW}
    locale: "${locale}"
    ) {
      ${NavigationHeaderFragment}
    }
}`

export const NavigationHeaderFragment = `
    ... on ComponentNavigationHeader {
    sys {
        id
     }
     __typename
     languages
     template
     isLoginHidden
     isLanguageHidden
     isSearchHidden
    isLightMode
      buttonGroupCollection(limit: 2){
      items{
        ...on LinkComponent{
       ${linkFragment}
        }
      }
    }
    lightLogo{
      ${LogoFragment}
    }
    darkLogo{
      ${LogoFragment}
    }
    ctaButton{
      ${linkFragment}
    }
    navigationMenuItemsCollection(limit:10){
      items{
        template
        firstCardHeading
        secondCardHeading
        menuLink{
          ${linkFragment}
        }
        menuListsCollection(limit:10){
          items{
            template
            heading
            linksCollection(limit:10){
              items{
                ${linkFragment}
              }
            }
          }
        }
        featuredCardsCollection(limit:2){
          items{
            template
            heading
            subHeading
            startTime
            image{
              url
              title
            }
            button{
              ...on LinkComponent{
                ${linkFragment}
                }
            }
            }
          }
        
        cardListsCollection(limit:4){
          items{
            template
            heading
            startTime
            description
          }
        }
        ctaLink{
          ...on LinkComponent{
            ${linkFragment}
            }
        }
      }
      
    }
    
  }
 `
