import { linkFragment } from './link.query'

export const tabBoxQuery = (id: string, locale: string) => `
{
  componentTabBox(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${tabBoxFragment}
  }
}
`

export const tabBoxFragment = `
    ... on ComponentTabBox{
      sys {
        id
      }
      __typename
      htmlAttr
      template
      internalName
      subHeading
      heading
      description
      button{
        ...on LinkComponent{
          ${linkFragment}
          }
      }
      tabItemsCollection {
        items {
          sys {
            id
          }
          tabHeading
          tabComponentCollection {
            items {
              ... on ComponentAccordion {
                __typename
                sys {
                  id
                }
              }
              ... on ComponentCallout {
                __typename
                sys {
                  id
                }
              }
  
              ... on CardComponent {
                __typename
                sys {
                  id
                }
              }
  
              ... on CarouselComponent {
                __typename
                sys {
                  id
                }
              }

              ... on ComponentCountdown {
                __typename
                sys {
                  id
                }
              }
  
              ... on ComponentCtaRow {
                __typename
                sys {
                  id
                }
              }
              ... on ComponentFeaturedRows {
                __typename
                sys {
                  id
                }
              }
  
              ... on ComponentGallery {
                __typename
                sys {
                  id
                }
              }

              ... on ComponentImage {
                __typename
                sys {
                  id
                }
              }
  
              ... on HeroComponent {
                __typename
                sys {
                  id
                }
              }
  
              ... on ComponentNavigationFooter {
                __typename
                sys {
                  id
                }
              }

              ... on ComponentNavigationHeader {
                __typename
                sys {
                  id
                }
              }

              ... on ComponentLayoutContainer{
                __typename
                sys{
                    id
                }
              }  
  
              ... on ComponentOffices {
                __typename
                sys {
                  id
                }
              }
  
              ... on ComponentQuote {
                __typename
                sys {
                  id
                }
              }

              ...on ComponentLayoutRow {
                __typename
                sys {
                  id
                }
              }

              ...on ComponentDynamicTagging {
                __typename
                sys {
                  id
                }
              }
              
            }
          }
        }
      }
    }
    `
