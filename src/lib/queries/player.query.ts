export const playerQuery = (id: string, locale: string) => `
{
  player(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${playerFragment}
  }
}
`

export const playerFragment = `
    ... on Player{
        contentId
        template
        internalName
        __typename
        heading
        description
        maxWidth
        height
        videoSettings
        isLightMode
    }
    `
