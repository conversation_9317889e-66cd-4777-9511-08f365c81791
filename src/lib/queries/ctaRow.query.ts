import { linkFragment } from './link.query'

export const ctaRowQuery = (id: string, locale: string) => `
{
  componentCtaRow(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${ctaRowFragment}
  }
}`

export const ctaRowFragment = `
    ... on ComponentCtaRow{
        sys{
            id
        }
        htmlAttr
        __typename
        internalName
        template
        subheading
        heading
        description
        isBlur
        link{
          ...on LinkComponent{
            ${linkFragment}
            }
        }
        buttonGroupCollection{ 
          items {
            ${linkFragment}
          }
        }
        isLightMode
        image{
          title
          url
        }
        backgroundImage{
          title
          url
        }
        calloutInfoCardCollection{
          items{
            __typename
            template
            heading
            description
            button {
              ...on LinkComponent{
              ${linkFragment}
              }
            }
            icon
            image{
              url
              title
            }
            size
            alignment
          }
        }
    }
`
