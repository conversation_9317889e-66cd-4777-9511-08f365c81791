export const richtextQuery = (id: string, locale: string) => `{
    componentRichtext(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
        ${richtextFragment}
    }
}
`
export const richtextQueryMultipleId = (ids: Array<string>, locale: string) => ` {
 componentRichtextCollection(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}",where:{sys:{id_in:["${ids.join(`","`)}"]}}){
  items{
   ${richtextFragment}
  }
}
}
`


export const richtextFragment = `
    ...on ComponentRichtext{
        sys{
            id
        }
        __typename
        content
        htmlAttr
        isFullXBleed
        isDocumentation
    }
`
