export const layoutContainerQuery = (id: string, locale: string) => `
{
  componentLayoutContainer(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${layoutContainerFragment}
  }
}
`

export const layoutContainerFragment = `
    ... on ComponentLayoutContainer{
        sys{
            id
        }
        internalName
        numberOfColumns
        horizontalContentAlignment
        verticalContentAlignment
        rowGap
        columnGap
        isFullXBleed
        htmlAttr
        __typename
        layoutContainerItemsCollection{
            items{
                __typename
                sys{
                    id
                }
            }
        }
    }
 `
