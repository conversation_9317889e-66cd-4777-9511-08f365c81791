import { imageFragment } from './image.query'
import { layoutRowFragment } from './layoutRow.query'
import { linkFragment } from './link.query'
import { richtextFragment } from './richtext.query'

export const formQuery = (id: string, locale: string) => `
{
  componentForm(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${formFragment}
  }
}`
export const formFieldQuery = (id: string, locale: string) => `
{
  formField(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${formFieldFragment}
  }
}`

export const formFieldFragment = `
    ... on FormField{
      __typename
      altusLabel
      altusFieldName
      altusFieldType
      altusDataFormat
      altusFieldValues
      altusId
      altusIsRequired
      validationErrorMessage
      helperText
      placeholderText
      isAltusEmailAllowed
      isGenericEmailAllowed
      userFieldLabels
      altusFieldValues
      appendValueInComment
      altusDependentsCollection(limit: 5){
        items{
          __typename
          dependentValue
          dependentFieldsCollection(limit: 5){
            items{
              __typename
              altusLabel
              altusFieldName
              altusFieldType
              altusDataFormat
              altusFieldValues
              altusId
              altusIsRequired
              validationErrorMessage
              helperText
              placeholderText
              userFieldLabels
              altusFieldValues
              appendValueInComment
              altusDependentsCollection(limit: 3){
                items{
                  __typename
                  dependentValue
                  dependentFieldsCollection(limit: 5){
                    items{
                      __typename
                      altusLabel
                      altusFieldName
                      altusFieldType
                      altusDataFormat
                      altusFieldValues
                      altusId
                      altusIsRequired
                      validationErrorMessage
                      helperText
                      placeholderText
                      userFieldLabels
                      altusFieldValues
                      appendValueInComment
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
`

export const formSectionFragment = `
  ... on FormSection{
    internalName
    footer{
      ${richtextFragment}
    }
    header{
      ${richtextFragment}
    }
    sectionItemsCollection(limit: 10){
      items{
        ... on ComponentLayoutRow{
          sys{
              id
          }
          __typename
        }
        ... on FormField{
          sys{
            id
          }
          __typename
        }
      }
    }
  }
`

export const formFragment = `
... on ComponentForm {
    endpointUrl
    sfmcUrl
    sys{
      id
    }
    footer{
      ${richtextFragment}
    }
    header{
      ${richtextFragment}
    }
    __typename
    template
    backgroundImage {
        ${imageFragment}
    }
    thankYouMessage
    loadingMessage{
      ${richtextFragment}
    }
    successMessage{
      ${richtextFragment}
    }
    errorMessage{
      ${richtextFragment}
    }
    postSuccessMessage{
      ${richtextFragment}
    }
    displayAt
    isStatic
    htmlAttr
    isLightMode
    displayPostAction
    displayInternalLink{
      slug
    }
    formFieldsCollection(limit: 15){
      items{
        ${formFieldFragment}
        ${formSectionFragment}
        ${layoutRowFragment}
        ${linkFragment}
      }
    }
    hiddenFieldsCollection(limit: 15){
      items{
        __typename
        sys{
          id
        }
      }
    }
  }
`
