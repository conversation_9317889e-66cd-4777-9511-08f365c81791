export const linkFragment = `
... on LinkComponent {
  sys {
    id
  }
  __typename
  isExperimentation
  experimentEvents
  internalName
  template
  htmlAttr
  text
  externalLink
  asset{
    url
  }
  sectionId
  fieldMapping
  icon
  iconPlacement
  isLightMode
  isChevron2Arrow
  openInNewTab
  internalLink {
    slug
  }
  image{
    url,
    title
  }
  actionContent {
    __typename
    ...on ComponentPopup{
      sys {
        id
      }
    }
    ... on ComponentForm {
      sys {
        id
      }
    }
  }
}`

export const linkQuery = (id: string, locale: string) => `
    {
      linkComponent(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}",id:"${id}") {
      ${linkFragment}
    }
  }
    `
