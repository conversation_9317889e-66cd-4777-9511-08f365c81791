import { breadCrumbFragment } from './breadCrumb.query'
import { imageFragment } from './image.query'
import { linkFragment } from './link.query'
import { menuListFragment } from './menuList.query'

export const heroQuery = (id: string, locale: string) => `
{
  heroComponent(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${heroFragment}
  }
}`

export const heroFragment = `
    ... on HeroComponent {
    sys {
        id
     }
    __typename
    template
    height
    isHeroOverlay
    heading
    subHeading
    description
    logoShowcase{
    ... on ComponentLogoShowcase{
      __typename  
            sys {
              id
            }
     }
}
     imageTabs{
   ... on CarouselComponent{
      sys {
        id
      }
      __typename
    }
}

form{
  ... on ComponentForm {
   __typename
    sys{
      id
    }
}
    }

afs{
... on SystemAfs {
    __typename
    sys {
      id
    }
}    
}

    button{
      ...on LinkComponent{
        ${linkFragment}
        }
    }
    isLightMode
    htmlAttr
    showDropdown
    showBreadcrumbs
    isBgImageEffect
    heroDescription: richDescription
    dynamicRichTextCollection{
      items{
        internalName
        content
        htmlAttr
        isFullXBleed
      }
    }
    insightsCardCollection{
      items{
        startTime
        image{
          url
          title
        }
        heading
        subHeading
      }
    }
    insightsCarouselDataCollection{
      
        items{
          image{
            url 
            title
          }
          startTime
          heading
          description
          button{
            text
            externalLink
            icon
            iconPlacement
            isChevron2Arrow
          }
        }
      
    }
    buttonsCollection(limit:2){
      items{
       ${linkFragment}
      }
    }
    bgImage{
      ${imageFragment}
    }
    
    breadCrumb{
      ${breadCrumbFragment}
    }
    logo{
      url
      title
    }
    fieldMapping
    dynamicImagesCollection(limit:5){
      items{
        ... on Asset{
          url
          title
        }
      }
    }
    dropdownList{
      ${menuListFragment}
    }
    menuListCollection(limit: 5){
      items{
        ${menuListFragment}
      }
    }
    videoPlayer{
       __typename
      sys{
        id
      }
    }
  }
`
