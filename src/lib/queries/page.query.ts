import { ctaRowFragment } from "./ctaRow.query"
import { linkFragment } from "./link.query"
import { menuListFragment } from "./menuList.query"

export const getPageFromSlugQuery = (slug: string, locale: string) => process.env.NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT === 'a4' ?  `
{
    recordCollection(
      preview: ${process.env.NEXT_PUBLIC_PREVIEW}
      limit: 1
      locale: "${locale}"
      where: {
        slug: "${slug}"
        contentfulMetadata: { tags: { id_contains_all: "${process.env.NEXT_PUBLIC_DOMAIN}" } }
      }
    ) {
        items {
          sys{
            id
          }
          name
          type
          slug
          props{
            __typename
            json
          }              
        }
    }
}
`
:`
    {
        pageCollection(
          preview: ${process.env.NEXT_PUBLIC_PREVIEW}
          limit: 1
          locale: "${locale}"
          where: {
            slug: "${slug}"
            contentfulMetadata: { tags: { id_contains_all: "${process.env.NEXT_PUBLIC_DOMAIN}" } }
          }
        ) {
            items {
              sys{
                id
              }
              __typename
              internalName
              template
              title
              slug
              seoTitle
              seoDescription
              afsCardTitle
              afsDescription
              seoKeywords
              isTranslucent
              isHeaderNavigationHidden
              isFooterNavigationHidden
              isMasterPageOfExperimentation
              isExperimentation
              experimentationId
              isNavLightMode
              isLightBgImage
              noIndex
              primaryCta{
                ${ctaRowFragment}
              }
              canonicalUrl
              noFollow
              publishDate
              pageThumbnail{
                url
              }
              document{
                url
                title
              }
              introSection{
                 __typename
                sys{
                  id
                }
              }
              menuList{
                ${menuListFragment}
              }
              aboutUs{
                __typename
                sys{
                  id
                }
              }
              hero{
                __typename
                 sys{
                  id
                }
              }
              dts{
                __typename
                sys{
                  id
                }
              }
              endContentCollection(limit: 30){
               items {
                  __typename
                  sys{
                    id
                  }
                }
              }
              authorsHeading
              authorsCollection{
                items{
                  __typename
                  template
                  fullName
                  tags
                  jobTitle
                  companyName
                  isLightMode
                  image {
                    url
                  }
                  button {
                    ...on LinkComponent{
                    ${linkFragment}
                    }
                  }
                }
              }
              contentCollection(limit: 30){
                items {
                  __typename
                  sys{
                    id
                  }
                }
              }
            }
        }
    }
`

export const getPageFromTagsQuery = (
  tags: string[],
  limit: string,
  order: string,
  condition: string,
  locale: string = 'en-CA',
  idContent?: string
) => {
  let orCond = ''

  if (condition === 'id_contains_some') {
    orCond = `id_contains_all: ["${idContent}", "${process.env.NEXT_PUBLIC_DOMAIN}"]`
  }

  return `{
  pageCollection(
    preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}"
    limit: ${limit}
    order: ${order}
    where: {
      contentfulMetadata: {
        tags: {
          ${condition}: [
            ${tags}
          ]
          ${orCond}
        }
      }
    }
  ) {
    items {
      contentfulMetadata{
        tags{
          id
          name
        }
      }
      shortTitle
      internalName
      __typename
      template
      seoDescription
      afsCardTitle
      afsDescription
      title
      slug
      pageThumbnail{
        url
        title
      }
      publishDate
    }
  }
}
`
}

export const getPageFromTagsHeroQuery = (
  limit: string,
  order: string,
  condition,
  locale: string = 'en-CA'
) => {
  return `{
  pageCollection(
    preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}"
    limit: ${limit}
    order: ${order}
    where: {
      contentfulMetadata: {
        tags: ${condition}
      }
    }
  ) {
    items {
      contentfulMetadata{
        tags{
          id
          name
        }
      }
      shortTitle
      internalName
      __typename
      template
      seoDescription
      afsCardTitle
      afsDescription
      title
      slug
      pageThumbnail{
        url
        title
      }
      publishDate
    }
  }
}
`
}

export const pageLocaleSlugById = (id: string) => `
query {
  en: page(id: "${id}", locale: "en-CA", preview: ${process.env.NEXT_PUBLIC_PREVIEW}) {
    slug
  }
  fr: page(id: "${id}", locale: "fr-CA", preview: ${process.env.NEXT_PUBLIC_PREVIEW}) {
    slug
  }
  de: page(id: "${id}", locale: "de-DE", preview: ${process.env.NEXT_PUBLIC_PREVIEW}) {
    slug
  }
  es: page(id: "${id}", locale: "es", preview: ${process.env.NEXT_PUBLIC_PREVIEW}) {
    slug
  }
  it: page(id: "${id}", locale: "it", preview: ${process.env.NEXT_PUBLIC_PREVIEW}) {
    slug
  }
  nl: page(id: "${id}", locale: "nl", preview: ${process.env.NEXT_PUBLIC_PREVIEW}) {
    slug
  }
}
`

export const pageUrlQueryByLocale = (
  locale: string = 'en-CA',
  excludeNoIndexPages: Boolean = false
) => `
  query {
    pageCollection(
      locale: "${locale}",
      limit:1000,
      where: {
        slug_exists: true,
        ${excludeNoIndexPages ? 'noIndex_not: true,' : ''}
        contentfulMetadata: {
          tags: { id_contains_some: "${process.env.NEXT_PUBLIC_DOMAIN}" }
        }
      }
    ) {
      items {
        slug
        publishDate
        sys{
          publishedAt
        }
      }
    }
  }
`

export const pagesForExperimentation = (locale: string = 'en-CA') => `
  query {
    configurationsCollection(
      locale: "${locale}",
      preview: ${process.env.NEXT_PUBLIC_PREVIEW},
      limit:1000,
    ) {
     items {
        sys {
          id
        }
        internalName
	    type
      }
    }
  }
`
export const getGlobalURLJsonCollectionQuery = (locale: string = 'en-CA') => `
  query {
    configurationsCollection(
      locale: "${locale}",
      preview: ${process.env.NEXT_PUBLIC_PREVIEW},
      limit:1000,
    ) {
     items {
        sys {
          id
        }
        internalName
	    type
      }
    }
  }
`

export const getIdFromSlugQuery = (slug: string, locale: string) => `
{
  pageCollection(
    limit: 1,
    preview:${process.env.NEXT_PUBLIC_PREVIEW},
    locale:"${locale}"
    where: {
      slug: "${slug}"
      contentfulMetadata:{
        tags:{id_contains_all:["${process.env.NEXT_PUBLIC_DOMAIN}"]}
      }
    }) {
    items {
      sys {
        id
      }
      slug
    }
  }
}
`
