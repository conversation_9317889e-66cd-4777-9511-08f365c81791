import { linkFragment } from './link.query'

export const breadCrumbQuery = (id: string, locale: string) => `
{
  breadCrumbComponent(preview:${process.env.NEXT_PUBLIC_PREVIEW},locale:"${locale}", id: "${id}") {
    ${breadCrumbFragment}
  }
}`

export const breadCrumbFragment = `
... on BreadCrumbComponent {
  sys {
    id
  }
  __typename
  template
  endText
  isLightMode
  isExperimentation
  experimentEvents
  linksCollection(limit: 4){
    items {
      ${linkFragment}
    }
  }
}
`
