import { linkFragment } from './link.query'
import { playerFragment } from './player.query'

export const accordionQuery = (id: string, locale: string) => `
{
  componentAccordion(preview:${process.env.NEXT_PUBLIC_PREVIEW},id:"${id}",locale:"${locale}") {
    ${accordionFragment}
  }
}`

export const accordionItemQuery = (id: string, locale: string) => `
{
  componentAccordionItem(preview:${process.env.NEXT_PUBLIC_PREVIEW},id:"${id}",locale:"${locale}") {
    ${accordionItemFragment}
  }
}`

export const accordionFragment = `
... on ComponentAccordion {
  __typename
  sys {
    id
  }
  htmlAttr
  heading
  description
  subheading
  headingAlignment
  isOpen
  isFaq
  accordionItemsCollection {
    items{
      __typename
      sys{
        id
      }
    }
  }
}
`

export const accordionItemFragment = `
... on ComponentAccordionItem {
  sys {
    id
  }
  __typename
  accordionItemHeading
  template
  accordionItemPlayerCollection{
    items{
      ...on Player{
        ${playerFragment}
      }
    }
  }
  content
  itemLink{
    ...on LinkComponent{
      ${linkFragment}
      }
  }
  accordionItemImage {
    url
    title
  }
  linkListCollection(limit: 10){
    items{
     ${linkFragment}
    }
  }
  itemBlocksCollection(limit: 10) {
    items {
      ... on AccordionItemBlock {
        richContent
      }
      linkListCollection(limit:10) {
        ... on AccordionItemBlockLinkListCollection {
          items {
           ${linkFragment}
          }
        }
      }
        heading
      image{
        url
        title
      }
    }
  }
}

`
