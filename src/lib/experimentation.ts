import { NextFetchEvent, NextRequest, NextResponse } from 'next/server'
import <PERSON>atsig from 'statsig-node'
import { corsAllowedOrigins } from '../globals/utils'
import { BUISNESS_DOMAINS } from '../utils'
import { fetchGraphQL, getEntryDataById } from './api'
import { EVEN_ODD, IS_UUID, UID_COOKIE, USER_EVN_ODD_ID } from './constant'
import { getGlobalURLJsonCollectionQuery } from './queries/page.query'
// import { GraphQLResponse } from './types'; // Ensure this import matches your actual GraphQL response type

export const getUserIDforExperiment = (request: NextRequest) => {
  let userId = request.cookies.get(UID_COOKIE)?.value
  let hasUserId = !!userId

  // If there's no active user ID in cookies or its value is invalid, get a new one
  if (!userId || !IS_UUID.test(userId)) {
    userId = crypto.randomUUID()
    hasUserId = false
  }
  return {
    userId,
    hasUserId,
  }
}
export const getUserEvenOddIDforExperiment = (
  request: NextRequest,
  globalUserCount: number,
  countFunction: Function
) => {
  let UserEvenOddId = +(request?.cookies?.get(USER_EVN_ODD_ID)?.value ?? 0)
  let EvenOddGroup = request.cookies.get(EVEN_ODD)?.value
  let hasUserEvenOddId = !!UserEvenOddId

  if (UserEvenOddId && +UserEvenOddId + 1 > globalUserCount) {
    countFunction(+UserEvenOddId + 1)
  }
  // If there's no active user ID in cookies or its value is invalid, get a new one
  if (!UserEvenOddId && EvenOddGroup !== 'even' && EvenOddGroup !== 'odd') {
    UserEvenOddId = globalUserCount
    hasUserEvenOddId = false
    EvenOddGroup = +UserEvenOddId % 2 === 0 ? 'even' : 'odd'
    countFunction(+UserEvenOddId + 1)
  }

  return {
    UserEvenOddId,
    hasUserEvenOddId,
    EvenOddGroup,
  }
}

export async function handelExperimentbasedonVariantParam({
  req,
  event,
  userId,
  hasUserId,
  Paths,
  EvenOddGroup,
  hasUserEvenOddId,
  UserEvenOddId,
  lang,
  variantPage,
}: {
  req: NextRequest
  event: NextFetchEvent
  userId: string
  hasUserId: boolean
  EvenOddGroup?: string
  hasUserEvenOddId?: boolean
  UserEvenOddId?: string | number
  Paths: Record<string, PathData>
  lang: string
  variantPage?: string
}) {
  // const { pathname } = req.nextUrl
  const origin = req.headers.get('origin') ?? ''

  // await Statsig.initialize(
  //   process.env.NEXT_PUBLIC_STATSIG_SERVER_KEY!
  //   // { environment: { tier: 'staging' } } // optional, pass options here if needed
  // )

  // const config = await Statsig.getConfig({ userID: userId }, Paths[pathname])
  // const config = await Statsig.getExperiment(
  //   { userID: userId },
  //   Paths?.[pathname]?.experimentationId ?? 'no-exp'
  // )
  const bucket = variantPage
  const url = req.nextUrl.clone()
  let newurl = bucket
  if (lang && BUISNESS_DOMAINS['altus'] == process.env.NEXT_PUBLIC_DOMAIN) {
    newurl = `${bucket}/${lang}`
  }
  url.pathname = `${newurl}`

  // Response that'll rewrite to the selected bucket
  const res = NextResponse.rewrite(url)
  CookieSetTopagesforExperimentation({
    response: res,
    hasUserId,
    userId,
    EvenOddGroup,
    hasUserEvenOddId,
    UserEvenOddId,
    origin,
  })

  // event.waitUntil(Statsig.flush())

  return res
}

export async function handelExperimentStatsigViaRedirection({
  req,
  event,
  userId,
  hasUserId,
  Paths,
  EvenOddGroup,
  hasUserEvenOddId,
  UserEvenOddId,
  lang,
}: {
  req: NextRequest
  event: NextFetchEvent
  userId: string
  hasUserId: boolean
  EvenOddGroup?: string
  hasUserEvenOddId?: boolean
  UserEvenOddId?: string | number
  Paths: Record<string, PathData>
  lang: string
}) {
  const { pathname } = req.nextUrl
  const origin = req.headers.get('origin') ?? ''

  await Statsig.initialize(
    process.env.NEXT_PUBLIC_STATSIG_SERVER_KEY!
    // { environment: { tier: 'staging' } } // optional, pass options here if needed
  )

  // const config = await Statsig.getConfig({ userID: userId }, Paths[pathname])
  const config = await Statsig.getExperiment(
    { userID: userId },
    Paths?.[pathname]?.experimentationId ?? 'no-exp'
  )
  const bucket = config.get('page', pathname)
  const url = req.nextUrl.clone()
  let newurl = bucket
  if (lang && BUISNESS_DOMAINS['altus'] == process.env.NEXT_PUBLIC_DOMAIN) {
    newurl = `${bucket}/${lang}`
  }
  const variantID = Paths?.[pathname]?.experimentationPages?.findIndex(
    (el) => el === `/${bucket}`
  )
  if (variantID !== -1) {
    url.searchParams.set('variant', variantID)
  }

  // Response that'll rewrite to the selected bucket
  const res = NextResponse.redirect(url)
  CookieSetTopagesforExperimentation({
    response: res,
    hasUserId,
    userId,
    EvenOddGroup,
    hasUserEvenOddId,
    UserEvenOddId,
    origin,
  })

  event.waitUntil(Statsig.flush())

  return res
}

export async function handelExperimentEvenOdd({
  req,
  userId,
  hasUserId,
  Paths,
  EvenOddGroup,
  hasUserEvenOddId,
  UserEvenOddId,
}: {
  req: NextRequest
  event: NextFetchEvent
  userId: string
  hasUserId: boolean
  EvenOddGroup?: string
  hasUserEvenOddId?: boolean
  UserEvenOddId?: string | number
  Paths: Record<string, PathData>
}) {
  const { pathname } = req.nextUrl
  const origin = req.headers.get('origin') ?? ''

  const url = req.nextUrl.clone()
  const assignedPage = getExperimentationPage({
    Paths,
    pathname,
    UserEvenOddId,
  })
  url.pathname = `${assignedPage}`
  const res = NextResponse.rewrite(url)
  CookieSetTopagesforExperimentation({
    response: res,
    hasUserId,
    userId,
    EvenOddGroup,
    hasUserEvenOddId,
    UserEvenOddId,
    origin,
  })

  return res
}

export const CookieSetTopagesforExperimentation = ({
  response,
  hasUserId,
  userId,
  origin = '',
  // hasUserEvenOddId,
  // UserEvenOddId,
  // EvenOddGroup,
}: CookieSetTopagesforExperimentationParams) => {
  if (!hasUserId) {
    response.cookies.set(UID_COOKIE, userId, {
      maxAge: 60 * 60 * 24,
    })
  }
  if (corsAllowedOrigins.includes(origin)) {
    console.log('Setting CORS headers for origin', origin)
    // add the CORS headers to the response
    response.headers.append('Access-Control-Allow-Origin', origin)
    response.headers.append(
      'Access-Control-Allow-Methods',
      'POST, GET, OPTIONS'
    )
    response.headers.append('Access-Control-Allow-Headers', 'Content-Type')
  }
  // if (!hasUserEvenOddId) {
  //     response.cookies.set(USER_EVN_ODD_ID, UserEvenOddId, {
  //         maxAge: 60 * 60 * 24,
  //     })
  //     response.cookies.set(EVEN_ODD, EvenOddGroup, {
  //         maxAge: 60 * 60 * 24,
  //     })
  // }
}

export function getExperimentationPage({
  Paths,
  pathname,
  UserEvenOddId,
}: {
  pathname: string
  UserEvenOddId?: string | number
  Paths: Record<string, PathData>
}): string | undefined {
  const pathData = Paths[pathname]
  if (
    !pathData ||
    !pathData.experimentationPages ||
    pathData.experimentationPages.length === 0
  ) {
    return '/home/'
  }
  const pageIndex =
    (+(UserEvenOddId ?? 1) - 1) % pathData.experimentationPages.length
  return pathData.experimentationPages[pageIndex]
}

type ConfigurationItem = {
  internalName: string
  type: string
  sys: {
    id: string
  }
}

export const GlobalConfigurationDataKeyWise = async (
  key = 'experimentation'
): Promise<any> => {
  try {
    const data1: GraphQLResponse = await fetchGraphQL(
      getGlobalURLJsonCollectionQuery()
    )
    const res = data1?.data?.configurationsCollection
      ?.items as ConfigurationItem[]

    const idToFetch =
      res?.find((item: ConfigurationItem) => {
        return (
          item.internalName.split(' ').includes('AGL') && // 'agl'.toUpperCase() directly to 'AGL'
          item.type === 'MSA'
        )
      })?.sys?.id || ''

    if (idToFetch) {
      const entryData = await getEntryDataById(idToFetch)
      const response =
        entryData?.fields?.data?.content?.[0]?.content?.[0]?.value

      if (response) {
        return JSON.parse(response)?.[key]
      }
    }
  } catch (error) {
    console.error('Error fetching global configuration data:', error)
  }
  return undefined
}
