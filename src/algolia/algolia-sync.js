import algoliasearch from 'algoliasearch/lite.js'
import dotenv from 'dotenv'

if (process.env.NODE_ENV !== 'production') {
  dotenv.config({ path: '.env.local' })
} else {
  dotenv.config({ path: '.env' })
}

const domain = process.env.NEXT_PUBLIC_DOMAIN
// const domain = 'test';

const domainMap = {
  domainAltusGroupCom: 'msa-agl-v3w',
  domainVerifinoCom: 'msa-ver-v3w',
  domainOne11Com: 'msa-o11-v3w',
  domainFinanceActiveCom: 'msa-fia-v3w',
  domainReonomyCom: 'msa-reo-v3w',
  test: 'dev-v3',
}

const algoliaIndex = domainMap[domain]

function fetchGraphQL(query) {
  return fetch(
    `https://graphql.contentful.com/content/v1/spaces/${process.env.NEXT_PUBLIC_CONTENTFUL_SPACE_ID}/environments/${process.env.NEXT_PUBLIC_CONTENTFUL_ENVIRONMENT}`,
    {
      cache: 'no-store',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.CONTENTFUL_ACCESS_TOKEN}`,
      },
      body: JSON.stringify({ query }),
    }
  ).then((response) => response.json())
}

const getPageForAlgoia = () => `
{
    pageCollection( 
      limit:1000
      preview: false
      where: {
        hideFromAlgolia_not:true
        contentfulMetadata: { tags: { id_contains_all: "${process.env.NEXT_PUBLIC_DOMAIN}" } }
        #template_not: "Insight Article"
        #AND: [{ slug_not_contains: "events" }, { slug_not_contains: "webinars" }]
      }) {
        total
        items {
          sys {
            id
          }
          title
          slug
          shortTitle
          seoDescription
          seoKeywords
          pageThumbnail {
            url
          }
        }
    }
}
`

const getPageData = async () => {
  const data = await fetchGraphQL(getPageForAlgoia())
  return data
}

const fetchData = async () => {
  const allPageData = await getPageData()
  console.log('allPageData: ', allPageData)
  return allPageData
}

export const putDataToAlgolia = async () => {
  if (process.env.VERCEL_ENV !== 'production') {
    return
  }
  // Process a POST request
  const algoliaClient = algoliasearch(
    process.env.NEXT_PUBLIC_ALGOLIA_APPLICATION_ID,
    process.env.ALGOLIA_ADMIN_KEY
  )

  console.log('algoliaIndex', algoliaIndex)

  const index = algoliaClient.initIndex(algoliaIndex)

  const finalResponse = await fetchData()

  console.log('finalResponse', finalResponse)

  const algoliaRecords = finalResponse?.data?.pageCollection?.items
    .map((item) => {
      const { sys, ...rest } = item
      const objectID = sys?.id

      if (objectID !== undefined) {
        return {
          objectID,
          ...rest,
        }
      }

      return null // or {}
    })
    .filter(Boolean)

  await index.clearObjects()

  return await index
    .saveObjects(algoliaRecords)
    .then((res) => {
      console.log('Pages indexing into Algolia')
      return res
    })
    .catch((error) => {
      console.error('Error when indexing page into Algolia', error)
      return error
    })
}

putDataToAlgolia()
