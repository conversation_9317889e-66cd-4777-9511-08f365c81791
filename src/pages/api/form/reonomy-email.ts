import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    var myHeaders = new Headers()
    myHeaders.append('Content-Type', 'application/json')
    var raw = JSON.stringify({
      email: req.body.email,
    })

    var requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
      redirect: 'follow',
    }
    const response = await fetch(
      'https://app.reonomy.com/signup-prevalidation',
      requestOptions
    )
    // console.warn('response---->', response.text())
    const data = await response.json()
    return res.status(200).json({
      status: 'success',
      body: req.body,
      data,
    })
    // if (data?.is_generic_email || data?.code === "EMAIL_IS_GENERIC") {
    //   dispatch(setEmailValidationMsg('Generic emails like Gmail or Yahoo not accepted'))
    // } else {
    //   return true
    // }
  } catch (error) {
    console.warn('error', error)
    return res.status(500).json({
      status: 'error',
      message: error?.message,
      body: req.body,
    })
  }
}
