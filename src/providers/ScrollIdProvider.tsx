'use client'

import { useEffect } from 'react'
import { findScrolledElement } from '../systems/AFS/AFSPages/utils'

function ScrollIdProvider({ children, response, allCompDataArray }) {
  useEffect(() => {
    const ComponentIds = []

    const introSection = response?.data?.pageCollection?.items[0]?.introSection
    const introSectionId = introSection?.htmlAttr?.id

    if (introSectionId) {
      ComponentIds.push(introSectionId)
    }

    const processComponent = async (component) => {
      if (component?.__typename === 'ComponentLayoutRow') {
        // Check layoutColumnCollection
        ComponentIds.push(component?.htmlAttr?.id)
        if (component?.layoutColumnCollection?.items) {
          for (const columnItem of component.layoutColumnCollection.items) {
            // Check layoutItemCollection
            if (columnItem?.layoutItemCollection?.items) {
              for (const nestedComponent of columnItem.layoutItemCollection
                .items) {
                // Check htmlAttr.id and add to ComponentIds
                // const mainComp = await getComponentQueryData(
                //   nestedComponent.sys.id,
                //   nestedComponent.__typename
                // )
                const firstProperty = Object.values(nestedComponent || {})[0]
                if (firstProperty?.htmlAttr?.id) {
                  ComponentIds.push(firstProperty?.htmlAttr.id)
                }
              }
            }
          }
        }
      } else {
        // Check htmlAttr.id for other components
        if (component?.htmlAttr?.id) {
          ComponentIds.push(component.htmlAttr.id)
        }
      }
    }

    allCompDataArray.forEach((component) => {
      processComponent(component)
    })
    // setComponentData(allCompDataArray)

    let prevElment = null

    const updateUrl = () => {
      const scrolledElement = findScrolledElement(ComponentIds)
      if (scrolledElement && prevElment !== scrolledElement) {
        const id = scrolledElement.id
        const newUrl = `#${id}`
        window.history.replaceState(null, null, newUrl)
        prevElment = scrolledElement
      } else if (!scrolledElement && prevElment) {
        const newUrl = ' '
        window.history.replaceState(null, null, newUrl)
        prevElment = null
      }
    }

    setTimeout(() => {
      const hash = window.location.hash
      if (hash) {
        const el = document.querySelector(hash)
        if (el) {
          window.scrollTo(0, el.getBoundingClientRect().top - 100)
        }
      }
    }, 1000)

    // setTimeout(() => {
    window.addEventListener('scroll', updateUrl)
    // }, 4000)
    // updateUrl()

    return () => {
      window.removeEventListener('scroll', updateUrl)
    }
  }, [])

  return <>{children}</>
}

export default ScrollIdProvider
