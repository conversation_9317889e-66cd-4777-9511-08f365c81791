'use client'
import { StatsigClient } from '@statsig/js-client'
import { StatsigProvider } from '@statsig/react-bindings'
import { ReactElement, ReactPortal } from 'react'
import { StatsigUser } from 'statsig-node'

type ReactText = string | number
type ReactChild = ReactElement | ReactText
type ReactNode = ReactChild | ReactPortal | boolean | null | undefined

export default function BootStrappedStatsigClient({
  user,
  children,
}: {
  user: StatsigUser
  children: ReactNode
}): JSX.Element {
  // const client = useBootstrappedClient(
  //   process.env.NEXT_PUBLIC_STATSIG_CLIENT_KEY!,
  //   user
  // )
  const myStatsigClient = new StatsigClient(
    process.env.NEXT_PUBLIC_STATSIG_CLIENT_KEY!,
    user
  )
  myStatsigClient.initializeSync()
  // useEffect(() => {
  //   const onAnyClientEvent = (event: AnyStatsigClientEvent) =>
  //   client.on('*', onAnyClientEvent)
  //   return () => client.off('*', onAnyClientEvent)
  // }, [client])

  return <StatsigProvider client={myStatsigClient}>{children}</StatsigProvider>
}
