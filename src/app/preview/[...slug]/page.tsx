import { Metadata } from 'next'
import PagePreviewRoot from '../../../utils/PagePreviewRoot'
import { metadataProcessor } from '../../../utils/pageUtils'

// export const dynamicParams = false // true | false,
// export const revalidate = 3600 // seconds

// MetaData
export async function generateMetadata({
  params,
  // searchParams,
}: unknown): Promise<Metadata> {
  return await metadataProcessor(params)
}

async function Page({
  params,
  // searchParams,
}: {
  params: { slug: Array<string> }
  // searchParams: object
}) {
  return <PagePreviewRoot params={params} logic={4} />
}

// Export the component as a Next.js server component
export default Page

// export async function generateStaticParams() {
//   return await staticParamsProcessor()
// }
