'use client'
import { alignChildComponent, decouple, reassembleChildren } from '../../../utils/previewUtils'
import { Data } from './const'



const page = () => {
  const MainData = JSON.parse(JSON.stringify(Data))

  let alignedChildren = JSON.parse(JSON.stringify(MainData))
  alignChildComponent(alignedChildren)

  // const MainData = MainDataB?.contentCollection?.items?.[0]
  const decoupledData = {}
  decouple(alignedChildren, decoupledData)

  console.log("decoupledData[alignedChildren.sys.id]", decoupledData[alignedChildren.sys.id])
  const reassembledData = { ...alignedChildren }
  reassembleChildren(reassembledData, decoupledData)
  console.log("reassembledData", reassembledData)
  console.log("decoupledData", decoupledData);


  // const nestedData = reassemble(decoupledData)
  // const nestedData = await couple(decoupledData, fetchExternalComponent)

  return (
    <div>
      {/* <PageRouter pageData={alignedChildren} /> */}
    </div>
  )
}

export default page
