export const Data = {
  sys: {
    id: '5qErLRJVXUbrUcy7dPe51j',
  },
  __typename: 'Page',
  internalName: 'AGL - Solutions - ARGUS Enterprise',
  template: 'Generic',
  title:
    'ARGUS Enterprise - Commercial Property Valuation & Asset Management Software ',
  slug: 'solutions/argus-enterprise',
  seoTitle:
    'ARGUS Enterprise - Commercial Property Valuation & Asset Management Software',
  seoDescription:
    'ARGUS Enterprise is a leading commercial property valuation and cash flow forecasting software trusted by CRE professionals worldwide.',
  seoKeywords: [
    'argus enterprise',
    'property valuation software',
    'commercial property valuation software',
    'argus enterprise training',
    'argus enterprise certification',
    'real estate forecasting software',
  ],
  isTranslucent: false,
  isHeaderNavigationHidden: false,
  isFooterNavigationHidden: false,
  noIndex: false,
  noFollow: false,
  endContentCollection: {
    items: [],
  },
  authorsCollection: {
    items: [],
  },
  contentCollection: {
    items: [
      {
        sys: {
          id: '6LQNXcgKwC9BuaP0hFUNQV',
        },
        __typename: 'HeroComponent',
        template: 'Twocolumn - Primary',
        heading: 'ARGUS Enterprise',
        isLightMode: true,
        htmlAttr: {
          className: 'bn7',
        },
        showDropdown: false,
        showBreadcrumbs: true,
        isBgImageEffect: false,
        heroDescription: {
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              attrs: {
                textAlign: 'left',
              },
              content: [
                {
                  text: 'A leading ',
                  type: 'text',
                  marks: [
                    {
                      type: 'altusText',
                      attrs: {},
                    },
                  ],
                },
                {
                  text: 'commercial property valuation, cash flow forecasting and asset management software',
                  type: 'text',
                  marks: [
                    {
                      type: 'textStyle',
                      attrs: {
                        color: '',
                      },
                    },
                    {
                      type: 'altusText',
                      attrs: {},
                    },
                  ],
                },
                {
                  text: ' solution.',
                  type: 'text',
                  marks: [
                    {
                      type: 'altusText',
                      attrs: {},
                    },
                  ],
                },
              ],
            },
          ],
        },
        dynamicRichTextCollection: {
          items: [],
        },
        insightsCardCollection: {
          items: [],
        },
        insightsCarouselDataCollection: {
          items: [],
        },
        dynamicImagesCollection: {
          items: [],
        },
        menuListCollection: {
          items: [],
        },
        breadCrumb: {
          sys: {
            id: '526YlvsAxzmi4TkrVHL5Tm',
          },
          __typename: 'BreadCrumbComponent',
          template: 'Short',
          endText: 'Solutions',
          isLightMode: true,
          linksCollection: {
            items: [],
          },
        },
        buttonsCollection: {
          items: [
            {
              sys: {
                id: '3FydpO4CvYf26bj7GgHilR',
              },
              __typename: 'LinkComponent',
              internalName: 'AGL - CTA Form - ARGUS Enterprise - Primary',
              template: 'Primary',
              text: 'Book a free demo',
              sectionId: '#overview',
              icon: 'RightChevron',
              iconPlacement: 'Suffix',
              isLightMode: true,
              isChevron2Arrow: true,
              openInNewTab: false,
            },
          ],
        },
        bgImage: {
          sys: {
            id: '45eSTZ9LvPnmBPz9S6dxVV',
          },
          __typename: 'ComponentImage',
          internalName: 'AGL - Solutions - ARGUS Enterprise',
          verticalAlignment: 'bottom',
          horizontalAlignment: 'right',
          objectFit: 'contain',
          imageFile: {
            url: 'https://images.ctfassets.net/8jgyidtgyr4v/5afrwEreeMVzco7vzEqD74/b6595917424ee3f06ad369594842cf4b/Solutions-ARGUSEnterprise-Hero-900x650.png',
            height: 650,
            width: 900,
          },
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '7ckXK2tzVZR4FC1SH9FKVY',
        },
        __typename: 'ComponentNavigationHeader',
        template: 'Sticky',
        isLoginHidden: false,
        isLanguageHidden: false,
        isSearchHidden: false,
        isLightMode: true,
        buttonGroupCollection: {
          items: [],
        },
        ctaButton: {
          sys: {
            id: '3FydpO4CvYf26bj7GgHilR',
          },
          __typename: 'LinkComponent',
          internalName: 'AGL - CTA Form - ARGUS Enterprise - Primary',
          template: 'Primary',
          text: 'Book a free demo',
          sectionId: '#overview',
          icon: 'RightChevron',
          iconPlacement: 'Suffix',
          isLightMode: true,
          isChevron2Arrow: true,
          openInNewTab: false,
        },
        navigationMenuItemsCollection: {
          items: [
            {
              template: 'Single Dropdown',
              menuListsCollection: {
                items: [],
              },
              featuredCardsCollection: {
                items: [],
              },
              cardListsCollection: {
                items: [],
              },
              menuLink: {
                sys: {
                  id: '4bWzHXhWiZDvQlzlrmrmDo',
                },
                __typename: 'LinkComponent',
                internalName: 'AGL - General Menu - Overview',
                template: 'Primary',
                text: 'Overview',
                sectionId: '#overview',
                isLightMode: true,
                openInNewTab: false,
              },
            },
            {
              template: 'Single Dropdown',
              menuListsCollection: {
                items: [],
              },
              featuredCardsCollection: {
                items: [],
              },
              cardListsCollection: {
                items: [],
              },
              menuLink: {
                sys: {
                  id: '1V8kVnC9bBx94ClAGHur8K',
                },
                __typename: 'LinkComponent',
                internalName: 'AGL - General Menu - Key Features',
                template: 'Primary',
                text: 'Key features',
                sectionId: '#keyfeatures',
                isLightMode: true,
                openInNewTab: false,
              },
            },
            {
              template: 'Single Dropdown',
              menuListsCollection: {
                items: [],
              },
              featuredCardsCollection: {
                items: [],
              },
              cardListsCollection: {
                items: [],
              },
              menuLink: {
                sys: {
                  id: '3EuU2j1OFU8c0AcgIVGkx',
                },
                __typename: 'LinkComponent',
                internalName: 'AGL - General Menu - Why Altus',
                template: 'Primary',
                text: 'Why Altus',
                sectionId: '#whyaltus',
                isLightMode: true,
                openInNewTab: false,
              },
            },
            {
              template: 'Single Dropdown',
              menuListsCollection: {
                items: [],
              },
              featuredCardsCollection: {
                items: [],
              },
              cardListsCollection: {
                items: [],
              },
              menuLink: {
                sys: {
                  id: '27AX7cgd0Z3YmoxynTLltt',
                },
                __typename: 'LinkComponent',
                internalName: 'AGL - General Menu - Training',
                template: 'Primary',
                text: 'Training',
                sectionId: '#training',
                isLightMode: true,
                openInNewTab: false,
              },
            },
            {
              template: 'Single Dropdown',
              menuListsCollection: {
                items: [],
              },
              featuredCardsCollection: {
                items: [],
              },
              cardListsCollection: {
                items: [],
              },
              menuLink: {
                sys: {
                  id: '32xpUl1dw6u9jxNZs1EoVr',
                },
                __typename: 'LinkComponent',
                internalName: 'AGL - General Menu - Customers',
                template: 'Primary',
                text: 'Customers',
                sectionId: '#customers',
                isLightMode: true,
                openInNewTab: false,
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '6XOWdl5x4M0N3tUOYAsxyV',
        },
        __typename: 'ComponentLayoutRow',
        internalName: 'AGL - Solutions - ARGUS Enterprise - Overview',
        htmlAttr: {
          id: 'overview',
        },
        isFullXBleed: false,
        layoutColumnCollection: {
          items: [
            {
              sys: {
                id: '4psGv5urVhIJbE1CsHFIx7',
              },
              __typename: 'ComponentLayoutColumn',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentRichtext',
                    sys: {
                      id: '3sJaVIDbsANflJnvpbjkpc',
                    },
                    componentRichtext: {
                      sys: {
                        id: '3sJaVIDbsANflJnvpbjkpc',
                      },
                      __typename: 'ComponentRichtext',
                      content: {
                        type: 'doc',
                        content: [
                          {
                            type: 'heading',
                            attrs: {
                              textAlign: 'left',
                              level: 3,
                            },
                            content: [
                              {
                                type: 'text',
                                text: 'ARGUS Enterprise is an industry-leading commercial property valuation and cash flow forecasting software ',
                              },
                              {
                                type: 'text',
                                marks: [
                                  {
                                    type: 'textStyle',
                                    attrs: {
                                      color: '#0028d7',
                                    },
                                  },
                                ],
                                text: 'enabling you to better manage your commercial assets and property portfolio.',
                              },
                            ],
                          },
                        ],
                      },
                      isFullXBleed: false,
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '63wJvZ4Y62YMw81G9fuL65',
              },
              __typename: 'ComponentLayoutColumn',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentForm',
                    sys: {
                      id: '7rRh3VyD6SDwL7wsURQy40',
                    },
                    componentForm: {
                      sys: {
                        id: '7rRh3VyD6SDwL7wsURQy40',
                      },
                      __typename: 'ComponentForm',
                      endpointUrl:
                        'https://go.altusgroup.com/l/575253/2023-05-21/36gpqf',
                      sfmcUrl: 'https://cloud.hello.altusgroup.com/wxiwnw0smra',
                      template: 'MultiStep',
                      thankYouMessage: {
                        type: 'doc',
                        content: [
                          {
                            type: 'heading',
                            attrs: {
                              level: 3,
                              textAlign: 'left',
                            },
                            content: [
                              {
                                text: 'Thank you for your inquiry',
                                type: 'text',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                text: 'An Altus Group representative will be in touch with you shortly. In the meantime, feel free to catch up on our latest CRE articles, guides and market research on our ',
                                type: 'text',
                              },
                              {
                                text: 'insights page',
                                type: 'text',
                                marks: [
                                  {
                                    type: 'link',
                                    attrs: {
                                      href: '',
                                      class: 'link',
                                      entity: {
                                        sys: {
                                          id: '18vqQ0c0a1XKexDXSemBna',
                                          type: 'Link',
                                          linkType: 'Entry',
                                        },
                                        slug: {
                                          es: 'insights',
                                          it: 'insights',
                                          nl: 'insights',
                                          'de-DE': 'insights',
                                          'en-CA': 'insights',
                                          'fr-CA': 'insights',
                                        },
                                      },
                                      target: '_self',
                                      slug: '/insights/',
                                    },
                                  },
                                ],
                              },
                              {
                                text: '.',
                                type: 'text',
                              },
                            ],
                          },
                        ],
                      },
                      isStatic: false,
                      hiddenFieldsCollection: {
                        items: [
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '6RA38evbAvO68iwcvFoP86',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Business Unit',
                              altusFieldName: 'businessunit',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['AA'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '1KaEsaC7DbEV6JSZlT8i9f',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Campaign response',
                              altusFieldName: 'campaignresponse',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['Responded'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '5yZ1rO82vt7Gh6yf8tue1j',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Form Name',
                              altusFieldName: 'formname',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: [
                                'Altus Website - ARGUS Enterprise',
                              ],
                              altusIsRequired: true,
                              isAltusEmailAllowed: true,
                              isGenericEmailAllowed: true,
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '5kWdBGtFTsTY7quqC8CXFJ',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Form type',
                              altusFieldName: 'formtype',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['Solution'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '2fzed5Tz0IICGqvrD48piK',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Hot Pass',
                              altusFieldName: 'hotpass',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['True'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '2G72PH287aJMSVikFG7bE3',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Interest',
                              altusFieldName: 'interest',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['Software'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '4sVWCDYimpSJJY68WLvzZt',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Inquiry Type - Sales',
                              altusFieldName: 'contactinquirytype',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['Sales'],
                              altusIsRequired: false,
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '2G4ra0z2VYwkNL8y2CtanO',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Primary Campaign',
                              altusFieldName: 'sfid',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['7012M000002mQfs'],
                              altusIsRequired: true,
                              isAltusEmailAllowed: true,
                              isGenericEmailAllowed: true,
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: '1w5oSbVo6xKArig1pWH07n',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Product Interest',
                              altusFieldName: 'productinterest',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['ARGUS Enterprise'],
                              altusIsRequired: true,
                              isAltusEmailAllowed: true,
                              isGenericEmailAllowed: true,
                              userFieldLabels: ['ARGUS Enterprise'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'FormField',
                            sys: {
                              id: 'vFcajUQhnuP8X2P6jkgLg',
                            },
                            formField: {
                              __typename: 'FormField',
                              altusLabel: 'Rating',
                              altusFieldName: 'rating',
                              altusFieldType: 'Hidden',
                              altusDataFormat: 'Text',
                              altusFieldValues: ['Hot'],
                              altusDependentsCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                        ],
                      },
                      footer: {
                        sys: {
                          id: '503okjNmlg1Q2j2XyBdEhW',
                        },
                        __typename: 'ComponentRichtext',
                        content: {
                          type: 'doc',
                          content: [
                            {
                              type: 'paragraph',
                              attrs: {
                                textAlign: 'left',
                              },
                              content: [
                                {
                                  text: 'By clicking submit, you consent to Altus Group Limited (including any of its affiliates and subsidiaries) contacting you by email and phone (to your email address and phone number listed on this form) with information about industry insights, market reports, upcoming events, and our products and services. Your consent may be withdrawn at any time, including by unsubscribing. Contact us if you have any questions, by visiting ',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '#757575',
                                      },
                                    },
                                    {
                                      type: 'altusText',
                                      attrs: {
                                        class: 'fs2',
                                      },
                                    },
                                  ],
                                },
                                {
                                  text: 'here',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'link',
                                      attrs: {
                                        href: '',
                                        class: 'link',
                                        entity: {
                                          sys: {
                                            id: '2IjycTLKNL93KTwEW2Rbta',
                                            type: 'Link',
                                            linkType: 'Entry',
                                          },
                                          slug: {
                                            'de-DE': 'home',
                                            'en-CA': 'contact-us',
                                            'fr-CA': 'contact-us',
                                          },
                                        },
                                        target: '_self',
                                        slug: '/contact-us/',
                                      },
                                    },
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '#0028d7',
                                      },
                                    },
                                    {
                                      type: 'altusText',
                                      attrs: {
                                        class: 'fs2',
                                      },
                                    },
                                  ],
                                },
                                {
                                  text: '. Please note: consent is not a condition of purchasing any of our products or services. Further details of our processing are in the ',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '#757575',
                                      },
                                    },
                                    {
                                      type: 'altusText',
                                      attrs: {
                                        class: 'fs2',
                                      },
                                    },
                                  ],
                                },
                                {
                                  text: 'Altus Group Privacy Notice',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'link',
                                      attrs: {
                                        href: '',
                                        class: 'link',
                                        entity: {
                                          sys: {
                                            id: '3WWkiN8eoEmid9GrArFUzA',
                                            type: 'Link',
                                            linkType: 'Entry',
                                          },
                                          slug: {
                                            'de-DE': 'home',
                                            'en-CA': 'legal/privacy-policy',
                                            'fr-CA': 'legal/privacy-policy',
                                          },
                                        },
                                        target: '_self',
                                        slug: '/legal/privacy-policy/',
                                      },
                                    },
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '#0028d7',
                                      },
                                    },
                                    {
                                      type: 'altusText',
                                      attrs: {
                                        class: 'fs2',
                                      },
                                    },
                                  ],
                                },
                                {
                                  text: '.',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '#757575',
                                      },
                                    },
                                    {
                                      type: 'altusText',
                                      attrs: {
                                        class: 'fs2',
                                      },
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        isFullXBleed: false,
                      },
                      loadingMessage: {
                        sys: {
                          id: '38zQPSkZRqUI7qea7A4Br',
                        },
                        __typename: 'ComponentRichtext',
                        content: {
                          type: 'doc',
                          content: [
                            {
                              type: 'paragraph',
                              attrs: {
                                textAlign: 'left',
                              },
                              content: [
                                {
                                  text: 'Hold tight, while we submit your inquiry.',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '',
                                      },
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        isFullXBleed: false,
                      },
                      successMessage: {
                        sys: {
                          id: '15Aey2HVS3sggg6Uns9Nkx',
                        },
                        __typename: 'ComponentRichtext',
                        content: {
                          type: 'doc',
                          content: [
                            {
                              type: 'paragraph',
                              attrs: {
                                textAlign: 'left',
                              },
                              content: [
                                {
                                  text: 'Your inquiry was successfully submitted.',
                                  type: 'text',
                                },
                              ],
                            },
                          ],
                        },
                        isFullXBleed: false,
                      },
                      errorMessage: {
                        sys: {
                          id: '2XPx3F1S6VfbTWmig8fZEb',
                        },
                        __typename: 'ComponentRichtext',
                        content: {
                          type: 'doc',
                          content: [
                            {
                              type: 'paragraph',
                              attrs: {
                                textAlign: 'left',
                              },
                              content: [
                                {
                                  text: 'Something went wrong. Please try again by refreshing your browser. If your issue persists, please email us at ',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '',
                                      },
                                    },
                                  ],
                                },
                                {
                                  text: '<EMAIL>',
                                  type: 'text',
                                  marks: [
                                    {
                                      type: 'link',
                                      attrs: {
                                        href: 'mailto:<EMAIL>',
                                        class: 'link',
                                        target: '_blank',
                                      },
                                    },
                                    {
                                      type: 'textStyle',
                                      attrs: {
                                        color: '',
                                      },
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        isFullXBleed: false,
                      },
                      formFieldsCollection: {
                        items: [
                          {
                            internalName:
                              'AGL - Solutions - Multistep - Section 1',
                            sectionItemsCollection: {
                              items: [
                                {
                                  sys: {
                                    id: '4XTZbRwZPAjBWg13lZ8Y7U',
                                  },
                                  __typename: 'FormField',
                                  formField: {
                                    __typename: 'FormField',
                                    altusLabel: 'Email',
                                    altusFieldName: 'Email',
                                    altusFieldType: 'Email',
                                    altusDataFormat: 'Email',
                                    altusIsRequired: true,
                                    validationErrorMessage:
                                      'This is a required field',
                                    placeholderText: '<EMAIL>',
                                    isAltusEmailAllowed: true,
                                    isGenericEmailAllowed: true,
                                    altusDependentsCollection: {
                                      items: [],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                                {
                                  sys: {
                                    id: 'oDVDCTwDXjqyDe0PDtS9q',
                                  },
                                  __typename: 'FormField',
                                  formField: {
                                    __typename: 'FormField',
                                    altusLabel: 'Country',
                                    altusFieldName: 'country',
                                    altusFieldType: 'Country',
                                    altusDataFormat: 'Text',
                                    altusIsRequired: true,
                                    validationErrorMessage:
                                      'This field is required',
                                    altusDependentsCollection: {
                                      items: [
                                        {
                                          __typename: 'FormDependent',
                                          dependentValue: 'United States',
                                          dependentFieldsCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                altusLabel: 'State',
                                                altusFieldName: 'state',
                                                altusFieldType: 'Dropdown',
                                                altusDataFormat: 'Text',
                                                altusFieldValues: [
                                                  'Alabama',
                                                  'Alaska',
                                                  'Arizona',
                                                  'Arkansas',
                                                  'California',
                                                  'Colorado',
                                                  'Connecticut',
                                                  'Delaware',
                                                  'District of Columbia',
                                                  'Florida',
                                                  'Georgia',
                                                  'Hawaii',
                                                  'Idaho',
                                                  'Illinois',
                                                  'Indiana',
                                                  'Iowa',
                                                  'Kansas',
                                                  'Kentucky',
                                                  'Louisiana',
                                                  'Maine',
                                                  'Maryland',
                                                  'Massachusetts',
                                                  'Michigan',
                                                  'Minnesota',
                                                  'Mississippi',
                                                  'Missouri',
                                                  'Montana',
                                                  'Nebraska',
                                                  'Nevada',
                                                  'New Hampshire',
                                                  'New Jersey',
                                                  'New Mexico',
                                                  'New York',
                                                  'North Carolina',
                                                  'North Dakota',
                                                  'Ohio',
                                                  'Oklahoma',
                                                  'Oregon',
                                                  'Pennsylvania',
                                                  'Rhode Island',
                                                  'South Carolina',
                                                  'South Dakota',
                                                  'Tennessee',
                                                  'Texas',
                                                  'Utah',
                                                  'Vermont',
                                                  'Virginia',
                                                  'Washington',
                                                  'West Virginia',
                                                  'Wisconsin',
                                                  'Wyoming',
                                                ],
                                                altusIsRequired: true,
                                                validationErrorMessage:
                                                  'This field is required',
                                                altusDependentsCollection: {
                                                  items: [],
                                                },
                                              },
                                            ],
                                          },
                                        },
                                        {
                                          __typename: 'FormDependent',
                                          dependentValue: 'Canada',
                                          dependentFieldsCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                altusLabel: 'State',
                                                altusFieldName: 'state',
                                                altusFieldType: 'Dropdown',
                                                altusDataFormat: 'Text',
                                                altusFieldValues: [
                                                  'Alberta',
                                                  'British Columbia',
                                                  'Manitoba',
                                                  'New Brunswick',
                                                  'Newfoundland and Labrador',
                                                  'Northwest Territories',
                                                  'Nova Scotia',
                                                  'Nunavut',
                                                  'Ontario',
                                                  'Prince Edward Island',
                                                  'Quebec',
                                                  'Saskatchewan',
                                                  'Yukon',
                                                ],
                                                altusIsRequired: true,
                                                validationErrorMessage:
                                                  'This field is required',
                                                altusDependentsCollection: {
                                                  items: [],
                                                },
                                              },
                                            ],
                                          },
                                        },
                                        {
                                          __typename: 'FormDependent',
                                          dependentValue: 'Australia',
                                          dependentFieldsCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                altusLabel: 'State',
                                                altusFieldName: 'state',
                                                altusFieldType: 'Dropdown',
                                                altusDataFormat: 'Text',
                                                altusFieldValues: [
                                                  'Australian Capital Territory',
                                                  'New South Wales',
                                                  'Queensland',
                                                  'Northern Territory',
                                                  'South Australia',
                                                  'Tasmania',
                                                  'Victoria',
                                                  'Western Australia',
                                                ],
                                                altusIsRequired: true,
                                                validationErrorMessage:
                                                  'This field is required',
                                                altusDependentsCollection: {
                                                  items: [],
                                                },
                                              },
                                            ],
                                          },
                                        },
                                      ],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                            header: {
                              sys: {
                                id: '6kYdXZ7jBABA2TQs6bqSDJ',
                              },
                              __typename: 'ComponentRichtext',
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'heading',
                                    attrs: {
                                      level: 4,
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        text: 'Speak with the Altus team',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '',
                                            },
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                  },
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        text: 'We need just a few quick details.',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '',
                                            },
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              isFullXBleed: false,
                            },
                          },
                          {
                            internalName:
                              'AGL - Solutions - MultiStep - Section 2',
                            sectionItemsCollection: {
                              items: [
                                {
                                  sys: {
                                    id: '61Lz8BSHTeXzt0NrtKunSV',
                                  },
                                  __typename: 'ComponentLayoutRow',
                                  componentLayoutRow: {
                                    sys: {
                                      id: '61Lz8BSHTeXzt0NrtKunSV',
                                    },
                                    __typename: 'ComponentLayoutRow',
                                    internalName:
                                      'MSA - Forms - First Name & Last Name',
                                    isFullXBleed: false,
                                    layoutColumnCollection: {
                                      items: [
                                        {
                                          sys: {
                                            id: '7B8RJRObuw12p2cFYAuMoo',
                                          },
                                          __typename: 'ComponentLayoutColumn',
                                          layoutItemCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                sys: {
                                                  id: '7dyJvlLQRpnDV4aSM9HGb5',
                                                },
                                                formField: {
                                                  __typename: 'FormField',
                                                  altusLabel: 'First name',
                                                  altusFieldName: 'firstname',
                                                  altusFieldType: 'Text',
                                                  altusDataFormat: 'Text',
                                                  altusIsRequired: true,
                                                  validationErrorMessage:
                                                    'This is a required field',
                                                  placeholderText: 'John',
                                                  altusDependentsCollection: {
                                                    items: [],
                                                  },
                                                  locale: 'en-CA',
                                                  fullUrl: '',
                                                },
                                              },
                                            ],
                                          },
                                        },
                                        {
                                          sys: {
                                            id: '6OHyIlZksdcGgwcPbbFk6c',
                                          },
                                          __typename: 'ComponentLayoutColumn',
                                          layoutItemCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                sys: {
                                                  id: '7ISO7q8d655r3XKopb3yh0',
                                                },
                                                formField: {
                                                  __typename: 'FormField',
                                                  altusLabel: 'Last name',
                                                  altusFieldName: 'lastname',
                                                  altusFieldType: 'Text',
                                                  altusDataFormat: 'Text',
                                                  altusIsRequired: true,
                                                  validationErrorMessage:
                                                    'This is a required field',
                                                  placeholderText: 'Smith',
                                                  altusDependentsCollection: {
                                                    items: [],
                                                  },
                                                  locale: 'en-CA',
                                                  fullUrl: '',
                                                },
                                              },
                                            ],
                                          },
                                        },
                                      ],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                                {
                                  sys: {
                                    id: 'zCZW0gBRIUTqtImgT0X9Q',
                                  },
                                  __typename: 'ComponentLayoutRow',
                                  componentLayoutRow: {
                                    sys: {
                                      id: 'zCZW0gBRIUTqtImgT0X9Q',
                                    },
                                    __typename: 'ComponentLayoutRow',
                                    internalName:
                                      'MSA - Forms - Phone & Job Title',
                                    isFullXBleed: false,
                                    isHeroOverlay: false,
                                    layoutColumnCollection: {
                                      items: [
                                        {
                                          sys: {
                                            id: '2RpnORYFPn8x4xWfHQk4HF',
                                          },
                                          __typename: 'ComponentLayoutColumn',
                                          layoutItemCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                sys: {
                                                  id: '6wW1MIDLgu1W8IYzIZcVqn',
                                                },
                                                formField: {
                                                  __typename: 'FormField',
                                                  altusLabel: 'Phone',
                                                  altusFieldName: 'phone',
                                                  altusFieldType: 'Text',
                                                  altusDataFormat: 'Phone',
                                                  altusIsRequired: true,
                                                  validationErrorMessage:
                                                    'This field is required',
                                                  placeholderText:
                                                    '**************',
                                                  altusDependentsCollection: {
                                                    items: [],
                                                  },
                                                  locale: 'en-CA',
                                                  fullUrl: '',
                                                },
                                              },
                                            ],
                                          },
                                        },
                                        {
                                          sys: {
                                            id: '2J941LKoHqSJjnmWHXxD9A',
                                          },
                                          __typename: 'ComponentLayoutColumn',
                                          layoutItemCollection: {
                                            items: [
                                              {
                                                __typename: 'FormField',
                                                sys: {
                                                  id: '1gmHh4HnWeI6PjFohXcBtj',
                                                },
                                                formField: {
                                                  __typename: 'FormField',
                                                  altusLabel: 'Job Title',
                                                  altusFieldName: 'jobtitle',
                                                  altusFieldType: 'Text',
                                                  altusDataFormat: 'Text',
                                                  altusIsRequired: true,
                                                  validationErrorMessage:
                                                    'This is a required field',
                                                  placeholderText: 'Owner',
                                                  altusDependentsCollection: {
                                                    items: [],
                                                  },
                                                  locale: 'en-CA',
                                                  fullUrl: '',
                                                },
                                              },
                                            ],
                                          },
                                        },
                                      ],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                                {
                                  sys: {
                                    id: '29FTOwfK0VrCbcybfVhAya',
                                  },
                                  __typename: 'FormField',
                                  formField: {
                                    __typename: 'FormField',
                                    altusLabel: 'Company',
                                    altusFieldName: 'company',
                                    altusFieldType: 'Text',
                                    altusDataFormat: 'Text',
                                    altusIsRequired: true,
                                    validationErrorMessage:
                                      'This field is required',
                                    placeholderText: 'Smith Development',
                                    altusDependentsCollection: {
                                      items: [],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                                {
                                  sys: {
                                    id: '6GMYoxyxGo7BLbNA6vKIWU',
                                  },
                                  __typename: 'FormField',
                                  formField: {
                                    __typename: 'FormField',
                                    altusLabel: 'How can we help you today?',
                                    altusFieldName: 'comments',
                                    altusFieldType: 'Text Area',
                                    altusDataFormat: 'Text',
                                    altusIsRequired: true,
                                    validationErrorMessage:
                                      'This is a required field',
                                    altusDependentsCollection: {
                                      items: [],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                                {
                                  sys: {
                                    id: '2blxbSSnxadz5LYDrKKYBJ',
                                  },
                                  __typename: 'FormField',
                                  formField: {
                                    __typename: 'FormField',
                                    altusLabel:
                                      'Yes! Sign me up to receive the insights newsletter',
                                    altusFieldName: 'newslettersignup',
                                    altusFieldType: 'CheckBox',
                                    altusDataFormat: 'Text',
                                    altusFieldValues: ['True'],
                                    altusIsRequired: false,
                                    userFieldLabels: ['Yes'],
                                    altusDependentsCollection: {
                                      items: [],
                                    },
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                            footer: {
                              sys: {
                                id: '503okjNmlg1Q2j2XyBdEhW',
                              },
                              __typename: 'ComponentRichtext',
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        text: 'By clicking submit, you consent to Altus Group Limited (including any of its affiliates and subsidiaries) contacting you by email and phone (to your email address and phone number listed on this form) with information about industry insights, market reports, upcoming events, and our products and services. Your consent may be withdrawn at any time, including by unsubscribing. Contact us if you have any questions, by visiting ',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '#757575',
                                            },
                                          },
                                          {
                                            type: 'altusText',
                                            attrs: {
                                              class: 'fs2',
                                            },
                                          },
                                        ],
                                      },
                                      {
                                        text: 'here',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'link',
                                            attrs: {
                                              href: '',
                                              class: 'link',
                                              entity: {
                                                sys: {
                                                  id: '2IjycTLKNL93KTwEW2Rbta',
                                                  type: 'Link',
                                                  linkType: 'Entry',
                                                },
                                                slug: {
                                                  'de-DE': 'home',
                                                  'en-CA': 'contact-us',
                                                  'fr-CA': 'contact-us',
                                                },
                                              },
                                              target: '_self',
                                              slug: '/contact-us/',
                                            },
                                          },
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '#0028d7',
                                            },
                                          },
                                          {
                                            type: 'altusText',
                                            attrs: {
                                              class: 'fs2',
                                            },
                                          },
                                        ],
                                      },
                                      {
                                        text: '. Please note: consent is not a condition of purchasing any of our products or services. Further details of our processing are in the ',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '#757575',
                                            },
                                          },
                                          {
                                            type: 'altusText',
                                            attrs: {
                                              class: 'fs2',
                                            },
                                          },
                                        ],
                                      },
                                      {
                                        text: 'Altus Group Privacy Notice',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'link',
                                            attrs: {
                                              href: '',
                                              class: 'link',
                                              entity: {
                                                sys: {
                                                  id: '3WWkiN8eoEmid9GrArFUzA',
                                                  type: 'Link',
                                                  linkType: 'Entry',
                                                },
                                                slug: {
                                                  'de-DE': 'home',
                                                  'en-CA':
                                                    'legal/privacy-policy',
                                                  'fr-CA':
                                                    'legal/privacy-policy',
                                                },
                                              },
                                              target: '_self',
                                              slug: '/legal/privacy-policy/',
                                            },
                                          },
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '#0028d7',
                                            },
                                          },
                                          {
                                            type: 'altusText',
                                            attrs: {
                                              class: 'fs2',
                                            },
                                          },
                                        ],
                                      },
                                      {
                                        text: '.',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: '#757575',
                                            },
                                          },
                                          {
                                            type: 'altusText',
                                            attrs: {
                                              class: 'fs2',
                                            },
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              isFullXBleed: false,
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '12ZT1tVmAE9uD6mHxDW7Gi',
        },
        __typename: 'ComponentLogoShowcase',
        template: 'Static Full Color',
        isLightMode: true,
        logoDataCollection: {
          items: [
            {
              sys: {
                id: '4Qn25xYFCvv0I6oFG5BnH6',
              },
              __typename: 'ComponentLogo',
              template: 'Brand Logo',
              maxWidth: '150',
              maxHeight: '100',
              logo: {
                url: 'https://images.ctfassets.net/8jgyidtgyr4v/7LgOTzs8nczOHuGH3pJje/42258948df068cd4ab4df102d4f89976/Aberdeen_Grayscale.svg',
              },
            },
            {
              sys: {
                id: '3N2waVeDUmWUmOr9JGwhtR',
              },
              __typename: 'ComponentLogo',
              template: 'Brand Logo',
              maxWidth: '150',
              maxHeight: '100',
              logo: {
                url: 'https://images.ctfassets.net/8jgyidtgyr4v/6xN81OXVqmVNZDiaAqomRy/d47bd3cb90b14778a52146540a1f71ac/Allianz_Greyscale.svg',
              },
            },
            {
              sys: {
                id: '1tmKduYGb62pUVykgSkuUK',
              },
              __typename: 'ComponentLogo',
              template: 'Brand Logo',
              maxWidth: '150',
              maxHeight: '100',
              logo: {
                url: 'https://images.ctfassets.net/8jgyidtgyr4v/3Ah7MkvnasqSqR6qIUJ0n6/06879abbf04c86a7c16fb9058cc64f8c/Colliers_Greyscale.svg',
              },
            },
            {
              sys: {
                id: '1tr7CU7F4BzmlgMGKKSJDw',
              },
              __typename: 'ComponentLogo',
              template: 'Brand Logo',
              maxWidth: '150',
              maxHeight: '100',
              logo: {
                url: 'https://images.ctfassets.net/8jgyidtgyr4v/1q3OfkJceTvWK7DIZGZ4Dj/55eb0d5244be415f5554c54ce31884eb/GIC_Greyscale.svg',
              },
            },
            {
              sys: {
                id: '5GBPPyqHcTzx0o9OZIaUyK',
              },
              __typename: 'ComponentLogo',
              template: 'Brand Logo',
              maxWidth: '150',
              maxHeight: '100',
              logo: {
                url: 'https://images.ctfassets.net/8jgyidtgyr4v/48ofZZEUxLRdJpDoAH6yUR/04ca5513218a9ade29856cf3b3fecab4/M_G_Real_Estate_-_Greyscale.svg',
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '4e1tnElS5OPcYkFNtEs8yb',
        },
        __typename: 'ComponentCallout',
        internalName: 'AGL - ARGUS Software - Stats',
        template: 'CalloutStats',
        isLightMode: false,
        statsCardCollection: {
          items: [
            {
              description: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    attrs: {
                      textAlign: 'left',
                    },
                    content: [
                      {
                        text: 'years developing our ARGUS software',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
              statsText: '30',
              symbolSuffix: '+',
            },
            {
              description: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    attrs: {
                      textAlign: 'left',
                    },
                    content: [
                      {
                        text: 'countries across the globe using ARGUS',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
              statsText: '100',
              symbolSuffix: '+',
            },
            {
              description: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    attrs: {
                      textAlign: 'left',
                    },
                    content: [
                      {
                        text: 'universities and colleges teaching ARGUS',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
              statsText: '200',
              symbolSuffix: '+',
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '1pAkZIwk7Vzo7w4iaJGpT5',
        },
        __typename: 'ComponentImage',
        internalName: 'AGL - ARGUS Enterprise - Software Background',
        htmlAttr: {
          className: 'bleedf mt-4 bp1',
        },
        maxHeight: '400px',
        imageFile: {
          url: 'https://images.ctfassets.net/8jgyidtgyr4v/gdZB1miCaPtfB0hH0YmIZ/f4a324645370aa6f77ae358ab23aff3b/Solutions-ARGUSEnterprise-Stats-1920x400.png',
          height: 400,
          width: 1920,
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '3Ati1ugdWjELKAp6wJKlNJ',
        },
        __typename: 'ComponentTabBox',
        htmlAttr: {
          id: 'keyfeatures',
        },
        internalName: 'AGL - Solutions - ARGUS Enterprise - Key Features',
        subHeading: 'Key features',
        heading:
          'Uncover deeper insight into your asset and portfolio performance with ARGUS Enterprise',
        tabItemsCollection: {
          items: [
            {
              sys: {
                id: '1rlZO9s48nO9w1yeWPkaSf',
              },
              tabHeading: 'Property valuations',
              tabComponentCollection: {
                items: [
                  {
                    __typename: 'ComponentLayoutRow',
                    sys: {
                      id: '6tf8ydcLRPTK0uMB1dHy0R',
                    },
                    componentLayoutRow: {
                      sys: {
                        id: '6tf8ydcLRPTK0uMB1dHy0R',
                      },
                      __typename: 'ComponentLayoutRow',
                      internalName:
                        'AGL - Solutions - ARGUS Enterprise - Key Features - Property Valuations',
                      isFullXBleed: false,
                      layoutColumnCollection: {
                        items: [
                          {
                            sys: {
                              id: '4GabLZjOY2TQdVYnzLQkdP',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'ComponentRichtext',
                                  sys: {
                                    id: '5C0DVWp4vF0QQdULfI38i0',
                                  },
                                  componentRichtext: {
                                    sys: {
                                      id: '5C0DVWp4vF0QQdULfI38i0',
                                    },
                                    __typename: 'ComponentRichtext',
                                    content: {
                                      type: 'doc',
                                      content: [
                                        {
                                          type: 'heading',
                                          attrs: {
                                            level: 3,
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Commercial property valuations and cash flow forecasting',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'ARGUS Enterprise revolutionizes the process of commercial property valuation by offering intricate cash flow forecasts. This feature enables modeling of changes in expenses, revenues, and capital expenditures on a lease-by-lease basis.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'It also facilitates the application of multiple valuation methods, including Discounted Cash Flow (DCF), Trad Val, and other global techniques, ensuring precise and comprehensive property valuations.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              type: 'reactInlineComponent',
                                              attrs: {
                                                sys: {
                                                  id: '3FydpO4CvYf26bj7GgHilR',
                                                  type: 'InlineEntry',
                                                  linkType: 'Entry',
                                                  typeName: 'linkComponent',
                                                  linkComponent: {
                                                    sys: {
                                                      id: '3FydpO4CvYf26bj7GgHilR',
                                                    },
                                                    __typename: 'LinkComponent',
                                                    internalName:
                                                      'AGL - CTA Form - ARGUS Enterprise - Primary',
                                                    template: 'Primary',
                                                    text: 'Book a free demo',
                                                    sectionId: '#overview',
                                                    icon: 'RightChevron',
                                                    iconPlacement: 'Suffix',
                                                    isLightMode: true,
                                                    isChevron2Arrow: true,
                                                    openInNewTab: false,
                                                    locale: 'en-CA',
                                                    fullUrl: '',
                                                  },
                                                },
                                              },
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                    isFullXBleed: false,
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                          {
                            sys: {
                              id: '5TXqIiqFuW7nVRYZwC29Ks',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'Player',
                                  sys: {
                                    id: '2KFs0cfmHx8mvulSJ1NuZp',
                                  },
                                  player: {
                                    __typename: 'Player',
                                    contentId: 'ipaKBl7EFtU',
                                    template: 'YouTube',
                                    internalName:
                                      'AGL - Solutions - ARGUS Enterprise - Valuations & Cashflow',
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '5iwPYoo2RuKylFJ45KdN3W',
              },
              tabHeading: 'Scenario testing',
              tabComponentCollection: {
                items: [
                  {
                    __typename: 'ComponentLayoutRow',
                    sys: {
                      id: '6TftKBbuuYwXidXydVAK3l',
                    },
                    componentLayoutRow: {
                      sys: {
                        id: '6TftKBbuuYwXidXydVAK3l',
                      },
                      __typename: 'ComponentLayoutRow',
                      internalName:
                        'AGL - Solutions - ARGUS Enterprise - Key Features - Scenario Testing',
                      isFullXBleed: false,
                      layoutColumnCollection: {
                        items: [
                          {
                            sys: {
                              id: '3jt6WsTtWp0jUe1G4ZjVbp',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'ComponentRichtext',
                                  sys: {
                                    id: '7LCaESdDHgDVYLwnnkUthj',
                                  },
                                  componentRichtext: {
                                    sys: {
                                      id: '7LCaESdDHgDVYLwnnkUthj',
                                    },
                                    __typename: 'ComponentRichtext',
                                    content: {
                                      type: 'doc',
                                      content: [
                                        {
                                          type: 'heading',
                                          attrs: {
                                            level: 3,
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Sensitivity analysis and scenario testing',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'ARGUS Enterprise stands out by enabling detailed what-if analysis for individual properties or entire portfolios.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'By comparing base and adjusted scenarios, users can assess the true impact of changes on property and portfolio KPIs. This feature empowers the exploration of best and worst-case scenarios, allowing for informed decision-making amidst varying market conditions.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'reactBlockComponent',
                                          attrs: {
                                            sys: {
                                              id: '3FydpO4CvYf26bj7GgHilR',
                                              type: 'BlockEntry',
                                              linkType: 'Entry',
                                              typeName: 'linkComponent',
                                              linkComponent: {
                                                sys: {
                                                  id: '3FydpO4CvYf26bj7GgHilR',
                                                },
                                                __typename: 'LinkComponent',
                                                internalName:
                                                  'AGL - CTA Form - ARGUS Enterprise - Primary',
                                                template: 'Primary',
                                                text: 'Book a free demo',
                                                sectionId: '#overview',
                                                icon: 'RightChevron',
                                                iconPlacement: 'Suffix',
                                                isLightMode: true,
                                                isChevron2Arrow: true,
                                                openInNewTab: false,
                                                locale: 'en-CA',
                                                fullUrl: '',
                                              },
                                            },
                                          },
                                        },
                                      ],
                                    },
                                    isFullXBleed: false,
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                          {
                            sys: {
                              id: '1fADFiAcKY9Jiqlqt5xTgB',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'Player',
                                  sys: {
                                    id: '3sCV8yyGMszJbDZabo4rcl',
                                  },
                                  player: {
                                    __typename: 'Player',
                                    contentId: 'K6Uzym3uyLc',
                                    template: 'YouTube',
                                    internalName:
                                      'AGL - Solutions- ARGUS Enterprise - Sensitivity Analysis',
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '2T8RtodzyNuGhrlQZbNDzK',
              },
              tabHeading: 'Transaction analysis',
              tabComponentCollection: {
                items: [
                  {
                    __typename: 'ComponentLayoutRow',
                    sys: {
                      id: '6xPAm2H2HS0NvD9TQAmyWh',
                    },
                    componentLayoutRow: {
                      sys: {
                        id: '6xPAm2H2HS0NvD9TQAmyWh',
                      },
                      __typename: 'ComponentLayoutRow',
                      internalName:
                        'AGL - Solutions - ARGUS Enterprise - Key Features - Transaction Analysis',
                      isFullXBleed: false,
                      layoutColumnCollection: {
                        items: [
                          {
                            sys: {
                              id: '5grGozhri6LArGBH8Ckwv5',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'ComponentRichtext',
                                  sys: {
                                    id: 'glftdsBKtnOOJjzx7I8sM',
                                  },
                                  componentRichtext: {
                                    sys: {
                                      id: 'glftdsBKtnOOJjzx7I8sM',
                                    },
                                    __typename: 'ComponentRichtext',
                                    content: {
                                      type: 'doc',
                                      content: [
                                        {
                                          type: 'heading',
                                          attrs: {
                                            level: 3,
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Transaction and investment analysis',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'ARGUS Enterprise facilitates the creation of robust returns models using an array property data and market assumptions. ',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Importing data from sellers, overlaying in-house assumptions, and utilizing our scenario tools to determine the best and worse case, and understand if a potential property meets your investment criteria. ',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'reactBlockComponent',
                                          attrs: {
                                            sys: {
                                              id: '3FydpO4CvYf26bj7GgHilR',
                                              type: 'BlockEntry',
                                              linkType: 'Entry',
                                              typeName: 'linkComponent',
                                              linkComponent: {
                                                sys: {
                                                  id: '3FydpO4CvYf26bj7GgHilR',
                                                },
                                                __typename: 'LinkComponent',
                                                internalName:
                                                  'AGL - CTA Form - ARGUS Enterprise - Primary',
                                                template: 'Primary',
                                                text: 'Book a free demo',
                                                sectionId: '#overview',
                                                icon: 'RightChevron',
                                                iconPlacement: 'Suffix',
                                                isLightMode: true,
                                                isChevron2Arrow: true,
                                                openInNewTab: false,
                                                locale: 'en-CA',
                                                fullUrl: '',
                                              },
                                            },
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                      ],
                                    },
                                    isFullXBleed: false,
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                          {
                            sys: {
                              id: '1f3NUVcYa524Of1RzmCjoL',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'Player',
                                  sys: {
                                    id: '1ipCPHOllLS4iKj97VusRx',
                                  },
                                  player: {
                                    __typename: 'Player',
                                    contentId: 'C1dXDiTCwC8',
                                    template: 'YouTube',
                                    internalName:
                                      'AGL - Solutions - ARGUS Enterprise - Transaction & Investment Analysis',
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '6vyItkP1gLVcnID4F9fXMw',
              },
              tabHeading: 'Asset budgeting',
              tabComponentCollection: {
                items: [
                  {
                    __typename: 'ComponentLayoutRow',
                    sys: {
                      id: '5wUApBQtNE5qIv1aQCN8Uk',
                    },
                    componentLayoutRow: {
                      sys: {
                        id: '5wUApBQtNE5qIv1aQCN8Uk',
                      },
                      __typename: 'ComponentLayoutRow',
                      internalName:
                        'AGL - Solutions - ARGUS Enterprise - Key Features - Asset Budgeting',
                      isFullXBleed: false,
                      layoutColumnCollection: {
                        items: [
                          {
                            sys: {
                              id: '5SQonbLEMUt2miMK0FZE9v',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'ComponentRichtext',
                                  sys: {
                                    id: '7pjIRuPeBIWL8jmzTEkqgW',
                                  },
                                  componentRichtext: {
                                    sys: {
                                      id: '7pjIRuPeBIWL8jmzTEkqgW',
                                    },
                                    __typename: 'ComponentRichtext',
                                    content: {
                                      type: 'doc',
                                      content: [
                                        {
                                          type: 'heading',
                                          attrs: {
                                            level: 3,
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Asset budgeting and forecasting',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'ARGUS Enterprise connects in real-time with third party accounting and property management solutions. By integrating existing systems, it enables the modeling of key operating expenses and revenue streams.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'This functionality allows users to easily compare monthly budgets against actuals, identify variances, conduct scenario modeling, and reforecast budgets efficiently.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'reactBlockComponent',
                                          attrs: {
                                            sys: {
                                              id: '3FydpO4CvYf26bj7GgHilR',
                                              type: 'BlockEntry',
                                              linkType: 'Entry',
                                              typeName: 'linkComponent',
                                              linkComponent: {
                                                sys: {
                                                  id: '3FydpO4CvYf26bj7GgHilR',
                                                },
                                                __typename: 'LinkComponent',
                                                internalName:
                                                  'AGL - CTA Form - ARGUS Enterprise - Primary',
                                                template: 'Primary',
                                                text: 'Book a free demo',
                                                sectionId: '#overview',
                                                icon: 'RightChevron',
                                                iconPlacement: 'Suffix',
                                                isLightMode: true,
                                                isChevron2Arrow: true,
                                                openInNewTab: false,
                                                locale: 'en-CA',
                                                fullUrl: '',
                                              },
                                            },
                                          },
                                        },
                                      ],
                                    },
                                    isFullXBleed: false,
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                          {
                            sys: {
                              id: '1RVkcIyEXyQYcz3R4lY62r',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'Player',
                                  sys: {
                                    id: '6hcXnTbW9jHdD8IjXQYYA4',
                                  },
                                  player: {
                                    __typename: 'Player',
                                    contentId: 'iZ4V8JMQwIQ',
                                    template: 'YouTube',
                                    internalName:
                                      'AGL - Solutions - ARGUS Enterprise - Asset Budgeting',
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '47shqObnwxZiSHs108VWeT',
              },
              tabHeading: 'Integrations',
              tabComponentCollection: {
                items: [
                  {
                    __typename: 'ComponentLayoutRow',
                    sys: {
                      id: '4nKHH8yiBS4HmVgxfAxqfd',
                    },
                    componentLayoutRow: {
                      sys: {
                        id: '4nKHH8yiBS4HmVgxfAxqfd',
                      },
                      __typename: 'ComponentLayoutRow',
                      internalName:
                        'AGL - Solutions - ARGUS Enterprise - Key Features - Integrations',
                      isFullXBleed: false,
                      layoutColumnCollection: {
                        items: [
                          {
                            sys: {
                              id: '2lMglJZeicpuxC3rNbkyWB',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'ComponentRichtext',
                                  sys: {
                                    id: '5SozdQWSPGWMRUCKp0MSoL',
                                  },
                                  componentRichtext: {
                                    sys: {
                                      id: '5SozdQWSPGWMRUCKp0MSoL',
                                    },
                                    __typename: 'ComponentRichtext',
                                    content: {
                                      type: 'doc',
                                      content: [
                                        {
                                          type: 'heading',
                                          attrs: {
                                            level: 3,
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Integration with third party systems',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'Most real estate professionals still use multiple spreadsheets and solutions to manage commercial real estate. ARGUS Enterprise easily connects and imports your data from existing spreadsheets and other property management and accounting systems.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                          content: [
                                            {
                                              text: 'From here, you can leverage the powerful ARGUS calculation engine to run cash flow analysis, forecasting, portfolio analysis and reporting. Taking the weight off your shoulders and keeping everyone together.',
                                              type: 'text',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'paragraph',
                                          attrs: {
                                            textAlign: 'left',
                                          },
                                        },
                                        {
                                          type: 'reactBlockComponent',
                                          attrs: {
                                            sys: {
                                              id: '3FydpO4CvYf26bj7GgHilR',
                                              type: 'BlockEntry',
                                              linkType: 'Entry',
                                              typeName: 'linkComponent',
                                              linkComponent: {
                                                sys: {
                                                  id: '3FydpO4CvYf26bj7GgHilR',
                                                },
                                                __typename: 'LinkComponent',
                                                internalName:
                                                  'AGL - CTA Form - ARGUS Enterprise - Primary',
                                                template: 'Primary',
                                                text: 'Book a free demo',
                                                sectionId: '#overview',
                                                icon: 'RightChevron',
                                                iconPlacement: 'Suffix',
                                                isLightMode: true,
                                                isChevron2Arrow: true,
                                                openInNewTab: false,
                                                locale: 'en-CA',
                                                fullUrl: '',
                                              },
                                            },
                                          },
                                        },
                                      ],
                                    },
                                    isFullXBleed: false,
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                          {
                            sys: {
                              id: '1aWBwqH9OPIgO5HY2q4otK',
                            },
                            __typename: 'ComponentLayoutColumn',
                            layoutItemCollection: {
                              items: [
                                {
                                  __typename: 'Player',
                                  sys: {
                                    id: '66ByOvQXUT6KZYyaB9QRMi',
                                  },
                                  player: {
                                    __typename: 'Player',
                                    contentId: 'zaiaxV9FZQc',
                                    template: 'YouTube',
                                    internalName:
                                      'AGL - Solutions - ARGUS Enterprise - Integrations',
                                    locale: 'en-CA',
                                    fullUrl: '',
                                  },
                                },
                              ],
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '2XkcEYSL4wBuHhhIF0nm4m',
        },
        __typename: 'ComponentImage',
        internalName: 'AGL - Solutions - Why Us - Background',
        htmlAttr: {
          className: 'bleedf',
        },
        maxHeight: '400px',
        imageFile: {
          url: 'https://images.ctfassets.net/8jgyidtgyr4v/UcTdtUQxDIB3RxyvR1An3/1909f599db20180388faa2351aff3b6d/Solutions-SoftwarePages-WhyAltus-913219882.jpg',
          height: 1080,
          width: 1920,
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '601TYCWwgoIpHD4bONbRTR',
        },
        __typename: 'ComponentLayoutRow',
        internalName: 'AGL - ARGUS Enterprise - Why ARGUS',
        htmlAttr: {
          id: 'whyaltus',
        },
        isFullXBleed: false,
        layoutColumnCollection: {
          items: [
            {
              sys: {
                id: '5A0WYEjFrBD4QGS12qCQHf',
              },
              __typename: 'ComponentLayoutColumn',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentRichtext',
                    sys: {
                      id: '1HWgm78uTpdvLjwIioaXLZ',
                    },
                    componentRichtext: {
                      sys: {
                        id: '1HWgm78uTpdvLjwIioaXLZ',
                      },
                      __typename: 'ComponentRichtext',
                      content: {
                        type: 'doc',
                        content: [
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                text: 'Why ALTUS',
                                type: 'text',
                                marks: [
                                  {
                                    type: 'textStyle',
                                    attrs: {
                                      color: '#0028d7',
                                    },
                                  },
                                  {
                                    type: 'altusText',
                                    attrs: {
                                      class: 'subheading',
                                    },
                                  },
                                ],
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'heading',
                            attrs: {
                              level: 2,
                              textAlign: 'left',
                            },
                            content: [
                              {
                                text: '30+ years of pioneering CRE software, trusted worldwide',
                                type: 'text',
                                marks: [
                                  {
                                    type: 'textStyle',
                                    attrs: {
                                      color: 'rgb(0, 0, 0)',
                                    },
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      isFullXBleed: false,
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '4RdOJ0pn2P6IvYK0Cy5yp6',
              },
              __typename: 'ComponentLayoutColumn',
              width: '50%',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentRichtext',
                    sys: {
                      id: '6Wnj6pqwp54K65nAlqAMu',
                    },
                    componentRichtext: {
                      sys: {
                        id: '6Wnj6pqwp54K65nAlqAMu',
                      },
                      __typename: 'ComponentRichtext',
                      content: {
                        type: 'doc',
                        content: [
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                type: 'text',
                                text: 'ARGUS Enterprise represents the best of our three software solutions -  ARGUS Valuation DCF, Valuation Capitalisation and DYNA Asset Management, in one complete package.',
                              },
                              {
                                type: 'hardBreak',
                              },
                              {
                                type: 'hardBreak',
                              },
                              {
                                type: 'text',
                                text: 'With one centralized solution, build accurate cash flow forecasts, run stress testing on market and leasing assumptions, create comprehensive commercial property valuations, and more.',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'reactBlockComponent',
                            attrs: {
                              sys: {
                                id: '3FydpO4CvYf26bj7GgHilR',
                                type: 'BlockEntry',
                                linkType: 'Entry',
                                typeName: 'linkComponent',
                                linkComponent: {
                                  sys: {
                                    id: '3FydpO4CvYf26bj7GgHilR',
                                  },
                                  __typename: 'LinkComponent',
                                  internalName:
                                    'AGL - CTA Form - ARGUS Enterprise - Primary',
                                  template: 'Primary',
                                  text: 'Book a free demo',
                                  sectionId: '#overview',
                                  icon: 'RightChevron',
                                  iconPlacement: 'Suffix',
                                  isLightMode: true,
                                  isChevron2Arrow: true,
                                  openInNewTab: false,
                                  locale: 'en-CA',
                                  fullUrl: '',
                                },
                              },
                            },
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                        ],
                      },
                      isFullXBleed: false,
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '1CdoOXXR5h7iQ6c1hnO2rl',
        },
        __typename: 'ComponentLayoutContainer',
        internalName: 'AGL - ARGUS Enterprise - Why Altus',
        numberOfColumns: '3',
        isFullXBleed: false,
        layoutContainerItemsCollection: {
          items: [
            {
              __typename: 'CardComponent',
              sys: {
                id: '7Knqavxm3RIPI8cjbX5jYl',
              },
              cardComponent: {
                sys: {
                  id: '7Knqavxm3RIPI8cjbX5jYl',
                },
                __typename: 'CardComponent',
                internalName:
                  'AGL - Solutions - ARGUS Enterprise - Why - Transparency',
                template: 'CardImage',
                isLightMode: true,
                heading: 'Greater transparency',
                description: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      attrs: {
                        textAlign: 'left',
                      },
                      content: [
                        {
                          text: 'Store multiple portfolios, and thousands of properties in one place, saving even more time on analysis and reporting.',
                          type: 'text',
                        },
                      ],
                    },
                  ],
                },
                cardsCollection: {
                  items: [],
                },
                buttonGroupCollection: {
                  items: [],
                },
                alignment: 'Image Top',
                size: 'medium',
                htmlAttributes: {
                  className: 'p1 shadow3',
                },
                image: {
                  title: 'Icon - General - Greater Transparency',
                  url: 'https://images.ctfassets.net/8jgyidtgyr4v/5rW6vxh30r3D97xdFeLS9J/f975d82dcf99d76669c3dd869205f97e/Greater_Transparency_-_P2-S3_Gradient.svg',
                },
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'CardComponent',
              sys: {
                id: '6i8txAtFZ4pYK5bjrYuFYk',
              },
              cardComponent: {
                sys: {
                  id: '6i8txAtFZ4pYK5bjrYuFYk',
                },
                __typename: 'CardComponent',
                internalName:
                  'AGL - Solutions - ARGUS Enterprise - Why - Insights',
                template: 'CardImage',
                isLightMode: true,
                heading: 'Deeper intelligence',
                description: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      attrs: {
                        textAlign: 'left',
                      },
                      content: [
                        {
                          text: 'Advanced analytics to stress test how your commercial properties will perform in different market conditions.',
                          type: 'text',
                        },
                      ],
                    },
                  ],
                },
                cardsCollection: {
                  items: [],
                },
                buttonGroupCollection: {
                  items: [],
                },
                alignment: 'Image Top',
                size: 'medium',
                htmlAttributes: {
                  className: 'p1 shadow3',
                },
                image: {
                  title: 'Icon - General - Insights Intelligence',
                  url: 'https://images.ctfassets.net/8jgyidtgyr4v/7BMukXBF6DmRE4dIgFjjhr/6e58dda390f6c5d676f9781b043f39ab/Insights_Intelligence_-_P2-S3_Gradient.svg',
                },
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'CardComponent',
              sys: {
                id: 'leAURZBzz5itHKnyQdnua',
              },
              cardComponent: {
                sys: {
                  id: 'leAURZBzz5itHKnyQdnua',
                },
                __typename: 'CardComponent',
                internalName:
                  'AGL - Solutions - ARGUS Enterprise - Why - Accuracy',
                template: 'CardImage',
                isLightMode: true,
                heading: 'Increased accuracy',
                description: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      attrs: {
                        textAlign: 'left',
                      },
                      content: [
                        {
                          text: 'Connect third party property management and accounting systems for real-time data and see its direct impact on your cash flow.',
                          type: 'text',
                          marks: [
                            {
                              type: 'textStyle',
                              attrs: {
                                color: '',
                              },
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
                cardsCollection: {
                  items: [],
                },
                buttonGroupCollection: {
                  items: [],
                },
                alignment: 'Image Top',
                size: 'medium',
                htmlAttributes: {
                  className: 'p1 shadow3',
                },
                image: {
                  title: 'Icon - General - Data Accuracy',
                  url: 'https://images.ctfassets.net/8jgyidtgyr4v/QeUR1w5Wo9pyojQEqmNhE/370dc50f0418de2c5580205b069a350b/Data_Accuracy_-_P2-S3_Gradient.svg',
                },
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'CardComponent',
              sys: {
                id: '1rK3CShLFNAy3QCbV8xubJ',
              },
              cardComponent: {
                sys: {
                  id: '1rK3CShLFNAy3QCbV8xubJ',
                },
                __typename: 'CardComponent',
                internalName:
                  'AGL - Solutions - ARGUS Enterprise - Why - Calculation Engine',
                template: 'CardImage',
                isLightMode: true,
                heading: 'Robust calculations',
                description: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      attrs: {
                        textAlign: 'left',
                      },
                      content: [
                        {
                          text: 'Run detailed cash flow models, apply changes over time and create forecasts to see direct impact on your cash flow.',
                          type: 'text',
                        },
                      ],
                    },
                  ],
                },
                cardsCollection: {
                  items: [],
                },
                buttonGroupCollection: {
                  items: [],
                },
                alignment: 'Image Top',
                size: 'medium',
                htmlAttributes: {
                  className: 'p1 shadow3',
                },
                image: {
                  title: 'Icon - General - Calculation Engine',
                  url: 'https://images.ctfassets.net/8jgyidtgyr4v/3kk32EDLowvL4DBtk0UewF/5a5e3971c10ad38507ebf28c7ce04f24/Calculation_Engine_-_P2-S3_Gradient.svg',
                },
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'CardComponent',
              sys: {
                id: 'OesUDEADw6gS35y0iJXXU',
              },
              cardComponent: {
                sys: {
                  id: 'OesUDEADw6gS35y0iJXXU',
                },
                __typename: 'CardComponent',
                internalName:
                  'AGL - Solutions - ARGUS Enterprise - Why - Data Integrity',
                template: 'CardImage',
                isLightMode: true,
                heading: 'Data integrity',
                description: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      attrs: {
                        textAlign: 'left',
                      },
                      content: [
                        {
                          type: 'text',
                          marks: [
                            {
                              type: 'textStyle',
                              attrs: {
                                color: '',
                              },
                            },
                          ],
                          text: 'Safeguard your asset data and guarantee the integrity of your calculations with our powerful calculation engine.',
                        },
                      ],
                    },
                  ],
                },
                cardsCollection: {
                  items: [],
                },
                buttonGroupCollection: {
                  items: [],
                },
                alignment: 'Image Top',
                size: 'medium',
                htmlAttributes: {
                  className: 'p1 shadow3',
                },
                image: {
                  title: 'Icon - General - Data Integrity',
                  url: 'https://images.ctfassets.net/8jgyidtgyr4v/1jSAtXu1JqRtapFKAVoI1r/fc7bb626c688f803db4479d38fcec2d3/Data_Integrity_-_P2-S3_Gradient.svg',
                },
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'CardComponent',
              sys: {
                id: '10Lu0T4d3MQEHOtMcVdKng',
              },
              cardComponent: {
                sys: {
                  id: '10Lu0T4d3MQEHOtMcVdKng',
                },
                __typename: 'CardComponent',
                internalName:
                  'AGL - Solutions - ARGUS Enterprise - Why - Global Standard',
                template: 'CardImage',
                isLightMode: true,
                heading: 'Global standard ',
                description: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      attrs: {
                        textAlign: 'left',
                      },
                      content: [
                        {
                          type: 'text',
                          marks: [
                            {
                              type: 'textStyle',
                              attrs: {
                                color: '',
                              },
                            },
                          ],
                          text: 'Run global valuation methods including discounted cash flow (DCF), income capitalization and traditional valuation.',
                        },
                      ],
                    },
                  ],
                },
                cardsCollection: {
                  items: [],
                },
                buttonGroupCollection: {
                  items: [],
                },
                alignment: 'Image Top',
                size: 'medium',
                htmlAttributes: {
                  className: 'p1 shadow3',
                },
                image: {
                  title: 'Icon - General - Industry Global Standard',
                  url: 'https://images.ctfassets.net/8jgyidtgyr4v/48WXzoOtfaPhlKLemUhbTS/122f405a4012d2e295477661eb4c149d/Industry-Global_Standards_-_P2-S3_Gradient.svg',
                },
                locale: 'en-CA',
                fullUrl: '',
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '5Ppc8j1n8mqrKYuGNYnXNW',
        },
        __typename: 'ComponentImage',
        internalName: 'AGL - Solutions - Training',
        htmlAttr: {
          className: 'bleedf py0',
        },
        maxHeight: '400px',
        imageFile: {
          url: 'https://images.ctfassets.net/8jgyidtgyr4v/5vtlKGXGMmBxigrOaADDgR/94702a80afa9c041da2e69477ad17295/MSA_-_Solutions_-_Why_Altus_-_Training.png',
          height: 1303,
          width: 1961,
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '58VFTUSxxUXX9PItIpzvA5',
        },
        __typename: 'ComponentLayoutRow',
        internalName: 'AGL - Solutions - ARGUS Enterprise - Training',
        htmlAttr: {
          className: 'bn7 py3 mt-4',
          id: 'training',
        },
        isFullXBleed: true,
        layoutColumnCollection: {
          items: [
            {
              sys: {
                id: '5LaTCRNAt7La40rV32WxjN',
              },
              __typename: 'ComponentLayoutColumn',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentRichtext',
                    sys: {
                      id: '2oZm31cScjozdnU94GawqA',
                    },
                    componentRichtext: {
                      sys: {
                        id: '2oZm31cScjozdnU94GawqA',
                      },
                      __typename: 'ComponentRichtext',
                      content: {
                        type: 'doc',
                        content: [
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                type: 'text',
                                marks: [
                                  {
                                    type: 'textStyle',
                                    attrs: {
                                      color: '#0028d7',
                                    },
                                  },
                                  {
                                    type: 'altusText',
                                    attrs: {
                                      class: 'subheading',
                                    },
                                  },
                                ],
                                text: 'Training',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'heading',
                            attrs: {
                              textAlign: 'left',
                              level: 2,
                            },
                            content: [
                              {
                                type: 'text',
                                text: 'ARGUS Enterprise training and certification',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                type: 'text',
                                marks: [
                                  {
                                    type: 'altusText',
                                    attrs: {},
                                  },
                                ],
                                text: 'Build on your commercial real estate knowledge and expertise with our ARGUS Enterprise training and certification courses. ',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                type: 'text',
                                marks: [
                                  {
                                    type: 'textStyle',
                                    attrs: {
                                      color: 'rgb(33, 37, 41)',
                                    },
                                  },
                                ],
                                text: 'We offer private training options that allow you to tailor your training experience to meet your company’s requirements. Choose from our standard training agendas, or build a custom agenda over topics pertinent to your day-to-day operations.',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'reactBlockComponent',
                            attrs: {
                              sys: {
                                id: '66p0b6BlTxaTbnWdTiNBTP',
                                type: 'BlockEntry',
                                linkType: 'Entry',
                                typeName: 'linkComponent',
                                linkComponent: {
                                  sys: {
                                    id: '66p0b6BlTxaTbnWdTiNBTP',
                                  },
                                  __typename: 'LinkComponent',
                                  internalName:
                                    'AGL - CTA Form - ARGUS Private Training - Primary',
                                  template: 'Primary',
                                  text: 'Book private training',
                                  icon: 'RightChevron',
                                  iconPlacement: 'Suffix',
                                  isLightMode: true,
                                  isChevron2Arrow: true,
                                  openInNewTab: false,
                                  actionContent: {
                                    __typename: 'ComponentForm',
                                    sys: {
                                      id: '7EKZHvTa582yNlUh7N3R9T',
                                    },
                                    componentForm: {
                                      sys: {
                                        id: '7EKZHvTa582yNlUh7N3R9T',
                                      },
                                      __typename: 'ComponentForm',
                                      endpointUrl:
                                        'https://go.altusgroup.com/l/575253/2023-05-21/36gpr1',
                                      sfmcUrl:
                                        'https://cloud.hello.altusgroup.com/wxiwnw0smra',
                                      template: 'Popup',
                                      thankYouMessage: {
                                        type: 'doc',
                                        content: [
                                          {
                                            type: 'heading',
                                            attrs: {
                                              level: 3,
                                              textAlign: 'left',
                                            },
                                            content: [
                                              {
                                                text: 'Thank you for your inquiry',
                                                type: 'text',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'paragraph',
                                            attrs: {
                                              textAlign: 'left',
                                            },
                                          },
                                          {
                                            type: 'paragraph',
                                            attrs: {
                                              textAlign: 'left',
                                            },
                                            content: [
                                              {
                                                text: 'An Altus Group representative will be in touch with you shortly. In the meantime, feel free to catch up on our latest CRE articles, guides and market research on our ',
                                                type: 'text',
                                              },
                                              {
                                                text: 'insights page',
                                                type: 'text',
                                                marks: [
                                                  {
                                                    type: 'link',
                                                    attrs: {
                                                      href: '',
                                                      class: 'link',
                                                      entity: {
                                                        sys: {
                                                          id: '18vqQ0c0a1XKexDXSemBna',
                                                          type: 'Link',
                                                          linkType: 'Entry',
                                                        },
                                                        slug: {
                                                          es: 'insights',
                                                          it: 'insights',
                                                          nl: 'insights',
                                                          'de-DE': 'insights',
                                                          'en-CA': 'insights',
                                                          'fr-CA': 'insights',
                                                        },
                                                      },
                                                      target: '_self',
                                                      slug: '/insights/',
                                                    },
                                                  },
                                                ],
                                              },
                                              {
                                                text: '.',
                                                type: 'text',
                                              },
                                            ],
                                          },
                                        ],
                                      },
                                      isStatic: false,
                                      hiddenFieldsCollection: {
                                        items: [
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '6RA38evbAvO68iwcvFoP86',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Business Unit',
                                              altusFieldName: 'businessunit',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['AA'],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '1KaEsaC7DbEV6JSZlT8i9f',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Campaign response',
                                              altusFieldName:
                                                'campaignresponse',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['Responded'],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '66ddO3WS0vFOnh1njK6c8l',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Form Name',
                                              altusFieldName: 'formname',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: [
                                                'Altus Website - ARGUS Private Training',
                                              ],
                                              altusIsRequired: true,
                                              isAltusEmailAllowed: true,
                                              isGenericEmailAllowed: true,
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '5kWdBGtFTsTY7quqC8CXFJ',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Form type',
                                              altusFieldName: 'formtype',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['Solution'],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '2fzed5Tz0IICGqvrD48piK',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Hot Pass',
                                              altusFieldName: 'hotpass',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['True'],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '2G72PH287aJMSVikFG7bE3',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Interest',
                                              altusFieldName: 'interest',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['Software'],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '4sVWCDYimpSJJY68WLvzZt',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel:
                                                'Inquiry Type - Sales',
                                              altusFieldName:
                                                'contactinquirytype',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['Sales'],
                                              altusIsRequired: false,
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '4KJuMjo9x1bCqSwSvLHMPK',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Primary Campaign',
                                              altusFieldName: 'sfid',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: [
                                                '701Tn000005gtIAIAY',
                                              ],
                                              altusIsRequired: true,
                                              isAltusEmailAllowed: true,
                                              isGenericEmailAllowed: true,
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: '2pEDAG5nSwdVF9IYEEjMi2',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Product Interest',
                                              altusFieldName: 'productinterest',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: [
                                                'ARGUS Training',
                                              ],
                                              altusIsRequired: true,
                                              isAltusEmailAllowed: true,
                                              isGenericEmailAllowed: true,
                                              userFieldLabels: [
                                                'ARGUS Training',
                                              ],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            sys: {
                                              id: 'vFcajUQhnuP8X2P6jkgLg',
                                            },
                                            formField: {
                                              __typename: 'FormField',
                                              altusLabel: 'Rating',
                                              altusFieldName: 'rating',
                                              altusFieldType: 'Hidden',
                                              altusDataFormat: 'Text',
                                              altusFieldValues: ['Hot'],
                                              altusDependentsCollection: {
                                                items: [],
                                              },
                                              locale: 'en-CA',
                                              fullUrl: '',
                                            },
                                          },
                                        ],
                                      },
                                      footer: {
                                        sys: {
                                          id: '503okjNmlg1Q2j2XyBdEhW',
                                        },
                                        __typename: 'ComponentRichtext',
                                        content: {
                                          type: 'doc',
                                          content: [
                                            {
                                              type: 'paragraph',
                                              attrs: {
                                                textAlign: 'left',
                                              },
                                              content: [
                                                {
                                                  text: 'By clicking submit, you consent to Altus Group Limited (including any of its affiliates and subsidiaries) contacting you by email and phone (to your email address and phone number listed on this form) with information about industry insights, market reports, upcoming events, and our products and services. Your consent may be withdrawn at any time, including by unsubscribing. Contact us if you have any questions, by visiting ',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '#757575',
                                                      },
                                                    },
                                                    {
                                                      type: 'altusText',
                                                      attrs: {
                                                        class: 'fs2',
                                                      },
                                                    },
                                                  ],
                                                },
                                                {
                                                  text: 'here',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'link',
                                                      attrs: {
                                                        href: '',
                                                        class: 'link',
                                                        entity: {
                                                          sys: {
                                                            id: '2IjycTLKNL93KTwEW2Rbta',
                                                            type: 'Link',
                                                            linkType: 'Entry',
                                                          },
                                                          slug: {
                                                            'de-DE': 'home',
                                                            'en-CA':
                                                              'contact-us',
                                                            'fr-CA':
                                                              'contact-us',
                                                          },
                                                        },
                                                        target: '_self',
                                                        slug: '/contact-us/',
                                                      },
                                                    },
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '#0028d7',
                                                      },
                                                    },
                                                    {
                                                      type: 'altusText',
                                                      attrs: {
                                                        class: 'fs2',
                                                      },
                                                    },
                                                  ],
                                                },
                                                {
                                                  text: '. Please note: consent is not a condition of purchasing any of our products or services. Further details of our processing are in the ',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '#757575',
                                                      },
                                                    },
                                                    {
                                                      type: 'altusText',
                                                      attrs: {
                                                        class: 'fs2',
                                                      },
                                                    },
                                                  ],
                                                },
                                                {
                                                  text: 'Altus Group Privacy Notice',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'link',
                                                      attrs: {
                                                        href: '',
                                                        class: 'link',
                                                        entity: {
                                                          sys: {
                                                            id: '3WWkiN8eoEmid9GrArFUzA',
                                                            type: 'Link',
                                                            linkType: 'Entry',
                                                          },
                                                          slug: {
                                                            'de-DE': 'home',
                                                            'en-CA':
                                                              'legal/privacy-policy',
                                                            'fr-CA':
                                                              'legal/privacy-policy',
                                                          },
                                                        },
                                                        target: '_self',
                                                        slug: '/legal/privacy-policy/',
                                                      },
                                                    },
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '#0028d7',
                                                      },
                                                    },
                                                    {
                                                      type: 'altusText',
                                                      attrs: {
                                                        class: 'fs2',
                                                      },
                                                    },
                                                  ],
                                                },
                                                {
                                                  text: '.',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '#757575',
                                                      },
                                                    },
                                                    {
                                                      type: 'altusText',
                                                      attrs: {
                                                        class: 'fs2',
                                                      },
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                        isFullXBleed: false,
                                      },
                                      loadingMessage: {
                                        sys: {
                                          id: '38zQPSkZRqUI7qea7A4Br',
                                        },
                                        __typename: 'ComponentRichtext',
                                        content: {
                                          type: 'doc',
                                          content: [
                                            {
                                              type: 'paragraph',
                                              attrs: {
                                                textAlign: 'left',
                                              },
                                              content: [
                                                {
                                                  text: 'Hold tight, while we submit your inquiry.',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '',
                                                      },
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                        isFullXBleed: false,
                                      },
                                      successMessage: {
                                        sys: {
                                          id: '15Aey2HVS3sggg6Uns9Nkx',
                                        },
                                        __typename: 'ComponentRichtext',
                                        content: {
                                          type: 'doc',
                                          content: [
                                            {
                                              type: 'paragraph',
                                              attrs: {
                                                textAlign: 'left',
                                              },
                                              content: [
                                                {
                                                  text: 'Your inquiry was successfully submitted.',
                                                  type: 'text',
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                        isFullXBleed: false,
                                      },
                                      errorMessage: {
                                        sys: {
                                          id: '2XPx3F1S6VfbTWmig8fZEb',
                                        },
                                        __typename: 'ComponentRichtext',
                                        content: {
                                          type: 'doc',
                                          content: [
                                            {
                                              type: 'paragraph',
                                              attrs: {
                                                textAlign: 'left',
                                              },
                                              content: [
                                                {
                                                  text: 'Something went wrong. Please try again by refreshing your browser. If your issue persists, please email us at ',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '',
                                                      },
                                                    },
                                                  ],
                                                },
                                                {
                                                  text: '<EMAIL>',
                                                  type: 'text',
                                                  marks: [
                                                    {
                                                      type: 'link',
                                                      attrs: {
                                                        href: 'mailto:<EMAIL>',
                                                        class: 'link',
                                                        target: '_blank',
                                                      },
                                                    },
                                                    {
                                                      type: 'textStyle',
                                                      attrs: {
                                                        color: '',
                                                      },
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                        isFullXBleed: false,
                                      },
                                      formFieldsCollection: {
                                        items: [
                                          {
                                            __typename: 'FormField',
                                            altusLabel: 'I am interested in...',
                                            altusFieldName: 'productinterest',
                                            altusFieldType: 'Dropdown',
                                            altusDataFormat: 'Text',
                                            altusFieldValues: [
                                              'ARGUS Training',
                                              'ARGUS Training',
                                              'ARGUS Training',
                                              'ARGUS Training',
                                              'ARGUS Training',
                                            ],
                                            altusIsRequired: false,
                                            userFieldLabels: [
                                              'ARGUS Enterprise',
                                              'ARGUS Taliance',
                                              'ARGUS EstateMaster',
                                              'ARGUS Developer',
                                              'ARGUS Voyanta',
                                            ],
                                            altusDependentsCollection: {
                                              items: [],
                                            },
                                          },
                                          {
                                            sys: {
                                              id: '61Lz8BSHTeXzt0NrtKunSV',
                                            },
                                            __typename: 'ComponentLayoutRow',
                                            internalName:
                                              'MSA - Forms - First Name & Last Name',
                                            isFullXBleed: false,
                                            layoutColumnCollection: {
                                              items: [
                                                {
                                                  sys: {
                                                    id: '7B8RJRObuw12p2cFYAuMoo',
                                                  },
                                                  __typename:
                                                    'ComponentLayoutColumn',
                                                  layoutItemCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        sys: {
                                                          id: '7dyJvlLQRpnDV4aSM9HGb5',
                                                        },
                                                        formField: {
                                                          __typename:
                                                            'FormField',
                                                          altusLabel:
                                                            'First name',
                                                          altusFieldName:
                                                            'firstname',
                                                          altusFieldType:
                                                            'Text',
                                                          altusDataFormat:
                                                            'Text',
                                                          altusIsRequired: true,
                                                          validationErrorMessage:
                                                            'This is a required field',
                                                          placeholderText:
                                                            'John',
                                                          altusDependentsCollection:
                                                            {
                                                              items: [],
                                                            },
                                                          locale: 'en-CA',
                                                          fullUrl: '',
                                                        },
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  sys: {
                                                    id: '6OHyIlZksdcGgwcPbbFk6c',
                                                  },
                                                  __typename:
                                                    'ComponentLayoutColumn',
                                                  layoutItemCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        sys: {
                                                          id: '7ISO7q8d655r3XKopb3yh0',
                                                        },
                                                        formField: {
                                                          __typename:
                                                            'FormField',
                                                          altusLabel:
                                                            'Last name',
                                                          altusFieldName:
                                                            'lastname',
                                                          altusFieldType:
                                                            'Text',
                                                          altusDataFormat:
                                                            'Text',
                                                          altusIsRequired: true,
                                                          validationErrorMessage:
                                                            'This is a required field',
                                                          placeholderText:
                                                            'Smith',
                                                          altusDependentsCollection:
                                                            {
                                                              items: [],
                                                            },
                                                          locale: 'en-CA',
                                                          fullUrl: '',
                                                        },
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          },
                                          {
                                            sys: {
                                              id: '4HO6nIdIiLAK1X2OGdn88x',
                                            },
                                            __typename: 'ComponentLayoutRow',
                                            internalName:
                                              'MSA - Forms - Email & Phone',
                                            isFullXBleed: false,
                                            layoutColumnCollection: {
                                              items: [
                                                {
                                                  sys: {
                                                    id: '16mPPT9zVmISi9P1U1mBCu',
                                                  },
                                                  __typename:
                                                    'ComponentLayoutColumn',
                                                  layoutItemCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        sys: {
                                                          id: '4XTZbRwZPAjBWg13lZ8Y7U',
                                                        },
                                                        formField: {
                                                          __typename:
                                                            'FormField',
                                                          altusLabel: 'Email',
                                                          altusFieldName:
                                                            'Email',
                                                          altusFieldType:
                                                            'Email',
                                                          altusDataFormat:
                                                            'Email',
                                                          altusIsRequired: true,
                                                          validationErrorMessage:
                                                            'This is a required field',
                                                          placeholderText:
                                                            '<EMAIL>',
                                                          isAltusEmailAllowed:
                                                            true,
                                                          isGenericEmailAllowed:
                                                            true,
                                                          altusDependentsCollection:
                                                            {
                                                              items: [],
                                                            },
                                                          locale: 'en-CA',
                                                          fullUrl: '',
                                                        },
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  sys: {
                                                    id: '2RpnORYFPn8x4xWfHQk4HF',
                                                  },
                                                  __typename:
                                                    'ComponentLayoutColumn',
                                                  layoutItemCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        sys: {
                                                          id: '6wW1MIDLgu1W8IYzIZcVqn',
                                                        },
                                                        formField: {
                                                          __typename:
                                                            'FormField',
                                                          altusLabel: 'Phone',
                                                          altusFieldName:
                                                            'phone',
                                                          altusFieldType:
                                                            'Text',
                                                          altusDataFormat:
                                                            'Phone',
                                                          altusIsRequired: true,
                                                          validationErrorMessage:
                                                            'This field is required',
                                                          placeholderText:
                                                            '**************',
                                                          altusDependentsCollection:
                                                            {
                                                              items: [],
                                                            },
                                                          locale: 'en-CA',
                                                          fullUrl: '',
                                                        },
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          },
                                          {
                                            sys: {
                                              id: '3apRGqhnE8cIeSBYAjBM7',
                                            },
                                            __typename: 'ComponentLayoutRow',
                                            internalName:
                                              'MSA - Forms - Job Title & Company',
                                            isFullXBleed: false,
                                            layoutColumnCollection: {
                                              items: [
                                                {
                                                  sys: {
                                                    id: '4zb1oseOGcpNvaXCknDFdl',
                                                  },
                                                  __typename:
                                                    'ComponentLayoutColumn',
                                                  layoutItemCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        sys: {
                                                          id: '1gmHh4HnWeI6PjFohXcBtj',
                                                        },
                                                        formField: {
                                                          __typename:
                                                            'FormField',
                                                          altusLabel:
                                                            'Job Title',
                                                          altusFieldName:
                                                            'jobtitle',
                                                          altusFieldType:
                                                            'Text',
                                                          altusDataFormat:
                                                            'Text',
                                                          altusIsRequired: true,
                                                          validationErrorMessage:
                                                            'This is a required field',
                                                          placeholderText:
                                                            'Owner',
                                                          altusDependentsCollection:
                                                            {
                                                              items: [],
                                                            },
                                                          locale: 'en-CA',
                                                          fullUrl: '',
                                                        },
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  sys: {
                                                    id: '1PJnnG3WIrKTCbsY0sWaqW',
                                                  },
                                                  __typename:
                                                    'ComponentLayoutColumn',
                                                  layoutItemCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        sys: {
                                                          id: '29FTOwfK0VrCbcybfVhAya',
                                                        },
                                                        formField: {
                                                          __typename:
                                                            'FormField',
                                                          altusLabel: 'Company',
                                                          altusFieldName:
                                                            'company',
                                                          altusFieldType:
                                                            'Text',
                                                          altusDataFormat:
                                                            'Text',
                                                          altusIsRequired: true,
                                                          validationErrorMessage:
                                                            'This field is required',
                                                          placeholderText:
                                                            'Smith Development',
                                                          altusDependentsCollection:
                                                            {
                                                              items: [],
                                                            },
                                                          locale: 'en-CA',
                                                          fullUrl: '',
                                                        },
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            altusLabel: 'Country',
                                            altusFieldName: 'country',
                                            altusFieldType: 'Country',
                                            altusDataFormat: 'Text',
                                            altusIsRequired: true,
                                            validationErrorMessage:
                                              'This field is required',
                                            altusDependentsCollection: {
                                              items: [
                                                {
                                                  __typename: 'FormDependent',
                                                  dependentValue:
                                                    'United States',
                                                  dependentFieldsCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        altusLabel: 'State',
                                                        altusFieldName: 'state',
                                                        altusFieldType:
                                                          'Dropdown',
                                                        altusDataFormat: 'Text',
                                                        altusFieldValues: [
                                                          'Alabama',
                                                          'Alaska',
                                                          'Arizona',
                                                          'Arkansas',
                                                          'California',
                                                          'Colorado',
                                                          'Connecticut',
                                                          'Delaware',
                                                          'District of Columbia',
                                                          'Florida',
                                                          'Georgia',
                                                          'Hawaii',
                                                          'Idaho',
                                                          'Illinois',
                                                          'Indiana',
                                                          'Iowa',
                                                          'Kansas',
                                                          'Kentucky',
                                                          'Louisiana',
                                                          'Maine',
                                                          'Maryland',
                                                          'Massachusetts',
                                                          'Michigan',
                                                          'Minnesota',
                                                          'Mississippi',
                                                          'Missouri',
                                                          'Montana',
                                                          'Nebraska',
                                                          'Nevada',
                                                          'New Hampshire',
                                                          'New Jersey',
                                                          'New Mexico',
                                                          'New York',
                                                          'North Carolina',
                                                          'North Dakota',
                                                          'Ohio',
                                                          'Oklahoma',
                                                          'Oregon',
                                                          'Pennsylvania',
                                                          'Rhode Island',
                                                          'South Carolina',
                                                          'South Dakota',
                                                          'Tennessee',
                                                          'Texas',
                                                          'Utah',
                                                          'Vermont',
                                                          'Virginia',
                                                          'Washington',
                                                          'West Virginia',
                                                          'Wisconsin',
                                                          'Wyoming',
                                                        ],
                                                        altusIsRequired: true,
                                                        validationErrorMessage:
                                                          'This field is required',
                                                        altusDependentsCollection:
                                                          {
                                                            items: [],
                                                          },
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  __typename: 'FormDependent',
                                                  dependentValue: 'Canada',
                                                  dependentFieldsCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        altusLabel: 'State',
                                                        altusFieldName: 'state',
                                                        altusFieldType:
                                                          'Dropdown',
                                                        altusDataFormat: 'Text',
                                                        altusFieldValues: [
                                                          'Alberta',
                                                          'British Columbia',
                                                          'Manitoba',
                                                          'New Brunswick',
                                                          'Newfoundland and Labrador',
                                                          'Northwest Territories',
                                                          'Nova Scotia',
                                                          'Nunavut',
                                                          'Ontario',
                                                          'Prince Edward Island',
                                                          'Quebec',
                                                          'Saskatchewan',
                                                          'Yukon',
                                                        ],
                                                        altusIsRequired: true,
                                                        validationErrorMessage:
                                                          'This field is required',
                                                        altusDependentsCollection:
                                                          {
                                                            items: [],
                                                          },
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  __typename: 'FormDependent',
                                                  dependentValue: 'Australia',
                                                  dependentFieldsCollection: {
                                                    items: [
                                                      {
                                                        __typename: 'FormField',
                                                        altusLabel: 'State',
                                                        altusFieldName: 'state',
                                                        altusFieldType:
                                                          'Dropdown',
                                                        altusDataFormat: 'Text',
                                                        altusFieldValues: [
                                                          'Australian Capital Territory',
                                                          'New South Wales',
                                                          'Queensland',
                                                          'Northern Territory',
                                                          'South Australia',
                                                          'Tasmania',
                                                          'Victoria',
                                                          'Western Australia',
                                                        ],
                                                        altusIsRequired: true,
                                                        validationErrorMessage:
                                                          'This field is required',
                                                        altusDependentsCollection:
                                                          {
                                                            items: [],
                                                          },
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            altusLabel:
                                              'How can we help you today?',
                                            altusFieldName: 'comments',
                                            altusFieldType: 'Text Area',
                                            altusDataFormat: 'Text',
                                            altusIsRequired: true,
                                            validationErrorMessage:
                                              'This is a required field',
                                            placeholderText:
                                              'Tell us more about your business, needs and timeline',
                                            altusDependentsCollection: {
                                              items: [],
                                            },
                                          },
                                          {
                                            __typename: 'FormField',
                                            altusLabel:
                                              'Yes! Sign me up to receive the insights newsletter',
                                            altusFieldName: 'newslettersignup',
                                            altusFieldType: 'CheckBox',
                                            altusDataFormat: 'Text',
                                            altusFieldValues: ['True'],
                                            altusIsRequired: false,
                                            userFieldLabels: ['Yes'],
                                            altusDependentsCollection: {
                                              items: [],
                                            },
                                          },
                                          {
                                            sys: {
                                              id: '6Y1iyFkmxxqZ4WhB5i62Vb',
                                            },
                                            __typename: 'LinkComponent',
                                            internalName:
                                              'AGL - Form Generic - Submit Inquiry',
                                            template: 'Submit',
                                            text: 'Submit',
                                            icon: 'RightChevron',
                                            iconPlacement: 'Suffix',
                                            isLightMode: true,
                                            isChevron2Arrow: true,
                                            openInNewTab: false,
                                          },
                                          {
                                            __typename: 'FormField',
                                            altusLabel: 'Inquiry Type - Sales',
                                            altusFieldName:
                                              'contactinquirytype',
                                            altusFieldType: 'Hidden',
                                            altusDataFormat: 'Text',
                                            altusFieldValues: ['Sales'],
                                            altusIsRequired: false,
                                            altusDependentsCollection: {
                                              items: [],
                                            },
                                          },
                                        ],
                                      },
                                      locale: 'en-CA',
                                      fullUrl: '',
                                    },
                                  },
                                  locale: 'en-CA',
                                  fullUrl: '',
                                },
                              },
                            },
                          },
                        ],
                      },
                      isFullXBleed: false,
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
            {
              sys: {
                id: '1yo4PC4MfkfI92bo2VV66A',
              },
              __typename: 'ComponentLayoutColumn',
              alignItems: 'center',
              horizontalContentAlignment: 'center',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentRichtext',
                    sys: {
                      id: 'eGM4qPVI0W9nG93Ir8Rby',
                    },
                    componentRichtext: {
                      sys: {
                        id: 'eGM4qPVI0W9nG93Ir8Rby',
                      },
                      __typename: 'ComponentRichtext',
                      content: {
                        type: 'doc',
                        content: [
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                            content: [
                              {
                                type: 'text',
                                text: 'Explore our extensive training catalogue:',
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'bulletList',
                            content: [
                              {
                                type: 'listItem',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'ARGUS software certification',
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                type: 'listItem',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'Online training',
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                type: 'listItem',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'Public training',
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                type: 'listItem',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'University certification',
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                type: 'listItem',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'Educational resources',
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                          {
                            type: 'paragraph',
                            attrs: {
                              textAlign: 'left',
                            },
                          },
                          {
                            type: 'reactBlockComponent',
                            attrs: {
                              sys: {
                                id: '5anpboiVn3BmJkISiJPOq8',
                                type: 'BlockEntry',
                                linkType: 'Entry',
                                typeName: 'linkComponent',
                                linkComponent: {
                                  sys: {
                                    id: '5anpboiVn3BmJkISiJPOq8',
                                  },
                                  __typename: 'LinkComponent',
                                  internalName:
                                    'AGL - CTA Internal - ARGUS Training - Tertiary',
                                  template: 'Tertiary',
                                  text: 'Explore our training courses',
                                  icon: 'RightChevron',
                                  iconPlacement: 'Suffix',
                                  isLightMode: true,
                                  isChevron2Arrow: true,
                                  openInNewTab: false,
                                  internalLink: {
                                    slug: 'argus/training',
                                  },
                                  locale: 'en-CA',
                                  fullUrl: '',
                                },
                              },
                            },
                          },
                        ],
                      },
                      isFullXBleed: false,
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '3JvZMIN0mzVrgRea2PshHJ',
        },
        __typename: 'CarouselComponent',
        template: 'CarouselGeneric',
        isLightMode: false,
        htmlAttr: {
          className: 'mt-4',
          id: 'customers',
        },
        heading: 'Hear what our customers say',
        subheading: 'Customers',
        timer: 5,
        carouselItemsCollection: {
          __typename: 'CarouselComponentCarouselItemsCollection',
          items: [
            {
              __typename: 'Player',
              sys: {
                id: '5fYIGjQoBjvhezQJIf0Tfe',
              },
              player: {
                __typename: 'Player',
                contentId: 'CBrnLVZZ9Zw',
                template: 'YouTube',
                internalName:
                  'AGL - Customers - ARGUS Enterprise - Paramount Group',
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'Player',
              sys: {
                id: '4FYVPdAEVr5gJB5HwdcfbX',
              },
              player: {
                __typename: 'Player',
                contentId: '8JvpJPXcOgQ',
                template: 'YouTube',
                internalName:
                  'AGL - Customers - ARGUS Enterprise - American Realty Advisors',
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'Player',
              sys: {
                id: '1sTdeMfdJAPQ3RTDAQGqCH',
              },
              player: {
                __typename: 'Player',
                contentId: 'E4Z9NSp0IQo',
                template: 'YouTube',
                internalName:
                  'AGL - Customers - ARGUS Enterprise - InvenTrust Properties',
                locale: 'en-CA',
                fullUrl: '',
              },
            },
            {
              __typename: 'Player',
              sys: {
                id: 'o39ZC6GSsIKE1wjxJ0TVq',
              },
              player: {
                __typename: 'Player',
                contentId: '_tY15pRyyX0',
                template: 'YouTube',
                internalName:
                  'AGL - Customers - ARGUS Enterprise - Marshall & Stevens',
                locale: 'en-CA',
                fullUrl: '',
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '6RqDT2f0xdWRcq9MAtWLB2',
        },
        __typename: 'ComponentLayoutRow',
        internalName: 'AGL - Solutions - ARGUS Enterprise - FAQs',
        htmlAttr: {
          className: 'bn7 py3 mt-4',
        },
        isFullXBleed: true,
        layoutColumnCollection: {
          items: [
            {
              sys: {
                id: '3uq3Lw12YjBDBD0vjS8Cc9',
              },
              __typename: 'ComponentLayoutColumn',
              horizontalContentAlignment: 'center',
              layoutItemCollection: {
                items: [
                  {
                    __typename: 'ComponentAccordion',
                    sys: {
                      id: '3DD1kjL34H64Zib5C7UhrJ',
                    },
                    componentAccordion: {
                      __typename: 'ComponentAccordion',
                      sys: {
                        id: '3DD1kjL34H64Zib5C7UhrJ',
                      },
                      heading: 'Frequently asked questions',
                      subheading: 'ARGUS Enterprise FAQs',
                      accordionItemsCollection: {
                        items: [
                          {
                            __typename: 'ComponentAccordionItem',
                            sys: {
                              id: '4dEL75v9vyb5T10AQX2Duq',
                            },
                            componentAccordionItem: {
                              sys: {
                                id: '4dEL75v9vyb5T10AQX2Duq',
                              },
                              __typename: 'ComponentAccordionItem',
                              accordionItemHeading:
                                'How much does ARGUS Enterprise cost?',
                              template: 'Generic',
                              accordionItemPlayerCollection: {
                                items: [],
                              },
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'We have several subscription plans and options available to support businesses of all sizes. Discount packages are available for multi-year subscriptions. To find the best package for your business, please contact us.',
                                      },
                                    ],
                                  },
                                ],
                              },
                              linkListCollection: {
                                items: [],
                              },
                              itemBlocksCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'ComponentAccordionItem',
                            sys: {
                              id: '5QuqHm3eenGTP31xkrkY3A',
                            },
                            componentAccordionItem: {
                              sys: {
                                id: '5QuqHm3eenGTP31xkrkY3A',
                              },
                              __typename: 'ComponentAccordionItem',
                              accordionItemHeading:
                                'Is ARGUS Enterprise cloud-based?',
                              template: 'Card',
                              accordionItemPlayerCollection: {
                                items: [],
                              },
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        text: 'Yes – ARGUS Enterprise is a cloud-based software solution and ',
                                        type: 'text',
                                      },
                                      {
                                        text: 'currently supports API integrations with other property management and accounting systems, in-house spreadsheets and other solutions.',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'textStyle',
                                            attrs: {
                                              color: 'rgb(34, 34, 34)',
                                            },
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              linkListCollection: {
                                items: [],
                              },
                              itemBlocksCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'ComponentAccordionItem',
                            sys: {
                              id: '6m8AJMv69ALhCs5SmjNEEF',
                            },
                            componentAccordionItem: {
                              sys: {
                                id: '6m8AJMv69ALhCs5SmjNEEF',
                              },
                              __typename: 'ComponentAccordionItem',
                              accordionItemHeading:
                                'Did ARGUS Enterprise replace Valuation DCF, ValCap and DYNA Asset Management? ',
                              template: 'Generic',
                              accordionItemPlayerCollection: {
                                items: [],
                              },
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        text: 'Yes - These legacy products are no longer sold or currently supported, and have been succeeded by ARGUS Enterprise. Our R&D team has worked very hard to bring over all the functionality from our legacy software, combining them all into one complete solution.',
                                        type: 'text',
                                      },
                                    ],
                                  },
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                  },
                                  {
                                    type: 'reactBlockComponent',
                                    attrs: {
                                      sys: {
                                        id: 'o39ZC6GSsIKE1wjxJ0TVq',
                                        type: 'BlockEntry',
                                        linkType: 'Entry',
                                        typeName: 'player',
                                        player: {
                                          __typename: 'Player',
                                          contentId: '_tY15pRyyX0',
                                          template: 'YouTube',
                                          internalName:
                                            'AGL - Customers - ARGUS Enterprise - Marshall & Stevens',
                                          locale: 'en-CA',
                                          fullUrl: '',
                                        },
                                      },
                                    },
                                  },
                                ],
                              },
                              linkListCollection: {
                                items: [],
                              },
                              itemBlocksCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'ComponentAccordionItem',
                            sys: {
                              id: '3ccfnexSwvr0TXcgL9gHew',
                            },
                            componentAccordionItem: {
                              sys: {
                                id: '3ccfnexSwvr0TXcgL9gHew',
                              },
                              __typename: 'ComponentAccordionItem',
                              accordionItemHeading:
                                'How do I contact customer support?',
                              template: 'Generic',
                              accordionItemPlayerCollection: {
                                items: [],
                              },
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        text: 'For existing clients, you can contact our support team by logging into the ',
                                      },
                                      {
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'link',
                                            attrs: {
                                              href: '',
                                              target: '_self',
                                              class: 'link',
                                              entity: {
                                                sys: {
                                                  id: '48lfCcTOtXhIEeKeKcuJYI',
                                                  type: 'Link',
                                                  linkType: 'Entry',
                                                },
                                                slug: {
                                                  'de-DE': 'home',
                                                  'en-CA': 'support',
                                                  'fr-CA': 'home',
                                                },
                                              },
                                              slug: '/support/',
                                            },
                                          },
                                        ],
                                        text: 'Support Community',
                                      },
                                      {
                                        type: 'text',
                                        text: ' portal.',
                                      },
                                    ],
                                  },
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                  },
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'bold',
                                          },
                                        ],
                                        text: 'Please note you must have an active software subscription ',
                                      },
                                      {
                                        type: 'text',
                                        text: 'in order to access these resources within the Support Community portal. If you do not have an active software subscription and want to re-establish support, please contact us.',
                                      },
                                    ],
                                  },
                                ],
                              },
                              linkListCollection: {
                                items: [],
                              },
                              itemBlocksCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                          {
                            __typename: 'ComponentAccordionItem',
                            sys: {
                              id: '6Bl6ldDtWDzLzCw9qDwnga',
                            },
                            componentAccordionItem: {
                              sys: {
                                id: '6Bl6ldDtWDzLzCw9qDwnga',
                              },
                              __typename: 'ComponentAccordionItem',
                              accordionItemHeading:
                                'Where can I find the ARGUS Enterprise software guides and technical documentation?',
                              template: 'Generic',
                              accordionItemPlayerCollection: {
                                items: [],
                              },
                              content: {
                                type: 'doc',
                                content: [
                                  {
                                    type: 'paragraph',
                                    attrs: {
                                      textAlign: 'left',
                                    },
                                    content: [
                                      {
                                        text: 'Visit the ',
                                        type: 'text',
                                      },
                                      {
                                        text: 'ARGUS Enterprise downloads',
                                        type: 'text',
                                        marks: [
                                          {
                                            type: 'link',
                                            attrs: {
                                              href: '',
                                              class: 'link',
                                              entity: {
                                                sys: {
                                                  id: '51l4iIF110TM5qfP6Slyp6',
                                                  type: 'Link',
                                                  linkType: 'Entry',
                                                },
                                                slug: {
                                                  'de-DE': 'home',
                                                  'en-CA':
                                                    'argus/downloads/argus-enterprise',
                                                  'fr-CA': 'home',
                                                },
                                              },
                                              target: '_self',
                                              slug: '/argus/downloads/argus-enterprise/',
                                            },
                                          },
                                        ],
                                      },
                                      {
                                        text: ' page to access the latest software versions, guides and technical documentation.',
                                        type: 'text',
                                      },
                                    ],
                                  },
                                ],
                              },
                              linkListCollection: {
                                items: [],
                              },
                              itemBlocksCollection: {
                                items: [],
                              },
                              locale: 'en-CA',
                              fullUrl: '',
                            },
                          },
                        ],
                      },
                      locale: 'en-CA',
                      fullUrl: '',
                    },
                  },
                ],
              },
            },
          ],
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
      {
        sys: {
          id: '4ZQBQYKgoKKdYVA2KmpqcM',
        },
        __typename: 'ComponentDynamicTagging',
        htmlAttr: {
          id: 'resources',
        },
        htmlAttributes: {
          id: 'resources',
        },
        isLightMode: true,
        template: 'DynamicWidgetInsights',
        heading: 'Latest insights',
        limit: 6,
        condition: 'AND',
        orderBy: 'publishDate_DESC',
        subHeading: 'Resources',
        tags: [
          'Domain: AltusGroup.com',
          'AFS: Insights',
          'Expertise: Valuations',
        ],
        button: {
          sys: {
            id: '7Mj4mpLIzJ4F14mp5j0Bp9',
          },
          __typename: 'LinkComponent',
          internalName: 'AGL - CTA Internal - Insights View All - Primary',
          template: 'Primary',
          text: 'View all insights',
          icon: 'RightChevron',
          isLightMode: true,
          isChevron2Arrow: true,
          openInNewTab: false,
          internalLink: {
            slug: 'insights/all',
          },
        },
        locale: 'en-CA',
        fullUrl: 'solutions/argus-enterprise',
      },
    ],
  },
  pageThumbnail: {
    url: 'https://images.ctfassets.net/8jgyidtgyr4v/5afrwEreeMVzco7vzEqD74/b6595917424ee3f06ad369594842cf4b/Solutions-ARGUSEnterprise-Hero-900x650.png',
  },
}
