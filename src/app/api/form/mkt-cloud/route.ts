import { NextResponse } from "next/server";
import { mktCloudAllowedOrigins } from "../../../../globals/utils";

export async function POST(request: Request) {
  const origin = request.headers.get('origin') || '';

  if (!mktCloudAllowedOrigins.includes(origin)) {
    return NextResponse.json({ isSuccess: false, error: "Form submission not allowed from domain " + origin }, { status: 403 })
  }

  const reqBody = await request.json()
  const reqHeaders = new Headers();
  reqHeaders.append("Content-Type", "application/json");
  reqHeaders.append("authorisation", "3Z4AG4Qnlgek:eY?pCkKvZ@<tbeVI`d6N@h7a6ny8.8-?;E?5£");
  console.log("reqBody", reqBody);
  const sfmcUrl = reqBody.sfmcUrl
  if (!sfmcUrl) {
    return NextResponse.json({ isSuccess: false, error: "Missing sfmcUrl" }, { status: 500 })
  }
  const requestOptions = {
    method: 'POST',
    headers: reqHeaders,
    body: JSON.stringify(reqBody)
  };

  try {
    const response = await fetch(sfmcUrl, requestOptions)
      .then(response => response.text())
      .then(result => result)
    return NextResponse.json({ isSuccess: true, response: response })
  } catch (error) {
    console.error('Error: could not get response of form submission: ', error)
    return NextResponse.json({ isSuccess: false, error: error }, { status: 500 })
  }
}