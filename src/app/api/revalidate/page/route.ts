import { revalidatePath } from 'next/cache'
import { NextRequest } from 'next/server'

export async function POST(request: NextRequest) {
  const req = await request.json()
  let slugs = req?.slugs

  console.log('POST: req body', req)

  if (slugs?.length > 0) {
    slugs.forEach((slug: string) => {
      revalidatePath(slug)
    })
    return Response.json({ revalidated: true, now: Date.now(), req })
  }

  return Response.json({
    revalidated: false,
    now: Date.now(),
    message: 'Missing slug to revalidate',
  })
}
