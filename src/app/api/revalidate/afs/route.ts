import { NextRequest } from 'next/server'

type cfTag = {
  sys: {
    id: string
  }
}

export async function POST(request: NextRequest) {
  const req = await request.json()

  const domainTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('domain')
  )?.sys?.id
  const afsTag = req.metadata.tags.find((tag: cfTag) =>
    tag.sys.id.startsWith('afs')
  )?.sys?.id
  console.log('POST: revalidate afs', domainTag, afsTag)

  if (!afsTag) {
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Missing afs tag to revalidate',
    })
  }

  const domainVercelUrl = getVercelUrl(domainTag)

  if (domainVercelUrl) {
    const afsSlug = getAFSslugFromTag(afsTag, domainTag)
    if (afsSlug) {
      const revalidateResponse = await fetch(
        `${domainVercelUrl}/api/revalidate/page/`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ slugs: [afsSlug] }),
        }
      ).then((res) => res.json())

      return Response.json(revalidateResponse)
    } else {
      return Response.json({
        revalidated: false,
        now: Date.now(),
        message: 'AFS tag not found or not supported',
      })
    }
  } else {
    return Response.json({
      revalidated: false,
      now: Date.now(),
      message: 'Domain not found or not supported',
    })
  }
}

function getAFSslugFromTag(afsTag: string, domainTag: string): string | null {
  switch (domainTag) {
    case 'domainAltusGroupCom':
      const tagToslug: Record<string, string> = {
        afsInsights: '/insights/all',
        afsPressRelease: '/press-releases',
        afsEvents: '/events',
      }
      return tagToslug[afsTag]
    case 'domainReonomyCom':
    case 'domainFinanceActiveCom':
      return '/resources'
    default:
      return null
  }
}

function getVercelUrl(domainTag: string) {
  const domainToUrl: Record<string, string> = {
    domainAltusGroupCom: 'https://msa-agl-v3w-git-main-altus.vercel.app',
    domainReonomyCom: 'https://msa-reo-v3w-git-main-altus.vercel.app',
    domainFinanceActiveCom: 'https://msa-fia-v3w-git-main-altus.vercel.app',
  }
  return domainToUrl[domainTag]
}
