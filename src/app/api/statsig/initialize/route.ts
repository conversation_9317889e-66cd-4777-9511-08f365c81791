import { StatsigUser } from 'statsig-node'
import { cl } from '../../../../globals/utils'
import { getStatsigValues } from '../../../../utils/statsig-server'

export async function POST(request: Request): Promise<Response> {
  cl('API called Post')
  const body = (await request.json()) as { user: StatsigUser }
  const values = await getStatsigValues(body.user)
  cl('API called')

  return new Response(values)
}
export async function GET() {
  cl('API called')
  return Response.json({
    res: {
      message: 'API statsig initalize',
      status: 200,
    },
  })
}
