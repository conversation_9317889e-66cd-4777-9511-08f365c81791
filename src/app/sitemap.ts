import { MetadataRoute } from 'next'
import {
  BUISNESS_DOMAINS_LOCALE_NAVIGATION,
  TAG_TO_URL,
} from '../globals/utils'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const domain = process.env.NEXT_PUBLIC_DOMAIN
  let locales = BUISNESS_DOMAINS_LOCALE_NAVIGATION[domain]
  if (domain === 'domainReonomyCom' || domain === 'domainAltusGroupCom') {
    locales = ['en-CA']
  }
  let allPages = []

  // for (const locale of locales) {
  //   const { data } = await fetchGraphQL(
  //     pageUrlQueryByLocale(locale, true),
  //     true
  //   )
  //   const pages = data.pageCollection.items

  //   allPages = [...allPages, ...pages]
  // }
  // return allPages.map((page) => ({
  //   url: encodeURI(
  //     `${TAG_TO_URL[process.env.NEXT_PUBLIC_DOMAIN]}${page.slug === '/' ? '' : '/' + page.slug}/`
  //   ),
  //   lastModified: page?.publishDate
  //     ? new Date(page?.publishDate).toISOString()
  //     : page?.sys?.publishedAt,
  // }))
  return [
    {
      url: encodeURI(`${TAG_TO_URL[process.env.NEXT_PUBLIC_DOMAIN]}/`),
      lastModified: new Date().toISOString(),
    }]
}
