import { SpeedInsights } from '@vercel/speed-insights/next'
import type { Metadata } from 'next'
import '../brands/globalStyles.scss'
import BackToTopButton from '../components/BackToTopButton'
import { BUISNESS_DOMAINS_FAVICONS, getBranch } from '../globals/utils'
import FormDataProvder from '../providers/FormDataProvder'
import { ReduxProvider } from '../redux/provider'

let domain: string = process.env.NEXT_PUBLIC_DOMAIN
let faviconData: any = BUISNESS_DOMAINS_FAVICONS[domain]

export const metadata: Metadata = {
  title: 'AWP V3 Components',
  description:
    '17 Studio UI is the secret realm of creators developing awesome UI components for 17 studio.',
  // icons: {
  //   icon: [{ url: url }],
  //   shortcut: [url],
  //   apple: [
  //     { url: url },
  //     {
  //       url: url,
  //       sizes: '180x180',
  //       type: 'image/png',
  //     },
  //   ],
  //   other: [
  //     {
  //       rel: 'apple-touch-icon-precomposed',
  //       url: url,
  //     },
  //   ],
  // },
}

const vercelEnvData = getBranch()

const gtmMapping = {
  domainReonomyCom:
    vercelEnvData.branch === 'staging' ? 'GTM-NXH8P493' : 'GTM-PRTKFPC',
  domainAltusGroupCom: 'GTM-5NDMLFK',
  domainFinanceActiveCom: 'GTM-N69FBRN',
  domainVerifinoCom: 'GTM-TDPCZ9NM',
  domainOne11Com: 'GTM-PKW8KQT',
}

const measurementIdMapping = {
  domainReonomyCom: 'G-29Y7MRL3G0',
}
const companySeoJson = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Altus Group',
  url: 'https://altusgroup.com/',
  logo: 'https://images.ctfassets.net/8jgyidtgyr4v/5be0JQK347L26I07IDA1WW/39d075aa0afa3c3ab3f584c0b1a00123/Altus-Group-logo.svg',
  sameAs: [
    'https://www.linkedin.com/company/altus-group/',
    'https://www.youtube.com/channel/UCWxU1ZXEOkPkj4INFvHIaYw',
    'https://twitter.com/altusgroup',
    'https://www.facebook.com/AltusGroup/',
  ],
  description:
    'Altus Group provides the global CRE industry with asset intelligence driven by our data-powered ARGUS technology, analytics and deep industry expertise.',
  email: '<EMAIL> ',
  telephone: '(*************',
  address: {
    '@type': 'PostalAddress',
    streetAddress: '33 Yonge Street, Suite #500',
    addressLocality: 'Toronto',
    addressCountry: 'CA',
    addressRegion: 'Ontario',
    postalCode: 'M5E 1G4',
  },
  founder: 'Not applicable',
  foundingDate: '2005',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const gtmId = gtmMapping[domain || '']
  const measurementId = measurementIdMapping[domain || '']

  const TheAnalytics =
    vercelEnvData && vercelEnvData?.branch === 'main' ? (
      <>
        {gtmId && (
          <script
            dangerouslySetInnerHTML={{
              __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});let f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${gtmId}');`,
            }}
          ></script>
        )}
        {/* {measurementId && (
                    <script
                        dangerouslySetInnerHTML={{
                            __html: `window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', '${measurementId}');`,
                        }}
                    ></script>
                )} */}
        {
          <>
            <script
              dangerouslySetInnerHTML={{
                __html: ` (function(w,d,t,r,u)
            {
              let f,n,i;
              w[u]=w[u]||[],f=function()
              {
                let o={ti:"97098596", enableAutoSpaTracking: true}; 
                o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad") 
              },
              n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function()
              {
                let s=this.readyState;
                s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)
              },
              i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)
            })
            (window,document,"script","//bat.bing.com/bat.js","uetq");`,
              }}
            ></script>
          </>
        }
      </>
    ) : vercelEnvData.branch === 'staging' ? (
      <>
        {gtmId && (
          <script
            dangerouslySetInnerHTML={{
              __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});let f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${gtmId}');`,
            }}
          ></script>
        )}
      </>
    ) : (
      <></>
    )

  let backToTopBtn = <BackToTopButton />

  if (process.env.NEXT_PUBLIC_DOMAIN === 'domainReonomyCom') {
    backToTopBtn = <></>
  }

  return (
    <html lang='en'>
      <head>
        {TheAnalytics}
        <link
          rel='apple-touch-icon'
          sizes='180x180'
          href={faviconData?.apple}
        />
        <link
          rel='icon'
          type='image/png'
          sizes='32x32'
          href={faviconData?.favicon32}
        />
        <link
          rel='icon'
          type='image/png'
          sizes='16x16'
          href={faviconData?.favicon16}
        />
        <link rel='manifest' href={faviconData?.site} />
        <link rel='mask-icon' href={faviconData?.safari} color='#5bbad5' />
        <meta name='msapplication-TileColor' content='#da532c' />
        <meta name='theme-color' content='#ffffff'></meta>
      </head>
      <body id={domain}>
        <noscript
          dangerouslySetInnerHTML={{
            __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${gtmId}" height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
          }}
        ></noscript>
        {domain === 'domainAltusGroupCom' && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(companySeoJson),
            }}
          ></script>
        )}

        <ReduxProvider>
          <FormDataProvder>{children}</FormDataProvder>
        </ReduxProvider>

        {backToTopBtn}
        {vercelEnvData?.branch === 'main' && <SpeedInsights />}
      </body>
    </html>
  )
}
