import { Metadata } from 'next'
import PageRoot from '../../utils/PageRoot'
import { metadataProcessor } from '../../utils/pageUtils'

// export const dynamicParams = false // true | false,
// export const revalidate = 3600 // seconds

// MetaData
export async function generateMetadata({
  params,
  // searchParams,
}: unknown): Promise<Metadata> {
  return await metadataProcessor(params)
}

async function Page({
  params,
  // searchParams,
}: {
  params: { slug: Array<string> }
  // searchParams: object
}) {
  return <PageRoot params={params} />
}

// Export the component as a Next.js server component
export default Page

// export async function generateStaticParams() {
//   return await staticParamsProcessor()
// }
