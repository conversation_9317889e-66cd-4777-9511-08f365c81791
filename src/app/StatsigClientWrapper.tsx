'use client'

import useCookie from '../hooks/useCookie'
import { UID_COOKIE } from '../lib/constant'
import BootStrappedStatsigClient from './BootStrappedStatsigClient'

const StatsigClientWrapper = ({ children, cookies: userID }) => {
  const { cookieValue, isLoading } = useCookie(UID_COOKIE)
  // !isLoading && console.log(cookieValue, "coValue : Filter Identifier")

  return isLoading ? (
    'Loading....'
  ) : (
    <BootStrappedStatsigClient user={{ userID: cookieValue }}>
      {children}
    </BootStrappedStatsigClient>
  )
}

export default StatsigClientWrapper
