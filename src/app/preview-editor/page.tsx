'use client'
import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import Popup from '../../components/Containers/Popup'
import PageRouter from '../../lib/componentsRouter/PageRouter'
import { FormRenderer } from '../../lib/previewEditor/FormRender'
import {
  resetEditingForm,
  updateComponentProp,
  updateEditingForm,
} from '../../redux/slices/previewSlice'
import CustomSidebar from '../../components/CustomSidebar'
function PreviewEditor() {
  const dispatch = useDispatch()
  const pageProps = useSelector((state) => state.preview.page)
  const editingFormProps = useSelector((state) => state.preview.editingForm)

  console.log("pagePRops",{ pageProps });


  // // Example: Update the button text
  // const updateButtonText = (value) => {
  //   dispatch(
  //     updateComponentProp({
  //       componentId: '7dyJvlLQRpnDV4aSM9HGb5', // Button's ID
  //       propName: 'altusLabel', // Property name
  //       value: value, // New value
  //     })
  //   )
  // }

  // function applyOverlay() {
  //   dispatch(
  //     updateComponentProp({
  //       componentId: '45S7q8pXs7fXDDf861zB1P',
  //       propName: 'isHeroOverlay',
  //       value: true,
  //     })
  //   )
  // }

  // function addNewButton() {
  //   dispatch(
  //     updateComponentProp({
  //       componentId: '36bOmavUYVrKD0HnuKRvke',
  //       propName: 'formFieldsCollection',
  //       value: {
  //         sys: {
  //           id: 'frsgrgrtg986798ds6fg',
  //         },
  //         __typename: 'LinkComponent',
  //         internalName: 'AGL - CTA Form - Press Release Signup - Primary',
  //         template: 'Primary',
  //         htmlAttr: null,
  //         text: 'Hurray new button',
  //         externalLink: null,
  //         asset: null,
  //         sectionId: null,
  //         fieldMapping: null,
  //         icon: 'RightChevron',
  //         iconPlacement: 'Suffix',
  //         isLightMode: true,
  //         isChevron2Arrow: true,
  //         openInNewTab: false,
  //         internalLink: null,
  //         image: null,
  //       },
  //       isAppendChild: true,
  //     })
  //   )
  // }

  useEffect(() => {
    function editingClickhandler(e) {
      const target = e.target?.closest('[component-id][component-type]')
      console.log({ target });

      if (target) {
        e.preventDefault() // Prevent default action (optional)
        const componentId = target.getAttribute('component-id')
        const componentType = target.getAttribute('component-type')
        console.log(`Editing component: ${componentType} (ID: ${componentId})`)
        dispatch(
          updateEditingForm({
            componentId,
            componentType,
          })
        )
        // Trigger your edit logic here, e.g., open an edit modal
      }
    }
    document.addEventListener('click', editingClickhandler)
    return () => {
      document.removeEventListener('click', editingClickhandler)
    }
  }, [])

  return (
    <div>
      <div>
        <PageRouter pageData={pageProps} />
      </div>
      <Popup isMaxedOut={false} />
      {/* <div style={{ display: 'flex' }}> */}

      <CustomSidebar isOpen={editingFormProps?.activerId} onCancel={() => {
        dispatch(resetEditingForm())
      }}>
        <div>
          <pre>{JSON.stringify(editingFormProps, null, 2)}</pre>
        </div>
        <FormRenderer
          fields={editingFormProps?.fields}
          onSubmit={(val) => {
            if (Object?.keys(val)?.length) {
              Object.entries(val)?.forEach(([key, val]) => {
                dispatch(
                  updateComponentProp({
                    componentId: editingFormProps?.activerId, // Button's ID
                    propName: key, // Property name
                    value: val, // New value
                  })
                )
              })
            }
          }}
        />
        {/* </div> */}
      </CustomSidebar>

      {/* </div> */}
    </div >
  )
}

export default PreviewEditor
