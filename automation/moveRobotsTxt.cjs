const fs = require('fs');
const dotenv = require('dotenv');

if (process.env.NODE_ENV !== 'production') {
    dotenv.config({ path: '.env.local' });
} else {
    dotenv.config({ path: '.env' });
}

function getFilePathFromDomain() {
    const domain = process.env.NEXT_PUBLIC_DOMAIN;
    const folderName = {
        domainAltusGroupCom: 'altus',
        domainFinanceActiveCom: 'financeActive',
        domainVerifinoCom: 'verifino',
        domainOne11Com: 'one11',
        domainReonomyCom: 'reonomy',
    }
    return `./src/brands/${folderName[domain]}/robots.txt`;
}

function moveRobotsTxt() {

    // Define source and destination paths
    const sourcePath = getFilePathFromDomain();
    const destinationPath = './public/robots.txt';

    // Copy the file
    fs.copyFile(sourcePath, destinationPath, (err) => {
        if (err) {
            console.log('Error occurred while copying file Robots.txt: ', err);
            throw err;
        } else {
            console.log('Robots.txt file copied successfully!');
        }
    });

}

moveRobotsTxt();